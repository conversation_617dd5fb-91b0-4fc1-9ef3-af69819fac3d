<template>
	<!--
		💊 产品详情页面模板
		这是显示医疗产品详细信息的页面，包含产品介绍、价格、购买等功能
		支持暗色主题、多语言、字体大小调整等功能
	-->

	<!--
		📄 产品详情页面容器
		- class="product-detail-container": 基础页面样式
		- :class: 动态样式类，包括：
		  * dark-theme: 暗色主题样式（当isDarkMode为true时）
		  * fontSizeClass: 字体大小样式类
		  * fontClass: 字体类型样式类（支持维吾尔文等）
		- :style: 动态样式，主要用于RTL布局
		- :key: 强制重新渲染的键，当主题、字体、语言变化时重新渲染
	-->
	<view class="product-detail-container" :class="[{ 'dark-theme': isDarkMode }, fontSizeClass, fontClass]" :style="pageStyle" :key="`${themeUpdateKey}-${fontSizeUpdateKey}-${i18nUpdateKey}`">
		<!--
			📋 产品信息卡片
			显示产品的基本信息，包括名称、描述、制造商、医生等
		-->
		<view class="product-card">
			<!-- 🔸 顶部虚线边框装饰 -->
			<view class="dotted-border-top"></view>

			<!--
				📊 产品主要信息区域
				左右布局：左侧显示文字信息，右侧显示产品图片
			-->
			<view class="product-main-info">
				<!-- 📝 产品左侧信息 -->
				<view class="product-left">
					<!--
						产品标题
						processedCurrentProduct.name: 经过处理的产品名称
					-->
					<text class="product-title">{{ processedCurrentProduct.name }}</text>

					<!--
						产品副标题/描述
						processedCurrentProduct.description: 产品的简短描述
					-->
					<text class="product-subtitle">{{ processedCurrentProduct.description }}</text>

					<!--
						制造商信息
						$t('productDetails.manufacturer'): 从语言包获取"制造商"文字
						processedCurrentProduct.manufacturer: 制造商名称
					-->
					<text class="manufacturer">{{ $t('productDetails.manufacturer') }}{{ processedCurrentProduct.manufacturer }}</text>

					<!-- 👨‍⚕️ 医生信息区域 -->
					<view class="doctor-info">
						<!--
							医生标签
							$t('productDetails.doctor'): 从语言包获取"医生"文字
						-->
						<text class="doctor-label">{{ $t('productDetails.doctor') }}</text>

						<!--
							医生姓名
							processedCurrentProduct.doctor: 推荐该产品的医生姓名
						-->
						<text class="doctor-name">{{ processedCurrentProduct.doctor }}</text>
					</view>

					<!-- 🆕 产品规格信息 -->
					<view class="product-specs" v-if="processedCurrentProduct.specifications && processedCurrentProduct.specifications !== '规格待定'">
						<text class="spec-label">规格:</text>
						<text class="spec-value">{{ processedCurrentProduct.specifications }}</text>
					</view>

					<!-- 🆕 库存信息 -->
					<view class="inventory-info">
						<text class="inventory-label">库存:</text>
						<text class="inventory-value" :class="{ 'low-stock': processedCurrentProduct.inventory < 50 }">
							{{ processedCurrentProduct.inventory }}
						</text>
						<text class="inventory-unit">件</text>
					</view>

					<!-- 🆕 销量信息 -->
					<view class="sales-info" v-if="processedCurrentProduct.salesCount > 0">
						<text class="sales-label">已售:</text>
						<text class="sales-value">{{ processedCurrentProduct.salesCount }}</text>
						<text class="sales-unit">件</text>
					</view>
				</view>

				<!-- 🖼️ 产品右侧图片 -->
				<view class="product-right">
					<!-- 产品图片容器 -->
					<view class="product-image-container">
						<!--
							产品图片
							:src: 产品图片路径
							mode="aspectFit": 图片适应模式，保持宽高比
						-->
						<image :src="processedCurrentProduct.image" class="product-detail-image" mode="aspectFit"></image>
					</view>
				</view>
			</view>

			<!-- 🔸 底部虚线边框装饰 -->
			<view class="dotted-border-bottom"></view>
		</view>

		<!--
			💰 价格和预约信息区域
			显示产品的价格信息
		-->
		<view class="price-section">
			<!-- 💵 价格信息 -->
			<view class="price-info">
				<!--
					价格标签
					$t('productDetails.price'): 从语言包获取"价格"文字
				-->
				<text class="price-label">{{ $t('productDetails.price') }}</text>

				<!-- 人民币符号 -->
				<text class="price-symbol">¥</text>

				<!--
					当前价格
					processedCurrentProduct.price: 产品的当前售价
				-->
				<text class="price-value">{{ processedCurrentProduct.price }}</text>

				<!--
					原价（划线价格）
					processedCurrentProduct.originalPrice: 产品的原价
				-->
				<text class="original-price">¥{{ processedCurrentProduct.originalPrice }}</text>
			</view>

			<!--
				📅 预约信息（已注释）
				这部分代码被注释掉了，可能是未来的功能
			-->
			<!-- <view class="appointment-info">
				<text class="appointment-icon">🕐</text>
				<text class="appointment-text">预约时间：{{ processedCurrentProduct.appointmentTime }}</text>
			</view> -->
		</view>

		<!--
			📖 详细说明区域
			显示产品的详细描述信息
		-->

		<!-- 详细说明标题 -->
		<view class="detail_title">
			<!--
				详细说明标题文字
				$t('productDetails.detailDescription'): 从语言包获取"详细说明"文字
			-->
			<text class="detail-title">{{ $t('productDetails.detailDescription') }}</text>
		</view>

		<!-- 详细说明内容区域 -->
		<view class="detail-section">
			<!--
				详细说明内容
				processedCurrentProduct.detailDescription: 产品的详细描述文字
			-->
			<text class="detail-content">{{ processedCurrentProduct.detailDescription }}</text>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<view class="cart-btn" @tap="showCartModal">
					<fui-icon class="cart-icon" name="cart" :size="60" color="#119b57"></fui-icon>
			</view>
			<view class="buy-now-btn" @tap="showBuyModal">
				<text class="buy-now-text">{{ $t('productDetails.buyNow') }}</text>
			</view>
		</view>

		<!-- 购买确认弹出框 -->
		<view v-if="showBuyConfirm" class="modal-overlay" @tap="closeBuyModal">
			<view class="buy-modal" @tap.stop>
				<text class="modal-title">{{ $t('productDetails.buyConfirm') }}</text>

				<view class="product-info-row">
					<text class="info-label">{{ $t('productDetails.productName') }}</text>
					<text class="info-value">{{ processedCurrentProduct.name }}</text>
				</view>

				<view class="product-info-row">
					<text class="info-label">{{ $t('productDetails.unitPrice') }}</text>
					<text class="info-value">¥{{ processedCurrentProduct.price }}</text>
				</view>

				<view class="quantity-section">
					<text class="quantity-label">{{ $t('productDetails.quantity') }}</text>
					<view class="quantity-controls">
						<view class="quantity-btn" @tap="decreaseQuantity">
							<text class="quantity-btn-text">-</text>
						</view>
						<text class="quantity-display">{{ buyQuantity }}</text>
						<view class="quantity-btn" @tap="increaseQuantity">
							<text class="quantity-btn-text">+</text>
						</view>
					</view>
				</view>

				<view class="total-section">
					<text class="total-label">{{ $t('productDetails.total') }}</text>
					<text class="total-price">¥{{ (processedCurrentProduct.price * buyQuantity).toFixed(2) }}</text>
				</view>

				<text class="confirm-tip">{{ $t('productDetails.confirmTip') }}</text>

				<view class="modal-buttons">
					<view
						v-for="button in buyModalButtons"
						:key="button.key"
						:class="button.btnClass"
						@tap="button.action"
					>
						<text :class="[button.textClass, fontClass]">{{ $t(button.textKey) }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 选择数量弹出框 -->
		<view v-if="showCartQuantity" class="modal-overlay" @tap="closeCartModal">
			<view class="cart-modal" @tap.stop>
				<text class="modal-title">{{ $t('productDetails.selectQuantity') }}</text>

				<text class="product-name-modal">{{ processedCurrentProduct.name }}</text>

				<view class="quantity-controls-large">
					<view class="quantity-btn-large" @tap="decreaseCartQuantity">
						<text class="quantity-btn-text-large">-</text>
					</view>
					<text class="quantity-display-large">{{ cartQuantity }}</text>
					<view class="quantity-btn-large" @tap="increaseCartQuantity">
						<text class="quantity-btn-text-large">+</text>
					</view>
				</view>

				<text class="stock-info">{{ $t('productDetails.stock') }}200</text>

				<view class="modal-buttons">
					<view
						v-for="button in cartModalButtons"
						:key="button.key"
						:class="button.btnClass"
						@tap="button.action"
					>
						<text :class="[button.textClass, fontClass]">{{ $t(button.textKey) }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useAppStore } from '@/store/app.js'
import { useFontSizePage } from '@/utils/fontSizeMixin.js'
import { useRTLPage } from '@/utils/rtlMixin.js'
import { t } from '@/locale/index.js'

// 使用全局应用状态
const appStore = useAppStore()
const isDarkMode = computed(() => appStore.isDarkMode)
const themeUpdateKey = ref(0)

// 使用字体大小功能
const { fontSizeClass, fontSizeUpdateKey } = useFontSizePage()

// RTL布局支持
const { pageClass, pageStyle } = useRTLPage()

// 国际化支持
const currentLanguage = ref(appStore.lang)
const i18nUpdateKey = ref(0)

// 计算字体类（合并RTL类）
const fontClass = computed(() => ({
	...pageClass.value,
	'ug': appStore.isUyghur,
	[`lang-${appStore.lang}`]: true
}))

// 国际化翻译函数
const $t = (key, params = {}) => {
	return t(key, params)
}

// 监听主题变化
uni.$on('themeChanged', (data) => {
	console.log('ProductDetails页面接收到主题变化:', data.isDark ? '暗色模式' : '浅色模式')
	themeUpdateKey.value++
})

// 监听语言变化
uni.$on('languageChanged', (data) => {
	console.log('ProductDetails页面接收到语言变化:', data.lang)
	currentLanguage.value = data.lang
	i18nUpdateKey.value++
})

// 弹出框状态
const showBuyConfirm = ref(false)
const showCartQuantity = ref(false)
const buyQuantity = ref(1)
const cartQuantity = ref(1)

// 产品数据 - 使用国际化翻译键
const getProductData = () => {
	return [
		// 药品数据
		{
			id: 1,
			nameKey: 'kidneyTonic',
			price: '218.00',
			originalPrice: '268.00',
			unit: '/盒',
			image: '/static/icon/user.svg',
			category: 'medicine',
			manufacturer: '吉林敖东',
			doctor: '敖敏医生',
			appointmentTime: '周三，上午 10:00'
		},
		{
			id: 2,
			nameKey: 'ibuprofen',
			price: '12.50',
			originalPrice: '16.00',
			unit: '/盒',
			image: '/static/icon/user.svg',
			category: 'medicine',
			manufacturer: '华润三九',
			doctor: '李医生',
			appointmentTime: '周四，下午 14:00'
		},
		{
			id: 3,
			nameKey: 'licorice',
			price: '8.90',
			originalPrice: '12.00',
			unit: '/盒',
			image: '/static/icon/user.svg',
			category: 'medicine',
			manufacturer: '同仁堂',
			doctor: '王医生',
			appointmentTime: '周五，上午 09:00'
		},
		{
			id: 4,
			nameKey: 'vitaminC',
			price: '6.80',
			originalPrice: '9.00',
			unit: '/瓶',
			image: '/static/icon/user.svg',
			category: 'medicine',
			manufacturer: '修正药业',
			doctor: '赵医生',
			appointmentTime: '周六，上午 11:00'
		},
		// 保健品数据
		{
			id: 5,
			nameKey: 'calcium',
			price: '45.00',
			originalPrice: '60.00',
			unit: '/瓶',
			image: '/static/icon/user.svg',
			category: 'health',
			manufacturer: '汤臣倍健',
			doctor: '孙医生',
			appointmentTime: '周一，下午 15:00'
		},
		{
			id: 6,
			nameKey: 'protein',
			price: '128.00',
			originalPrice: '168.00',
			unit: '/罐',
			image: '/static/icon/user.svg',
			category: 'health',
			manufacturer: '安利纽崔莱',
			doctor: '刘医生',
			appointmentTime: '周二，上午 10:30'
		},
		{
			id: 7,
			nameKey: 'fishOil',
			price: '89.00',
			originalPrice: '120.00',
			unit: '/瓶',
			image: '/static/icon/user.svg',
			category: 'health',
			manufacturer: '挪威小鱼',
			doctor: '陈医生',
			appointmentTime: '周三，下午 16:00'
		}
	]
}

// 产品数据
const allProducts = ref(getProductData())

// 当前产品
const currentProduct = ref({})

// 根据产品ID获取nameKey的映射
const getNameKeyById = (productId) => {
	const keyMap = {
		1: 'kidneyTonic',
		2: 'ibuprofen',
		3: 'licorice',
		4: 'vitaminC',
		5: 'calcium',
		6: 'protein',
		7: 'fishOil'
	}
	return keyMap[productId]
}

// 🆕 计算属性：处理产品数据 - 支持真实后端数据 + 调试
const processedCurrentProduct = computed(() => {
	// 添加对语言变化的响应
	const lang = currentLanguage.value

	console.log('🔍 processedCurrentProduct 计算中...')
	console.log('📦 currentProduct.value:', currentProduct.value)
	console.log('🆔 currentProduct.value.id:', currentProduct.value.id)

	if (!currentProduct.value || !currentProduct.value.id) {
		console.log('⚠️ 产品数据为空或没有ID，返回空对象')
		return {
			name: '加载中...',
			description: '正在加载产品信息...',
			detailDescription: '正在加载详细信息...',
			price: 0,
			originalPrice: 0,
			manufacturer: '加载中...',
			doctor: '加载中...',
			image: '/static/icon/user.svg',
			category: '未分类',
			inventory: 0,
			salesCount: 0,
			specifications: '规格待定'
		}
	}

	// 🆕 对于真实后端数据，直接返回处理后的数据
	const processed = {
		...currentProduct.value,
		// 确保所有必要字段都存在
		name: currentProduct.value.name || '未知产品',
		description: currentProduct.value.description || '暂无描述',
		detailDescription: currentProduct.value.detailDescription || currentProduct.value.description || '暂无详细描述',
		price: currentProduct.value.price || 0,
		originalPrice: currentProduct.value.originalPrice || currentProduct.value.price || 0,
		manufacturer: currentProduct.value.manufacturer || '未知厂商',
		doctor: currentProduct.value.doctor || '医生',
		image: currentProduct.value.image || '/static/icon/user.svg',
		category: currentProduct.value.category || '未分类',
		inventory: currentProduct.value.inventory || 0,
		salesCount: currentProduct.value.salesCount || 0,
		specifications: currentProduct.value.specifications || '规格待定'
	}

	console.log('✅ processedCurrentProduct 处理完成:', processed)
	return processed
})

// 获取页面参数
const getProductInfo = () => {
	// 使用onLoad生命周期获取参数，这里先设置默认值
	// 实际参数会在onLoad中获取
	currentProduct.value = allProducts.value[0] // 默认显示第一个产品
}

// 🆕 使用onLoad生命周期接收页面参数 - 支持真实后端数据
onLoad((options) => {
	console.log('📦 ProductDetails页面参数:', options)

	// 优先处理从List页面传递的productData
	if (options.productData) {
		try {
			const productData = JSON.parse(decodeURIComponent(options.productData))
			console.log('✅ 接收到的真实产品数据:', productData)

			// 🆕 处理真实后端数据格式 - 确保所有字段正确映射
			const processedData = {
				id: productData.id,
				name: productData.name || '未知产品',
				description: productData.description || '暂无描述',
				detailDescription: productData.detailedDescription || productData.detailed_description || productData.description || '暂无详细描述',
				price: parseFloat(productData.price) || 0,
				originalPrice: parseFloat(productData.originalPrice || productData.original_price || productData.price) || 0,
				manufacturer: productData.manufacturer || '未知厂商',
				doctor: productData.doctorName || productData.doctor_name || '医生',
				image: productData.image || '/static/icon/user.svg',
				category: productData.category || '未分类',
				inventory: productData.inventory || productData.inventory_count || 0,
				salesCount: productData.salesCount || productData.sales_count || 0,
				specifications: productData.specification || productData.specifications || '规格待定',
				doctorId: productData.doctorId || productData.doctor_id,
				status: productData.status,
				isActive: productData.isActive || productData.is_active,
				createdAt: productData.createdAt || productData.created_at,
				updatedAt: productData.updatedAt || productData.updated_at,
				// 兼容字段
				appointmentTime: productData.appointmentTime || '工作时间: 9:00-18:00',
				unit: productData.unit || '/盒'
			}

			// 🔧 强制设置产品数据
			currentProduct.value = processedData

			console.log('🎯 处理后的产品数据:', currentProduct.value)
			console.log('🔍 产品名称:', currentProduct.value.name)
			console.log('🔍 产品描述:', currentProduct.value.description)
			console.log('🔍 产品价格:', currentProduct.value.price)
			console.log('🔍 制造商:', currentProduct.value.manufacturer)
			console.log('🔍 医生:', currentProduct.value.doctor)

		} catch (error) {
			console.error('❌ 解析产品数据失败:', error)
			console.error('❌ 原始数据:', options.productData)
			// 解析失败时设置错误提示
			currentProduct.value = {
				id: 0,
				name: '数据解析失败',
				description: '无法解析产品数据',
				detailDescription: '请返回重试',
				price: 0,
				originalPrice: 0,
				manufacturer: '未知',
				doctor: '未知',
				image: '/static/icon/user.svg'
			}
		}
	} else if (options.productId) {
		// 兼容原有的productId参数方式
		const allProductsList = [...allProducts.value]
		const product = allProductsList.find(p => p.id == options.productId)
		if (product) {
			currentProduct.value = product
			console.log('找到产品:', product.name)
		} else {
			console.error('未找到产品ID:', options.productId)
			currentProduct.value = allProducts.value[0]
		}
	} else {
		console.error('未获取到产品参数')
		// 设置错误提示
		currentProduct.value = {
			id: 0,
			name: '未获取到产品参数',
			description: '请从产品列表进入',
			detailDescription: '请返回产品列表重新选择',
			price: 0,
			originalPrice: 0,
			manufacturer: '未知',
			doctor: '未知',
			image: '/static/icon/user.svg'
		}
	}

	console.log('🎉 当前产品设置完成:', currentProduct.value.name)

	// 🆕 强制触发页面更新
	nextTick(() => {
		console.log('🔄 强制更新页面，当前产品:', currentProduct.value.name)
	})
})

// 返回上一页
const goBack = () => {
	uni.navigateBack()
}

// 显示购买确认弹出框
const showBuyModal = () => {
	showBuyConfirm.value = true
}

// 关闭购买确认弹出框
const closeBuyModal = () => {
	showBuyConfirm.value = false
}

// 显示购物车数量选择弹出框
const showCartModal = () => {
	showCartQuantity.value = true
}

// 关闭购物车数量选择弹出框
const closeCartModal = () => {
	showCartQuantity.value = false
}

// 购买确认弹出框 - 数量控制
const decreaseQuantity = () => {
	if (buyQuantity.value > 1) {
		buyQuantity.value--
	}
}

const increaseQuantity = () => {
	buyQuantity.value++
}

// 购物车弹出框 - 数量控制
const decreaseCartQuantity = () => {
	if (cartQuantity.value > 1) {
		cartQuantity.value--
	}
}

const increaseCartQuantity = () => {
	cartQuantity.value++
}

// 确认购买
const confirmBuy = () => {
	console.log('确认购买:', processedCurrentProduct.value.name, '数量:', buyQuantity.value)
	closeBuyModal()

	// 跳转到订单确认页面
	uni.navigateTo({
		url: '/pages/List/ProductDetails/ConfirmOrder/ConfirmOrder'
	})
}

// 确认添加到购物车
const confirmAddToCart = () => {
	console.log('添加到购物车:', processedCurrentProduct.value.name, '数量:', cartQuantity.value)

	// 获取当前购物车数据
	let cartItems = uni.getStorageSync('cartItems') || []

	// 检查商品是否已在购物车中
	const existingItemIndex = cartItems.findIndex(item => item.id === processedCurrentProduct.value.id)

	if (existingItemIndex !== -1) {
		// 如果商品已存在，增加数量
		cartItems[existingItemIndex].quantity += cartQuantity.value
	} else {
		// 如果商品不存在，添加新商品
		const cartItem = {
			id: processedCurrentProduct.value.id,
			name: processedCurrentProduct.value.name,
			description: processedCurrentProduct.value.manufacturer,
			price: parseFloat(processedCurrentProduct.value.price),
			originalPrice: processedCurrentProduct.value.originalPrice ? parseFloat(processedCurrentProduct.value.originalPrice) : null,
			image: processedCurrentProduct.value.image,
			quantity: cartQuantity.value,
			selected: true, // 默认选中
			addedAt: new Date().toISOString()
		}
		cartItems.push(cartItem)
	}

	// 保存到本地存储
	uni.setStorageSync('cartItems', cartItems)

	// 通知购物车页面刷新
	uni.$emit('refreshCart')

	uni.showToast({
		title: $t('productDetails.addToCartSuccess', { count: cartQuantity.value }),
		icon: 'success'
	})

	// 重置数量并关闭弹窗
	cartQuantity.value = 1
	closeCartModal()
}

// 购买弹窗按钮配置
const buyModalButtons = computed(() => [
	{
		key: 'cancel',
		btnClass: 'cancel-btn',
		textClass: 'cancel-text',
		textKey: 'productDetails.cancel',
		action: closeBuyModal
	},
	{
		key: 'confirm',
		btnClass: 'confirm-btn',
		textClass: 'confirm-text',
		textKey: 'productDetails.confirmBuy',
		action: confirmBuy
	}
])

// 购物车弹窗按钮配置
const cartModalButtons = computed(() => [
	{
		key: 'cancel',
		btnClass: 'cancel-btn',
		textClass: 'cancel-text',
		textKey: 'productDetails.cancel',
		action: closeCartModal
	},
	{
		key: 'confirm',
		btnClass: 'confirm-btn',
		textClass: 'confirm-text',
		textKey: 'productDetails.confirm',
		action: confirmAddToCart
	}
])

// 生命周期
onMounted(() => {
	getProductInfo()
})

onUnmounted(() => {
	uni.$off('themeChanged')
	uni.$off('languageChanged')
})
</script>

<style lang="scss">
	.product-detail-container {
		min-height: 100vh;
		background-color: #F1F4FA;
		padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
		padding-top: 40rpx;
	}

	/* 产品信息卡片 */
	.product-card {
		// margin-top: calc(88rpx + var(--status-bar-height, 44rpx) + 40rpx);
		margin-left: 40rpx;
		margin-right: 40rpx;
		background-color: #E8F5E8;
		border-radius: 16rpx;
		padding: 40rpx;
		position: relative;
	}

	/* 虚线边框 */
	.dotted-border-top,
	.dotted-border-bottom {
		margin: -20rpx;
		position: absolute;
		left: 40rpx;
		right: 40rpx;
		height: 2rpx;
		background-image: repeating-linear-gradient(
			to right,
			#4CAF50 0,
			#4CAF50 8rpx,
			transparent 8rpx,
			transparent 16rpx
		);
	}

	.dotted-border-top {
		top: 0;
	}

	.dotted-border-bottom {
		bottom: 0;
	}

	.product-main-info {
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
	}

	.product-left {
		flex: 1;
		margin-right: 40rpx;
	}

	.product-title {
		font-size: 48rpx;
		font-weight: 600;
		color: #333333;
		margin-bottom: 16rpx;
		display: block;
	}

	.product-subtitle {
		font-size: 28rpx;
		color: #666666;
		margin-bottom: 24rpx;
		display: block;
	}

	.manufacturer {
		font-size: 24rpx;
		color: #888888;
		margin-bottom: 20rpx;
		display: block;
	}

	.doctor-info {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.doctor-label {
		background-color: #4CAF50;
		color: #ffffff;
		font-size: 20rpx;
		padding: 8rpx 16rpx;
		border-radius: 8rpx;
	}

	.doctor-name {
		font-size: 24rpx;
		color: #4CAF50;
		font-weight: 500;
	}

	/* 🆕 产品规格样式 */
	.product-specs {
		display: flex;
		align-items: center;
		gap: 8rpx;
		margin-top: 12rpx;
	}

	.spec-label {
		font-size: 22rpx;
		color: #666666;
	}

	.spec-value {
		font-size: 22rpx;
		color: #333333;
		background-color: #f5f5f5;
		padding: 4rpx 8rpx;
		border-radius: 4rpx;
	}

	/* 🆕 库存信息样式 */
	.inventory-info {
		display: flex;
		align-items: center;
		gap: 8rpx;
		margin-top: 12rpx;
	}

	.inventory-label {
		font-size: 22rpx;
		color: #666666;
	}

	.inventory-value {
		font-size: 22rpx;
		color: #4CAF50;
		font-weight: 500;
	}

	.inventory-value.low-stock {
		color: #FF5722;
	}

	.inventory-unit {
		font-size: 20rpx;
		color: #999999;
	}

	/* 🆕 销量信息样式 */
	.sales-info {
		display: flex;
		align-items: center;
		gap: 8rpx;
		margin-top: 8rpx;
	}

	.sales-label {
		font-size: 22rpx;
		color: #666666;
	}

	.sales-value {
		font-size: 22rpx;
		color: #2196F3;
		font-weight: 500;
	}

	.sales-unit {
		font-size: 20rpx;
		color: #999999;
	}

	.product-right {
		width: 200rpx;
		height: 200rpx;
	}

	.product-image-container {
		width: 100%;
		height: 100%;
		background-color: #ffffff;
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.product-detail-image {
		width: 120rpx;
		height: 120rpx;
	}

	/* 价格和预约信息 */
	.price-section {
		margin: 60rpx 40rpx 40rpx 40rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 40rpx;
		
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.price-info {
		display: flex;
		align-items: baseline;
		margin-bottom: 24rpx;
	}

	.price-label {
		font-size: 28rpx;
		color: #333333;
		margin-right: 16rpx;
	}

	.price-symbol {
		font-size: 32rpx;
		color: #ff4444;
		margin-right: 8rpx;
	}

	.price-value {
		font-size: 48rpx;
		font-weight: 600;
		color: #ff4444;
		margin-right: 20rpx;
	}

	.original-price {
		font-size: 24rpx;
		color: #999999;
		text-decoration: line-through;
	}

	.appointment-info {
		display: flex;
		align-items: center;
		gap: 12rpx;
	}

	.appointment-icon {
		font-size: 24rpx;
	}

	.appointment-text {
		font-size: 24rpx;
		color: #666666;
	}

	/* 详细说明 */
	.detail-section {
		margin: 0 40rpx 40rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 40rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.detail-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #000;
		margin-bottom: 24rpx;
		display: block;
		margin: 0 40rpx 40rpx;
	}

	.detail-content {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.6;
		display: block;
	}

	/* 底部操作栏 */
	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #ffffff;
		padding: 20rpx 40rpx;
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		border-top: 1rpx solid #e0e0e0;
		display: flex;
		align-items: center;
		gap: 20rpx;
	}

	.cart-btn {
		width: 100rpx;
		height: 100rpx;
		border: 4rpx solid #1a9659;
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #e9f6ef;
	}

	.cart-icon {
		font-size: 32rpx;
		
	}

	.buy-now-btn {
		flex: 1;
		height: 100rpx;
		background-color: #109d58;
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.buy-now-text {
		font-size: 32rpx;
		color: #ffffff;
		font-weight: 600;
	}

	/* 暗色主题样式 */
	.dark-theme {
		background-color: #1a1a1a !important;
	}

	.dark-theme .custom-navbar {
		background-color: #2d2d2d !important;
		border-bottom-color: #404040 !important;
	}

	.dark-theme .navbar-title,
	.dark-theme .back-icon {
		color: #ffffff !important;
	}

	.dark-theme .custom-navbar {
		background-color: #2d2d2d !important;
		border-bottom-color: #404040 !important;
	}

	.dark-theme .navbar-title,
	.dark-theme .back-icon {
		color: #ffffff !important;
	}

	.dark-theme .product-card {
		background-color: #2d4a2d !important;
	}

	.dark-theme .product-title {
		color: #ffffff !important;
	}

	.dark-theme .product-subtitle,
	.dark-theme .manufacturer {
		color: #b0b0b0 !important;
	}

	.dark-theme .product-image-container {
		background-color: #404040 !important;
	}

	.dark-theme .price-section,
	.dark-theme .detail-section {
		background-color: #2d2d2d !important;
	}

	.dark-theme .price-label,
	.dark-theme .detail-title {
		color: #ffffff !important;
	}

	.dark-theme .appointment-text,
	.dark-theme .detail-content {
		color: #b0b0b0 !important;
	}

	.dark-theme .bottom-actions {
		background-color: #2d2d2d !important;
		border-top-color: #404040 !important;
	}

	.dark-theme .cart-btn {
		background-color: #2d2d2d !important;
	}

	/* 弹出框样式 */
	.modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}
	

	/* 购买确认弹出框 */
	.buy-modal {
		width: 640rpx;
		background-color: #ffffff;
		border-radius: 32rpx;
		padding: 48rpx;
		margin: 0 32rpx;
	}

	.modal-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333333;
		text-align: left;
		margin-bottom: 40rpx;
		display: block;
	}

	.product-info-row {
		display: flex;
		align-items: center;
		margin-bottom: 24rpx;
	}

	.info-label {
		font-size: 28rpx;
		color: #333333;
		width: 160rpx;
	}

	.info-value {
		font-size: 28rpx;
		color: #333333;
		flex: 1;
	}

	.quantity-section {
		display: flex;
		align-items: center;
		margin-bottom: 32rpx;
	}

	.quantity-label {
		font-size: 28rpx;
		color: #333333;
		width: 160rpx;
	}

	.quantity-controls {
		display: flex;
		align-items: center;
		gap: 32rpx;
	}

	.quantity-btn {
		width: 60rpx;
		height: 60rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;
	}

	.quantity-btn-text {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
	}

	.quantity-display {
		font-size: 32rpx;
		color: #333333;
		min-width: 60rpx;
		text-align: center;
	}
	/* 购物车数量选择弹出框 */
	.cart-modal {
		width: 560rpx;
		background-color: #ffffff;
		border-radius: 24rpx;
		padding: 48rpx 40rpx;
		margin: 0 32rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.cart-modal .modal-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
		text-align: center;
		margin-bottom: 32rpx;
		display: block;
	}

	.product-name-modal {
		font-size: 28rpx;
		color: #666666;
		text-align: center;
		margin-bottom: 40rpx;
		display: block;
	}

	.quantity-controls-large {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 40rpx;
		margin-bottom: 32rpx;
	}

	.quantity-btn-large {
		width: 80rpx;
		height: 80rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;
	}

	.quantity-btn-text-large {
		font-size: 36rpx;
		color: #333333;
		font-weight: 500;
	}

	.quantity-display-large {
		font-size: 36rpx;
		color: #333333;
		font-weight: 500;
		min-width: 80rpx;
		text-align: center;
	}

	.stock-info {
		font-size: 24rpx;
		color: #999999;
		text-align: center;
		margin-bottom: 48rpx;
		display: block;
	}

	.total-section {
		display: flex;
		align-items: center;
		margin-bottom: 32rpx;
		
	}

	.total-label {
		font-size: 28rpx;
		color: #333333;
		margin-right: 16rpx;
		
	}

	.total-price {
		font-size: 36rpx;
		font-weight: 600;
		color: #4CAF50;
		
	}

	.confirm-tip {
		font-size: 24rpx;
		color: #666666;
		line-height: 1.6;
		margin-bottom: 48rpx;
		display: block;
		
	}

	.modal-buttons {
		display: flex;
		gap: 32rpx;
		width: 100%;
	}

	.cancel-btn {
		flex: 1;
		height: 80rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;
	}

	.cancel-text {
		font-size: 30rpx;
		color: #666666;
		font-weight: 400;
	}

	.confirm-btn {
		flex: 1;
		height: 80rpx;
		background-color: #4CAF50;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.confirm-text {
		font-size: 30rpx;
		color: #ffffff;
		font-weight: 500;
	}

	/* RTL布局支持 */
	.ug.lang-ug .product-left {
		margin-right: 0;
		margin-left: 40rpx;
	}

	.ug.lang-ug .price-info {
		flex-direction: row-reverse;
	}

	.ug.lang-ug .price-label {
		margin-right: 0;
		margin-left: 16rpx;
	}

	.ug.lang-ug .price-symbol {
		margin-right: 0;
		margin-left: 8rpx;
	}

	.ug.lang-ug .price-value {
		margin-right: 0;
		margin-left: 20rpx;
	}

	.ug.lang-ug .doctor-info {
		flex-direction: row-reverse;
	}

	.ug.lang-ug .quantity-controls,
	.ug.lang-ug .quantity-controls-large {
		flex-direction: row-reverse;
	}

	.ug.lang-ug .total-label {
		margin-right: 0;
		margin-left: 16rpx;
	}

	.ug.lang-ug .modal-buttons {
		flex-direction: row-reverse;
	}

	/* 字体大小响应式样式 */
	.font-size-small {
		.product-title {
			font-size: 32rpx !important;
		}

		.product-subtitle {
			font-size: 26rpx !important;
		}

		.manufacturer {
			font-size: 24rpx !important;
		}

		.doctor-label {
			font-size: 22rpx !important;
		}

		.doctor-name {
			font-size: 26rpx !important;
		}

		.price-value {
			font-size: 40rpx !important;
		}

		.original-price {
			font-size: 24rpx !important;
		}

		.unit {
			font-size: 24rpx !important;
		}

		.appointment-time {
			font-size: 24rpx !important;
		}

		.detail-title {
			font-size: 28rpx !important;
		}

		.detail-content {
			font-size: 26rpx !important;
		}

		.cart-text {
			font-size: 26rpx !important;
		}

		.buy-text {
			font-size: 26rpx !important;
		}

		.modal-title {
			font-size: 32rpx !important;
		}

		.modal-content {
			font-size: 26rpx !important;
		}

		.confirm-text {
			font-size: 26rpx !important;
		}
	}

	.font-size-medium {
		.product-title {
			font-size: 36rpx !important;
		}

		.product-subtitle {
			font-size: 30rpx !important;
		}

		.manufacturer {
			font-size: 28rpx !important;
		}

		.doctor-label {
			font-size: 26rpx !important;
		}

		.doctor-name {
			font-size: 30rpx !important;
		}

		.price-value {
			font-size: 44rpx !important;
		}

		.original-price {
			font-size: 28rpx !important;
		}

		.unit {
			font-size: 28rpx !important;
		}

		.appointment-time {
			font-size: 28rpx !important;
		}

		.detail-title {
			font-size: 32rpx !important;
		}

		.detail-content {
			font-size: 30rpx !important;
		}

		.cart-text {
			font-size: 30rpx !important;
		}

		.buy-text {
			font-size: 30rpx !important;
		}

		.modal-title {
			font-size: 36rpx !important;
		}

		.modal-content {
			font-size: 30rpx !important;
		}

		.confirm-text {
			font-size: 30rpx !important;
		}
	}

	.font-size-large {
		.product-title {
			font-size: 40rpx !important;
		}

		.product-subtitle {
			font-size: 34rpx !important;
		}

		.manufacturer {
			font-size: 32rpx !important;
		}

		.doctor-label {
			font-size: 30rpx !important;
		}

		.doctor-name {
			font-size: 34rpx !important;
		}

		.price-value {
			font-size: 48rpx !important;
		}

		.original-price {
			font-size: 32rpx !important;
		}

		.unit {
			font-size: 32rpx !important;
		}

		.appointment-time {
			font-size: 32rpx !important;
		}

		.detail-title {
			font-size: 36rpx !important;
		}

		.detail-content {
			font-size: 34rpx !important;
		}

		.cart-text {
			font-size: 34rpx !important;
		}

		.buy-text {
			font-size: 34rpx !important;
		}

		.modal-title {
			font-size: 40rpx !important;
		}

		.modal-content {
			font-size: 34rpx !important;
		}

		.confirm-text {
			font-size: 34rpx !important;
		}
	}
</style>
