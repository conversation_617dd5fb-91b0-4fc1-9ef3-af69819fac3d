<template>
	<!--
		✏️ 编辑资料页面模板
		这是用户编辑个人资料的页面，包含头像、基本信息、联系方式等
		用户可以在这里修改个人信息并保存
		支持头像上传、性别选择、生日选择等功能
	-->

	<!--
		📄 编辑资料页面容器
		- class="edit-profile-container with-save-button": 基础页面样式，包含保存按钮样式
		- :class: 动态样式类，包括：
		  * fontSizeClass: 字体大小样式类
		  * fontClass: 字体类型样式类（支持维吾尔文等）
		  * pageClass: 页面布局样式类（RTL支持）
		- :style: 动态样式，主要用于RTL布局
		- :key: 强制重新渲染的键，当字体变化时重新渲染
	-->
	<view class="edit-profile-container with-save-button" :class="[fontSizeClass, fontClass, pageClass]" :style="pageStyle" :key="fontSizeUpdateKey">
		<!--
			👤 头像编辑区域
			用户可以在这里查看和更换头像
		-->
		<view class="avatar-section">
			<!--
				头像容器
				@click: 点击时触发更换头像功能
			-->
			<view class="avatar-container" @click="changeAvatar">
				<!-- 头像圆圈 -->
				<view class="avatar-circle">
					<!--
						用户头像显示逻辑
						v-if: 当用户有头像且不是默认头像时显示真实头像
						userInfo.avatar: 用户头像路径
						@error: 头像加载失败时的处理函数
					-->
					<!-- 头像显示：优先显示新上传的头像，否则显示原头像 -->
					<image v-if="localAvatar || userInfo.avatar"
						   class="avatar-image"
						   :src="localAvatar || userInfo.avatar"
						   mode="aspectFill"></image>

					<!-- 默认头像图标 -->
					<fui-icon v-else name="my" :size="60" color="white"></fui-icon>
				</view>

				<!--
					相机徽章
					显示在头像右下角，提示用户可以更换头像
				-->
				<view class="camera-badge">
					<!-- 相机图标 -->
					<fui-icon name="camera" :size="16" color="white"></fui-icon>
				</view>
			</view>

			<!--
				头像标题
				$t('user.changeAvatar'): 从语言包获取"更换头像"文字
			-->
			<text class="avatar-title">{{ $t('user.changeAvatar') }}</text>

			<!--
				头像副标题
				$t('user.clickToChangePhoto'): 从语言包获取"点击更换照片"文字
			-->
			<text class="avatar-subtitle">{{ $t('user.clickToChangePhoto') }}</text>
		</view>

		<!--
			📋 基本信息区域
			包含用户的基本个人信息，如昵称、性别等
		-->
		<view class="section basic-section">
			<!-- 📝 基本信息标题区域 -->
			<view class="section-header basic-header">
				<!-- 基本信息图标 -->
				<view class="section-header-icon basic-header-icon">
					<!-- 用户图标，绿色主题 -->
					<fui-icon name="my" :size="24" color="#00b96b"></fui-icon>
				</view>

				<!--
					基本信息标题
					$t('user.basicInfo'): 从语言包获取"基本信息"文字
				-->
				<text class="section-header-title basic-header-title">{{ $t('user.basicInfo') }}</text>
			</view>

			<!-- 📋 基本信息卡片容器 -->
			<view class="basic-card-container">
				<!--
					📝 昵称输入项
					用户可以在这里修改昵称
				-->
				<view class="basic-form-item">
					<!--
						昵称标签
						$t('user.nickname'): 从语言包获取"昵称"文字
					-->
					<text class="basic-label">{{ $t('user.nickname') }}</text>

					<!-- 昵称输入容器 -->
					<view class="basic-input-container">
						<!--
							昵称输入框
							v-model: 双向绑定到formData.nickname
							placeholder: 输入框占位符
						-->
						<input class="basic-input-field" v-model="formData.nickname" placeholder="AAA" />
					</view>
				</view>

				<!--
					👫 性别选择项
					用户可以在这里选择性别
				-->
				<view class="basic-form-item">
					<!--
						性别标签
						$t('user.gender'): 从语言包获取"性别"文字
					-->
					<text class="basic-label">{{ $t('user.gender') }}</text>

					<!--
						性别选择容器
						@click: 点击时显示性别选择器
					-->
					<view class="basic-select-container" @click="showGenderPicker">
						<!-- 性别选择字段 -->
						<view class="basic-select-field">
							<!-- 性别图标 -->
							<view class="basic-select-icon gender-icon">
								<!-- 女性图标，绿色主题 -->
								<fui-icon name="female" :size="20" color="#00b96b"></fui-icon>
							</view>
							<text class="basic-select-text">{{ genderText }}</text>
							<fui-icon name="down" :size="16" color="#bfbfbf"></fui-icon>
						</view>
					</view>
				</view>

				<!-- 生日选择项 -->
				<view class="basic-form-item">
					<text class="basic-label">{{ $t('user.birthday') }}</text>
					<view class="basic-select-container" @click="showDatePicker">
						<view class="basic-select-field">
							<view class="basic-select-icon birthday-icon">
								<fui-icon name="calendar" :size="20" color="#00b96b"></fui-icon>
							</view>
							<text class="basic-select-text">{{ formData.birthday || '' }}</text>
							<fui-icon name="down" :size="16" color="#bfbfbf"></fui-icon>
						</view>
					</view>
				</view>

				<!-- 身高体重行 -->
				<view class="basic-form-row">
					<!-- 身高输入项 -->
					<view class="basic-form-item-half">
						<text class="basic-label">{{ $t('user.height') }}</text>
						<view class="basic-input-container">
							<input class="basic-input-field" v-model="formData.height" :placeholder="$t('user.enterHeight')"
								type="number" />
						</view>
					</view>
					<!-- 体重输入项 -->
					<view class="basic-form-item-half">
						<text class="basic-label">{{ $t('user.weight') }}</text>
						<view class="basic-input-container">
							<input class="basic-input-field" v-model="formData.weight" :placeholder="$t('user.enterWeight')"
								type="number" />
						</view>
					</view>
				</view>

				<!-- 血型选择项 -->
				<view class="basic-form-item">
					<text class="basic-label">{{ $t('user.bloodType') }}</text>
					<view class="basic-select-container" @click="showBloodTypePicker">
						<view class="basic-select-field">
							<view class="basic-select-icon blood-icon">
								<fui-icon name="heart" :size="20" color="#00b96b"></fui-icon>
							</view>
							<text class="basic-select-text">{{ bloodTypeText }}</text>
							<fui-icon name="down" :size="16" color="#bfbfbf"></fui-icon>
						</view>
					</view>
				</view>

				<!-- 常住地址选择项 -->
				<view class="basic-form-item">
					<view class="basic-label-row">
						<text class="basic-label">{{ $t('user.location') }}</text>
						<view class="basic-location-btn" :class="{ 'locating': isLocating }" @click="getCurrentLocation">
							<fui-icon :name="isLocating ? 'refresh' : 'location'" :size="14" :color="isLocating ? '#1890ff' : '#00b96b'"></fui-icon>
							<text>{{ isLocating ? $t('user.locating') : $t('user.locate') }}</text>
						</view>
					</view>
					<!-- 地址选择器 - 支持手动选择和自动定位 -->
					<view class="basic-select-container">
						<picker mode="region" @change="onRegionChange" :value="regionValue">
							<view class="basic-select-field">
								<view class="basic-select-icon location-icon">
									<fui-icon name="location" :size="20" color="#00b96b"></fui-icon>
								</view>
								<text class="basic-select-text">{{ formData.location || $t('user.defaultLocation') }}</text>
								<fui-icon name="down" :size="16" color="#bfbfbf"></fui-icon>
							</view>
						</picker>
					</view>
				</view>
			</view>
		</view>

		<!-- 健康信息区域 -->
		<view class="section health-section">
			<view class="section-header health-header">
				<view class="section-header-icon health-header-icon">
					<fui-icon name="shield" :size="24" color="#52c41a"></fui-icon>
				</view>
				<text class="section-header-title health-header-title">{{ $t('user.healthInfo') }}</text>
			</view>

			<!-- 过敏史卡片 -->
			<view class="health-card expandable-card" :class="{ 'expanded': formData.hasAllergy }">
				<text class="health-card-title">{{ $t('user.allergyHistory') }}</text>
				<text class="health-card-desc">{{ $t('user.allergyDesc') }}</text>
				<view class="health-radio-group">
					<!-- 是选项 -->
					<view class="health-radio-item" @click="setAllergy(true)">
						<view class="health-radio-circle" :class="{ active: formData.hasAllergy }"></view>
						<text class="health-radio-text">{{ $t('user.yes') }}</text>
					</view>
					<!-- 否选项 -->
					<view class="health-radio-item" @click="setAllergy(false)">
						<view class="health-radio-circle" :class="{ active: !formData.hasAllergy }"></view>
						<text class="health-radio-text">{{ $t('user.no') }}</text>
					</view>
				</view>

				<!-- 展开的过敏源选项 -->
				<view class="allergy-details" v-if="formData.hasAllergy">
					<text class="allergy-subtitle">{{ $t('user.commonAllergens') }}</text>

					<view class="allergy-options">
						<view class="allergy-option" @click="toggleAllergen($t('user.penicillin'))">
							<view class="allergy-checkbox" :class="{ checked: allergens.includes($t('user.penicillin')) }"></view>
							<text class="allergy-option-text">{{ $t('user.penicillin') }}</text>
						</view>

						<view class="allergy-option" @click="toggleAllergen($t('user.cephalosporin'))">
							<view class="allergy-checkbox" :class="{ checked: allergens.includes($t('user.cephalosporin')) }"></view>
							<text class="allergy-option-text">{{ $t('user.cephalosporin') }}</text>
						</view>

						<view class="allergy-option" @click="toggleAllergen($t('user.aspirin'))">
							<view class="allergy-checkbox" :class="{ checked: allergens.includes($t('user.aspirin')) }"></view>
							<text class="allergy-option-text">{{ $t('user.aspirin') }}</text>
						</view>

						<view class="allergy-option" @click="toggleAllergen($t('user.peanuts'))">
							<view class="allergy-checkbox" :class="{ checked: allergens.includes($t('user.peanuts')) }"></view>
							<text class="allergy-option-text">{{ $t('user.peanuts') }}</text>
						</view>

						<view class="allergy-option" @click="toggleAllergen($t('user.seafood'))">
							<view class="allergy-checkbox" :class="{ checked: allergens.includes($t('user.seafood')) }"></view>
							<text class="allergy-option-text">{{ $t('user.seafood') }}</text>
						</view>

						<view class="allergy-option" @click="toggleAllergen($t('user.milk'))">
							<view class="allergy-checkbox" :class="{ checked: allergens.includes($t('user.milk')) }"></view>
							<text class="allergy-option-text">{{ $t('user.milk') }}</text>
						</view>

						<view class="allergy-option" @click="toggleAllergen($t('user.eggs'))">
							<view class="allergy-checkbox" :class="{ checked: allergens.includes($t('user.eggs')) }"></view>
							<text class="allergy-option-text">{{ $t('user.eggs') }}</text>
						</view>

						<view class="allergy-option" @click="toggleAllergen($t('user.pollenMites'))">
							<view class="allergy-checkbox" :class="{ checked: allergens.includes($t('user.pollenMites')) }"></view>
							<text class="allergy-option-text">{{ $t('user.pollenMites') }}</text>
						</view>
					</view>

					<text class="allergy-subtitle">{{ $t('user.otherAllergies') }}</text>
					<view class="allergy-input-container">
						<textarea
							class="allergy-input"
							:placeholder="$t('user.otherAllergiesPlaceholder')"
							v-model="otherAllergies"
							maxlength="200"
						></textarea>
					</view>
				</view>
			</view>

			<!-- 当前用药卡片 -->
			<view class="health-card expandable-card" :class="{ 'expanded': formData.hasMedication }">
				<text class="health-card-title">{{ $t('user.currentMedication') }}</text>
				<text class="health-card-desc">{{ $t('user.currentMedicationDesc') }}</text>
				<view class="health-radio-group">
					<!-- 是选项 -->
					<view class="health-radio-item" @click="setMedication(true)">
						<view class="health-radio-circle" :class="{ active: formData.hasMedication }"></view>
						<text class="health-radio-text">{{ $t('user.yes') }}</text>
					</view>
					<!-- 否选项 -->
					<view class="health-radio-item" @click="setMedication(false)">
						<view class="health-radio-circle" :class="{ active: !formData.hasMedication }"></view>
						<text class="health-radio-text">{{ $t('user.no') }}</text>
					</view>
				</view>

				<!-- 展开的药物清单 -->
				<view class="medication-details" v-if="formData.hasMedication">
					<text class="allergy-subtitle">{{ $t('user.medicationList') }}</text>
					<view class="medication-input-container">
						<textarea
							class="allergy-input"
							:placeholder="$t('user.medicationListPlaceholder')"
							v-model="medicationList"
							maxlength="500"
						></textarea>
					</view>
				</view>
			</view>

			<!-- 慢性病史卡片 -->
			<view class="health-card expandable-card" :class="{ 'expanded': formData.hasChronicDisease }">
				<text class="health-card-title">{{ $t('user.chronicDiseaseHistory') }}</text>
				<text class="health-card-desc">{{ $t('user.chronicDiseaseDesc') }}</text>
				<view class="health-radio-group">
					<!-- 是选项 -->
					<view class="health-radio-item" @click="setChronicDisease(true)">
						<view class="health-radio-circle" :class="{ active: formData.hasChronicDisease }"></view>
						<text class="health-radio-text">{{ $t('user.yes') }}</text>
					</view>
					<!-- 否选项 -->
					<view class="health-radio-item" @click="setChronicDisease(false)">
						<view class="health-radio-circle" :class="{ active: !formData.hasChronicDisease }"></view>
						<text class="health-radio-text">{{ $t('user.no') }}</text>
					</view>
				</view>

				<!-- 展开的慢性病选项 -->
				<view class="chronic-disease-details" v-if="formData.hasChronicDisease">
					<text class="allergy-subtitle">{{ $t('user.specificSymptoms') }}</text>

					<view class="allergy-options">
						<!-- 高血压选项 -->
						<view class="allergy-option" @click="toggleChronicDisease($t('user.hypertension'))">
							<view class="allergy-checkbox" :class="{ checked: chronicDiseases.includes($t('user.hypertension')) }"></view>
							<text class="allergy-option-text">{{ $t('user.hypertension') }}</text>
						</view>

						<!-- 高血压详细信息 (仅当选择高血压时显示) -->
						<view v-if="chronicDiseases.includes($t('user.hypertension'))" class="chronic-disease-detail-card">
							<text class="chronic-detail-label">{{ $t('user.hypertensionRange') }}</text>
							<view class="chronic-input-container">
								<input
									class="chronic-input-field"
									v-model="hypertensionRange"
									:placeholder="$t('user.hypertensionPlaceholder')"
									maxlength="50"
								/>
							</view>
						</view>

						<!-- 糖尿病选项 -->
						<view class="allergy-option" @click="toggleChronicDisease($t('user.diabetes'))">
							<view class="allergy-checkbox" :class="{ checked: chronicDiseases.includes($t('user.diabetes')) }"></view>
							<text class="allergy-option-text">{{ $t('user.diabetes') }}</text>
						</view>

						<!-- 糖尿病详细信息 (仅当选择糖尿病时显示) -->
						<view v-if="chronicDiseases.includes($t('user.diabetes'))" class="chronic-disease-detail-card">
							<text class="chronic-detail-label">{{ $t('user.diabetesRange') }}</text>
							<view class="chronic-input-container">
								<input
									class="chronic-input-field"
									v-model="diabetesRange"
									:placeholder="$t('user.diabetesPlaceholder')"
									maxlength="50"
								/>
							</view>
						</view>
					</view>

					<text class="allergy-subtitle">{{ $t('user.otherChronicDiseases') }}</text>
					<view class="allergy-input-container">
						<textarea
							class="allergy-input"
							:placeholder="$t('user.otherChronicDiseasesPlaceholder')"
							v-model="otherChronicDiseases"
							maxlength="300"
						></textarea>
					</view>
				</view>
			</view>
		</view>

		<!-- 医疗历史区域 -->
		<view class="section medical-section">
			<!-- 医疗历史标题区域 -->
			<view class="section-header medical-header">
				<view class="section-header-icon medical-header-icon">
					<fui-icon name="calendar" :size="24" color="#00b96b"></fui-icon>
				</view>
				<text class="section-header-title medical-header-title">{{ $t('user.medicalHistory') }}</text>
			</view>

			<!-- 手术与住院史卡片 -->
			<view class="medical-card surgery-card expandable-card" :class="{ 'expanded': formData.hasSurgery }">
				<text class="medical-card-title">{{ $t('user.surgeryHistory') }}</text>
				<text class="medical-card-desc">{{ $t('user.surgeryHistoryDesc') }}</text>
				<view class="medical-radio-group">
					<!-- 是选项 -->
					<view class="medical-radio-item" @click="setSurgery(true)">
						<view class="medical-radio-circle" :class="{ active: formData.hasSurgery }"></view>
						<text class="medical-radio-text">{{ $t('user.yes') }}</text>
					</view>
					<!-- 否选项 -->
					<view class="medical-radio-item" @click="setSurgery(false)">
						<view class="medical-radio-circle" :class="{ active: !formData.hasSurgery }"></view>
						<text class="medical-radio-text">{{ $t('user.no') }}</text>
					</view>
				</view>

				<!-- 展开的手术住院详情 -->
				<view class="surgery-details" v-if="formData.hasSurgery">
					<text class="medical-details-subtitle">{{ $t('user.surgeryDetails') }}</text>
					<view class="medical-textarea-container">
						<textarea
							class="medical-textarea-field"
							:placeholder="$t('user.surgeryDetailsPlaceholder')"
							v-model="surgeryDetails"
							maxlength="500"
						></textarea>
					</view>
				</view>
			</view>

			<!-- 家族病史卡片 -->
			<view class="medical-card family-card">
				<text class="medical-card-title">{{ $t('user.familyHistory') }}</text>
				<text class="medical-card-desc">{{ $t('user.familyHistoryDesc') }}</text>

				<!-- 家族病史选项组 -->
				<view class="medical-checkbox-group">
					<!-- 高血压选项 -->
					<view class="medical-checkbox-item" @click="toggleFamilyDisease('hypertension')">
						<view class="medical-checkbox"
							:class="{ checked: formData.familyDiseases.includes('hypertension') }"></view>
						<text class="medical-checkbox-text">{{ $t('user.hypertension') }}</text>
					</view>
					<!-- 糖尿病选项 -->
					<view class="medical-checkbox-item" @click="toggleFamilyDisease('diabetes')">
						<view class="medical-checkbox"
							:class="{ checked: formData.familyDiseases.includes('diabetes') }">
						</view>
						<text class="medical-checkbox-text">{{ $t('user.diabetes') }}</text>
					</view>
					<!-- 心脏病选项 -->
					<view class="medical-checkbox-item" @click="toggleFamilyDisease('heart')">
						<view class="medical-checkbox" :class="{ checked: formData.familyDiseases.includes('heart') }">
						</view>
						<text class="medical-checkbox-text">{{ $t('user.heartDisease') }}</text>
					</view>
					<!-- 中风选项 -->
					<view class="medical-checkbox-item" @click="toggleFamilyDisease('stroke')">
						<view class="medical-checkbox" :class="{ checked: formData.familyDiseases.includes('stroke') }">
						</view>
						<text class="medical-checkbox-text">{{ $t('user.stroke') }}</text>
					</view>
					<!-- 癌症选项 -->
					<view class="medical-checkbox-item" @click="toggleFamilyDisease('cancer')">
						<view class="medical-checkbox" :class="{ checked: formData.familyDiseases.includes('cancer') }">
						</view>
						<text class="medical-checkbox-text">{{ $t('user.cancer') }}</text>
					</view>
					<!-- 精神健康疾病选项 -->
					<view class="medical-checkbox-item" @click="toggleFamilyDisease('mental')">
						<view class="medical-checkbox" :class="{ checked: formData.familyDiseases.includes('mental') }">
						</view>
						<text class="medical-checkbox-text">{{ $t('user.mentalHealth') }}</text>
					</view>
				</view>

				<!-- 其他家族病史补充 -->
				<text class="medical-other-label">{{ $t('user.otherFamilyHistory') }}</text>
				<view class="medical-textarea-container">
					<textarea class="medical-textarea-field" v-model="formData.otherFamilyHistory"
						:placeholder="$t('user.otherFamilyHistoryPlaceholder')" />
				</view>
			</view>
		</view>

		<!-- 生活方式区域 -->
		<view class="section lifestyle-section">
			<!-- 生活方式标题区域 -->
			<view class="section-header lifestyle-header">
				<view class="section-header-icon lifestyle-header-icon">
					<fui-icon name="activity" :size="24" color="#00b96b"></fui-icon>
				</view>
				<text class="section-header-title lifestyle-header-title">{{ $t('user.lifestyle') }}</text>
			</view>

			<!-- 生活方式卡片容器 -->
			<view class="lifestyle-card-container">
				<text class="lifestyle-card-title">{{ $t('user.lifestyle') }}</text>

				<!-- 运动频率选择项 -->
				<view class="lifestyle-form-item">
					<text class="lifestyle-label">{{ $t('user.exerciseFrequency') }}</text>
					<view class="lifestyle-select-container" @click="showExercisePicker">
						<view class="lifestyle-select-field">
							<text class="lifestyle-select-text">{{ exerciseText }}</text>
							<fui-icon name="down" :size="16" color="#bfbfbf"></fui-icon>
						</view>
					</view>
				</view>

				<!-- 日常饮食偏好选择项 -->
				<view class="lifestyle-form-item">
					<text class="lifestyle-label">{{ $t('user.dietPreferences') }}</text>
					<view class="lifestyle-checkbox-group">
						<!-- 爱吃辣的选项 -->
						<view class="lifestyle-checkbox-item" @click="toggleDietPreference('spicy')">
							<view class="lifestyle-checkbox"
								:class="{ checked: formData.dietPreferences.includes('spicy') }"></view>
							<text class="lifestyle-checkbox-text">{{ $t('user.spicy') }}</text>
						</view>
						<!-- 爱吃甜选项 -->
						<view class="lifestyle-checkbox-item" @click="toggleDietPreference('sweet')">
							<view class="lifestyle-checkbox"
								:class="{ checked: formData.dietPreferences.includes('sweet') }"></view>
							<text class="lifestyle-checkbox-text">{{ $t('user.sweet') }}</text>
						</view>
						<!-- 爱吃咸选项 -->
						<view class="lifestyle-checkbox-item" @click="toggleDietPreference('salty')">
							<view class="lifestyle-checkbox"
								:class="{ checked: formData.dietPreferences.includes('salty') }"></view>
							<text class="lifestyle-checkbox-text">{{ $t('user.salty') }}</text>
						</view>
						<!-- 爱吃清淡食物选项 -->
						<view class="lifestyle-checkbox-item" @click="toggleDietPreference('light')">
							<view class="lifestyle-checkbox"
								:class="{ checked: formData.dietPreferences.includes('light') }"></view>
							<text class="lifestyle-checkbox-text">{{ $t('user.light') }}</text>
						</view>
						<!-- 爱吃油腻食物选项 -->
						<view class="lifestyle-checkbox-item" @click="toggleDietPreference('oily')">
							<view class="lifestyle-checkbox"
								:class="{ checked: formData.dietPreferences.includes('oily') }"></view>
							<text class="lifestyle-checkbox-text">{{ $t('user.oily') }}</text>
						</view>
						<!-- 爱吃蔬菜选项 -->
						<view class="lifestyle-checkbox-item" @click="toggleDietPreference('vegetarian')">
							<view class="lifestyle-checkbox"
								:class="{ checked: formData.dietPreferences.includes('vegetarian') }"></view>
							<text class="lifestyle-checkbox-text">{{ $t('user.vegetarian') }}</text>
						</view>
					</view>
				</view>

				<!-- 吸烟情况选择项 -->
				<view class="lifestyle-form-item">
					<text class="lifestyle-label">{{ $t('user.smokingStatus') }}</text>
					<view class="lifestyle-select-container" @click="showSmokingPicker">
						<view class="lifestyle-select-field">
							<text class="lifestyle-select-text">{{ smokingText }}</text>
							<fui-icon name="down" :size="16" color="#bfbfbf"></fui-icon>
						</view>
					</view>
				</view>

				<!-- 饮酒情况选择项 -->
				<view class="lifestyle-form-item">
					<text class="lifestyle-label">{{ $t('user.drinkingStatus') }}</text>
					<view class="lifestyle-select-container" @click="showDrinkingPicker">
						<view class="lifestyle-select-field">
							<text class="lifestyle-select-text">{{ drinkingText }}</text>
							<fui-icon name="down" :size="16" color="#bfbfbf"></fui-icon>
						</view>
					</view>
				</view>

				<!-- 平均每晚睡眠时长选择项 -->
				<view class="lifestyle-form-item">
					<text class="lifestyle-label">{{ $t('user.sleepDuration') }}</text>
					<view class="lifestyle-select-container" @click="showSleepPicker">
						<view class="lifestyle-select-field">
							<text class="lifestyle-select-text">{{ sleepText }}</text>
							<fui-icon name="down" :size="16" color="#bfbfbf"></fui-icon>
						</view>
					</view>
				</view>

				<!-- 睡眠质量选择项 -->
				<view class="lifestyle-form-item">
					<text class="lifestyle-label">{{ $t('user.sleepQuality') }}</text>
					<view class="lifestyle-select-container" @click="showSleepQualityPicker">
						<view class="lifestyle-select-field">
							<text class="lifestyle-select-text">{{ sleepQualityText }}</text>
							<fui-icon name="down" :size="16" color="#bfbfbf"></fui-icon>
						</view>
					</view>
				</view>

				<!-- 近期压力水平选择项 -->
				<view class="lifestyle-form-item">
					<text class="lifestyle-label">{{ $t('user.stressLevel') }}</text>
					<view class="lifestyle-select-container" @click="showStressPicker">
						<view class="lifestyle-select-field">
							<text class="lifestyle-select-text">{{ stressText }}</text>
							<fui-icon name="down" :size="16" color="#bfbfbf"></fui-icon>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 女性健康区域 (仅当性别为女时显示) -->
		<view v-if="formData.gender === 2" class="section women-health-section">
			<!-- 女性健康标题区域 -->
			<view class="section-header women-health-header">
				<view class="section-header-icon women-health-header-icon">
					<fui-icon name="female" :size="24" color="#00b96b"></fui-icon>
				</view>
				<text class="section-header-title women-health-header-title">{{ $t('user.womenHealth') }}</text>
			</view>

			<!-- 女性健康卡片容器 -->
			<view class="women-health-card-container">
				<text class="women-health-card-title">{{ $t('user.womenHealth') }}</text>

				<!-- 是否已绝经选择项 -->
				<view class="women-health-form-item">
					<text class="women-health-label">{{ $t('user.menopause') }}</text>
					<view class="women-health-radio-group">
						<!-- 是选项 -->
						<view class="women-health-radio-item" @click="setMenopause(true)">
							<view class="women-health-radio-circle" :class="{ active: formData.isMenopause }"></view>
							<text class="women-health-radio-text">{{ $t('user.yes') }}</text>
						</view>
						<!-- 否选项 -->
						<view class="women-health-radio-item" @click="setMenopause(false)">
							<view class="women-health-radio-circle" :class="{ active: !formData.isMenopause }"></view>
							<text class="women-health-radio-text">{{ $t('user.no') }}</text>
						</view>
					</view>
				</view>

				<!-- 月经周期是否规律选择项 (仅未绝经时显示) -->
				<view v-if="!formData.isMenopause" class="women-health-form-item">
					<text class="women-health-label">{{ $t('user.menstrualCycle') }}</text>
					<view class="women-health-select-container" @click="showMenstrualCyclePicker">
						<view class="women-health-select-field">
							<text class="women-health-select-text">{{ menstrualCycleText }}</text>
							<fui-icon name="down" :size="16" color="#bfbfbf"></fui-icon>
						</view>
					</view>
				</view>

				<!-- 是否曾怀孕选择项 -->
				<view class="women-health-form-item">
					<text class="women-health-label">{{ $t('user.pregnancy') }}</text>
					<view class="women-health-radio-group">
						<!-- 是选项 -->
						<view class="women-health-radio-item" @click="setPregnancy(true)">
							<view class="women-health-radio-circle" :class="{ active: formData.hasPregnancy }"></view>
							<text class="women-health-radio-text">{{ $t('user.yes') }}</text>
						</view>
						<!-- 否选项 -->
						<view class="women-health-radio-item" @click="setPregnancy(false)">
							<view class="women-health-radio-circle" :class="{ active: !formData.hasPregnancy }"></view>
							<text class="women-health-radio-text">{{ $t('user.no') }}</text>
						</view>
					</view>
				</view>

				<!-- 生育次数选择项 (仅曾怀孕时显示) -->
				<view v-if="formData.hasPregnancy" class="women-health-form-item">
					<text class="women-health-label">{{ $t('user.birthCount') }}</text>
					<view class="women-health-select-container" @click="showBirthCountPicker">
						<view class="women-health-select-field">
							<text class="women-health-select-text">{{ birthCountText }}</text>
							<fui-icon name="down" :size="16" color="#bfbfbf"></fui-icon>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 性别选择弹窗 -->
	<view v-if="showGenderModal" class="modal-mask" @click="showGenderModal = false">
		<view class="gender-modal" @click.stop>
			<view class="modal-header">
				<text class="modal-title">{{ $t('user.gender') }}</text>
			</view>
			<view class="modal-content">
				<!-- 男 -->
				<view
					class="gender-option-card"
					:class="{ 'selected': formData.gender === 1 }"
					@click="selectGender(1)"
				>
					<view class="gender-icon">
						<fui-icon name="male" :size="40" color="#1890ff"></fui-icon>
					</view>
					<text class="gender-text">{{ $t('user.male') }}</text>
					<view v-if="formData.gender === 1" class="check-icon">
						<fui-icon name="checkbox" :size="24" color="#52c41a"></fui-icon>
					</view>
				</view>

				<!-- 女 -->
				<view
					class="gender-option-card"
					:class="{ 'selected': formData.gender === 2 }"
					@click="selectGender(2)"
				>
					<view class="gender-icon female">
						<fui-icon name="female" :size="40" color="#eb2f96"></fui-icon>
					</view>
					<text class="gender-text">{{ $t('user.female') }}</text>
					<view v-if="formData.gender === 2" class="check-icon">
						<fui-icon name="checkbox" :size="24" color="#52c41a"></fui-icon>
					</view>
				</view>

				<!-- 未设置 -->
				<view
					class="gender-option-card"
					:class="{ 'selected': formData.gender === 0 }"
					@click="selectGender(0)"
				>
					<view class="gender-icon info">
						<fui-icon name="info" :size="40" color="#999"></fui-icon>
					</view>
					<text class="gender-text">{{ $t('user.notSet') }}</text>
					<view v-if="formData.gender === 0" class="check-icon">
						<fui-icon name="checkbox" :size="24" color="#52c41a"></fui-icon>
					</view>
				</view>
			</view>
			<view class="modal-footer">
				<text class="cancel-text" @click="showGenderModal = false">{{ $t('common.cancel') }}</text>
			</view>
		</view>
	</view>

	<!-- 血型选择弹窗 -->
	<view v-if="showBloodTypeModal" class="modal-mask" @click="showBloodTypeModal = false">
		<view class="bloodtype-modal" @click.stop>
			<view class="modal-header">
				<text class="modal-title">{{ $t('user.bloodType') }}</text>
			</view>
			<view class="modal-content">
				<!-- 不清楚 -->
				<view
					class="gender-option-card"
					:class="{ 'selected': formData.bloodType === 0 }"
					@click="selectBloodType(0)"
				>

					<text class="gender-text">{{ $t('user.unknownBloodType') }}</text>
					<view v-if="formData.bloodType === 0" class="check-icon">✓</view>
				</view>

				<!-- A型 -->
				<view
					class="gender-option-card"
					:class="{ 'selected': formData.bloodType === 1 }"
					@click="selectBloodType(1)"
				>

					<text class="gender-text">{{ $t('user.typeA') }}</text>
					<view v-if="formData.bloodType === 1" class="check-icon">✓</view>
				</view>

				<!-- B型 -->
				<view
					class="gender-option-card"
					:class="{ 'selected': formData.bloodType === 2 }"
					@click="selectBloodType(2)"
				>

					<text class="gender-text">{{ $t('user.typeB') }}</text>
					<view v-if="formData.bloodType === 2" class="check-icon">✓</view>
				</view>

				<!-- AB型 -->
				<view
					class="gender-option-card"
					:class="{ 'selected': formData.bloodType === 3 }"
					@click="selectBloodType(3)"
				>

					<text class="gender-text">{{ $t('user.typeAB') }}</text>
					<view v-if="formData.bloodType === 3" class="check-icon">✓</view>
				</view>

				<!-- O型 -->
				<view
					class="gender-option-card"
					:class="{ 'selected': formData.bloodType === 4 }"
					@click="selectBloodType(4)"
				>

					<text class="gender-text">{{ $t('user.typeO') }}</text>
					<view v-if="formData.bloodType === 4" class="check-icon">✓</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 日历选择弹窗 -->
	<view v-if="showCalendarModal" class="calendar-modal-mask" @click="onCalendarCancel">
		<view class="calendar-modal" @click.stop>
			<view class="calendar-header">
				<text class="calendar-title">{{ $t('user.selectDate') }}</text>
			</view>
			<view class="calendar-content">
				<fui-calendar
					:type="1"
					:value="selectedDate ? [selectedDate] : (formData.birthday ? [formData.birthday] : [])"
					:minDate="'1900-01-01'"
					:maxDate="new Date().toISOString().split('T')[0]"
					:isMultiple="false"
					:showBtn="false"
					@change="onCalendarChange"
				></fui-calendar>
			</view>
			<view class="calendar-footer">
				<view class="calendar-btn cancel-btn" @click="onCalendarCancel">
					<text class="calendar-btn-text cancel-text">{{ $t('common.cancel') }}</text>
				</view>
				<view class="calendar-btn confirm-btn" @click="onCalendarConfirm">
					<text class="calendar-btn-text confirm-text">{{ $t('common.confirm') }}</text>
				</view>
			</view>
		</view>
	</view>

	<!-- 固定底部保存按钮 -->
	<view class="save-button-container">
		<view class="save-button" @click="saveProfile">
			<text class="save-button-text">{{ $t('user.save') }}</text>
		</view>
	</view>


</template>

<script setup>
	// ===== 导入依赖 =====
	import {ref,computed,onMounted,onUnmounted} from 'vue'
	import {useUserStore} from '@/store/user.js'
	import {useAppStore} from '@/store/app.js'
	import {useFontSizePage} from '@/utils/fontSizeMixin.js'
	import { validateNickname, getCurrentUserInfo } from '@/request/userSync.js'
	import { processAvatarUrl } from '@/request/avatar.js'
	import { userinfo, updateUserInfo as updateUserProfileApi, uploadUserAvatar } from '@/request/index.js'
	import { t } from '@/locale/index.js'
	import { useRTLPage } from '@/utils/rtlMixin.js'

	// ===== 功能模块初始化 =====
	// 字体大小功能
	const {
		fontSizeClass,
		fontSizeUpdateKey
	} = useFontSizePage()

	// 用户状态管理
	const {
		userInfo
	} = useUserStore()

	// 应用状态管理
	const appStore = useAppStore()

	// RTL布局支持
	const { pageClass, pageStyle, isRTL } = useRTLPage()

	// 计算字体类（合并RTL类）
	const fontClass = computed(() => ({
		...pageClass.value,
		'ug': appStore.isUyghur,
		[`lang-${appStore.lang}`]: true
	}))

	// 翻译方法
	const $t = (key) => t(key)

	// ===== 表单数据定义 =====
	const formData = ref({
		// 基本信息
		nickname: '',
		gender: 0, // 0: 未知, 1: 男, 2: 女
		birthday: '',
		height: '',
		weight: '',
		bloodType: 0, // 0: 不清楚, 1: A型, 2: B型, 3: AB型, 4: O型
		location: '',

		// 健康信息
		hasAllergy: false,
		hasMedication: false,
		hasChronicDisease: false,
		hasSurgery: false,
		familyDiseases: [],
		otherFamilyHistory: '',

		// 生活方式
		exerciseFrequency: 0,
		dietPreferences: [],
		smokingHistory: 0,
		drinkingHistory: 0,
		sleepDuration: 0,
		sleepQuality: 0,
		stressLevel: 0,

		// 女性健康相关字段
		isMenopause: false, // 是否已绝经
		menstrualCycle: 0, // 月经周期规律性: 0: 规律, 1: 不规律, 2: 偶尔不规律
		hasPregnancy: false, // 是否曾怀孕
		birthCount: 0 // 生育次数: 0: 0次, 1: 1次, 2: 2次, 3: 3次, 4: 4次及以上
	})

	// ===== 弹窗控制状态 =====
	const showGenderModal = ref(false)
	const showBloodTypeModal = ref(false)
	const showCalendarModal = ref(false)

	// 🖼️ 头像相关变量
	const localAvatarFile = ref('')    // 本地选择的图片文件路径
	const localAvatar = ref('')        // 本地图片的显示地址
	const hasAvatarChanged = ref(false) // 是否选择了新头像

	// ===== 健康信息相关数据 =====
	const allergens = ref([]) // 过敏源列表
	const otherAllergies = ref('') // 其他过敏源
	const medicationList = ref('') // 当前用药清单
	const chronicDiseases = ref([]) // 慢性病列表
	const otherChronicDiseases = ref('') // 其他慢性病
	const surgeryDetails = ref('') // 手术与住院史详情

	// 慢性病详细数值
	const hypertensionRange = ref('') // 高血压范围
	const diabetesRange = ref('') // 糖尿病血糖范围

	// ===== 选择器选项配置 =====
	// 基本信息选项
	const genderOptions = computed(() => [$t('user.notSet'), $t('user.male'), $t('user.female')])
	const bloodTypeOptions = computed(() => [$t('user.unknownBloodType'), $t('user.typeA'), $t('user.typeB'), $t('user.typeAB'), $t('user.typeO')])

	// 生活方式选项
	const exerciseOptions = computed(() => [
		$t('user.exerciseOptions.sedentary'),
		$t('user.exerciseOptions.light'),
		$t('user.exerciseOptions.moderate'),
		$t('user.exerciseOptions.intense')
	])
	const smokingOptions = computed(() => [
		$t('user.smokingOptions.never'),
		$t('user.smokingOptions.occasionally'),
		$t('user.smokingOptions.frequently'),
		$t('user.smokingOptions.quit')
	])
	const drinkingOptions = computed(() => [
		$t('user.drinkingOptions.never'),
		$t('user.drinkingOptions.occasionally'),
		$t('user.drinkingOptions.frequently'),
		$t('user.drinkingOptions.quit')
	])
	const sleepOptions = computed(() => [
		$t('user.sleepOptions.normal'),
		$t('user.sleepOptions.short'),
		$t('user.sleepOptions.long'),
		$t('user.sleepOptions.veryShort'),
		$t('user.sleepOptions.veryLong')
	])
	const sleepQualityOptions = computed(() => [
		$t('user.sleepQualityOptions.veryGood'),
		$t('user.sleepQualityOptions.good'),
		$t('user.sleepQualityOptions.poor'),
		$t('user.sleepQualityOptions.veryPoor')
	])
	const stressOptions = computed(() => [
		$t('user.stressOptions.veryLow'),
		$t('user.stressOptions.low'),
		$t('user.stressOptions.high'),
		$t('user.stressOptions.veryHigh')
	])

	// 女性健康选项
	const menstrualCycleOptions = computed(() => [
		$t('user.menstrualCycleOptions.regular'),
		$t('user.menstrualCycleOptions.irregular'),
		$t('user.menstrualCycleOptions.sometimes')
	])
	const birthCountOptions = computed(() => [
		$t('user.birthCountOptions.zero'),
		$t('user.birthCountOptions.one'),
		$t('user.birthCountOptions.two'),
		$t('user.birthCountOptions.three'),
		$t('user.birthCountOptions.fourPlus')
	])

	// 计算属性
	const genderText = computed(() => genderOptions.value[formData.value.gender] || $t('user.notSet'))
	const bloodTypeText = computed(() => bloodTypeOptions.value[formData.value.bloodType] || $t('user.unknownBloodType'))
	const exerciseText = computed(() => exerciseOptions.value[formData.value.exerciseFrequency] || $t('user.exerciseOptions.sedentary'))
	const smokingText = computed(() => smokingOptions.value[formData.value.smokingHistory] || $t('user.smokingOptions.never'))
	const drinkingText = computed(() => drinkingOptions.value[formData.value.drinkingHistory] || $t('user.drinkingOptions.never'))
	const sleepText = computed(() => sleepOptions.value[formData.value.sleepDuration] || $t('user.sleepOptions.normal'))
	const sleepQualityText = computed(() => sleepQualityOptions.value[formData.value.sleepQuality] || $t('user.sleepQualityOptions.veryGood'))
	const stressText = computed(() => stressOptions.value[formData.value.stressLevel] || $t('user.stressOptions.veryLow'))
	const menstrualCycleText = computed(() => menstrualCycleOptions.value[formData.value.menstrualCycle] || $t('user.menstrualCycleOptions.regular'))
	const birthCountText = computed(() => birthCountOptions.value[formData.value.birthCount] || $t('user.birthCountOptions.zero'))

	// ===== API数据获取 =====
	/**
	 * 从服务器获取用户资料
	 */
	// 📡 从服务器获取用户完整资料
	const fetchUserProfile = async () => {
		try {
			console.log('🚀 调用用户资料接口: https://appdava.sulmas.com.cn/applet/v1/app/profile')

			const response = await userinfo()
			console.log('✅ 用户资料API响应:', response)

			if (response && response.code === 200 && response.data) {
				const profileData = response.data
				console.log('📋 服务器返回的用户资料:', profileData)

				// 🖼️ 特别记录头像信息
				if (profileData.avatar) {
					console.log('🖼️ 获取到头像:', profileData.avatar)
				}

				// 👤 特别记录基本信息
				console.log('👤 基本信息:', {
					nickname: profileData.nickname,
					sex: profileData.sex,
					avatar: profileData.avatar,
					birthday: profileData.birthday
				})

				// 将API数据映射到表单数据
				return {
					// 基本信息
					nickname: profileData.nickname || '',
					gender: profileData.sex || 0, // API返回sex，映射到gender
					birthday: profileData.birthday || '',
					height: profileData.height || '',
					weight: profileData.weight || '',
					bloodType: profileData.blood_type || 0,
					location: profileData.location || '',
					avatar: profileData.avatar || '',

					// 健康信息
					hasAllergy: profileData.has_allergy || false,
					allergens: profileData.allergens || [],
					otherAllergies: profileData.other_allergies || '',
					hasMedication: profileData.has_medication || false,
					medicationList: profileData.medication_list || '',
					hasChronicDisease: profileData.has_chronic_disease || false,
					chronicDiseases: profileData.chronic_diseases || [],
					otherChronicDiseases: profileData.other_chronic_diseases || '',
					hypertensionRange: profileData.hypertension_range || '',
					diabetesRange: profileData.diabetes_range || '',
					hasSurgery: profileData.has_surgery || false,
					surgeryDetails: profileData.surgery_details || '',
					familyDiseases: profileData.family_diseases || [],
					otherFamilyHistory: profileData.other_family_history || '',

					// 生活方式
					exerciseFrequency: profileData.exercise_frequency || 0,
					dietPreferences: profileData.diet_preferences || [],
					smokingHistory: profileData.smoking_history || 0,
					drinkingHistory: profileData.drinking_history || 0,
					sleepDuration: profileData.sleep_duration || 0,
					sleepQuality: profileData.sleep_quality || 0,
					stressLevel: profileData.stress_level || 0,

					// 女性健康信息
					isMenopause: profileData.is_menopause || false,
					menstrualCycle: profileData.menstrual_cycle || 0,
					hasPregnancy: profileData.has_pregnancy || false,
					birthCount: profileData.birth_count || 0
				}
			} else {
				console.log('⚠️ API返回数据格式异常:', response)
				return null
			}
		} catch (error) {
			console.error('❌ 获取用户资料失败:', error)
			// 不显示错误提示，静默失败，使用本地数据
			return null
		}
	}

	// ===== 页面生命周期 =====
	// 初始化表单数据 - 优先从API获取，fallback到本地数据
	onMounted(async () => {
		// 第一步：尝试从API获取用户资料
		const apiUserData = await fetchUserProfile()

		// 第二步：获取本地用户信息作为备用
		const currentUserInfo = getCurrentUserInfo()
		const localUserInfo = { ...userInfo.value, ...currentUserInfo }

		// 第三步：合并数据，API数据优先，本地数据作为fallback
		const combinedUserInfo = apiUserData ? { ...localUserInfo, ...apiUserData } : localUserInfo

		if (combinedUserInfo) {
			formData.value = {
				// 基本信息 - 优先使用微信昵称
				nickname: combinedUserInfo.nickname || combinedUserInfo.name || '',
				gender: combinedUserInfo.gender || 0,
				birthday: combinedUserInfo.birthday || '',
				height: combinedUserInfo.height || '',
				weight: combinedUserInfo.weight || '',
				bloodType: combinedUserInfo.bloodType || 0,
				location: combinedUserInfo.location || '',

				// 健康信息
				hasAllergy: combinedUserInfo.hasAllergy || false,
				hasMedication: combinedUserInfo.hasMedication || false,
				hasChronicDisease: combinedUserInfo.hasChronicDisease || false,
				hasSurgery: combinedUserInfo.hasSurgery || false,
				familyDiseases: combinedUserInfo.familyDiseases || [],
				otherFamilyHistory: combinedUserInfo.otherFamilyHistory || '',

				// 生活方式
				exerciseFrequency: combinedUserInfo.exerciseFrequency || 0,
				dietPreferences: combinedUserInfo.dietPreferences || [],
				smokingHistory: combinedUserInfo.smokingHistory || 0,
				drinkingHistory: combinedUserInfo.drinkingHistory || 0,
				sleepDuration: combinedUserInfo.sleepDuration || 0,
				sleepQuality: combinedUserInfo.sleepQuality || 0,
				stressLevel: combinedUserInfo.stressLevel || 0
			}
		}

		// 初始化健康信息数据
		if (combinedUserInfo) {
			allergens.value = combinedUserInfo.allergens || []
			otherAllergies.value = combinedUserInfo.otherAllergies || ''
			medicationList.value = combinedUserInfo.medicationList || ''
			chronicDiseases.value = combinedUserInfo.chronicDiseases || []
			otherChronicDiseases.value = combinedUserInfo.otherChronicDiseases || ''
			surgeryDetails.value = combinedUserInfo.surgeryDetails || ''

			// 初始化慢性病详细数值
			hypertensionRange.value = combinedUserInfo.hypertensionRange || ''
			diabetesRange.value = combinedUserInfo.diabetesRange || ''
		}

		// 🖼️ 初始化头像显示
		if (combinedUserInfo && combinedUserInfo.avatar) {
			// 处理头像URL，确保使用完整路径
			const processedAvatar = processAvatarUrl(combinedUserInfo.avatar)
			console.log('🔗 处理头像URL:', combinedUserInfo.avatar, '->', processedAvatar)

			// 设置头像显示
			localAvatar.value = processedAvatar
			hasAvatarChanged.value = false
			console.log('✅ 头像已设置到页面显示')
		} else {
			console.log('⚠️ 未获取到头像信息，将显示默认头像')
		}

		// 📋 显示初始化结果
		console.log('📋 表单数据初始化完成:')
		console.log('  👤 昵称:', formData.value.nickname)
		console.log('  👫 性别:', formData.value.gender === 1 ? '男' : formData.value.gender === 2 ? '女' : '未设置')
		console.log('  🖼️ 头像:', localAvatar.value || '无')
		console.log('  🎂 生日:', formData.value.birthday || '未设置')
		console.log('  📊 数据来源:', apiUserData ? 'API服务器' : '本地存储')
		console.log('🏥 慢性病数据初始化完成:', {
			chronicDiseases: chronicDiseases.value,
			hypertensionRange: hypertensionRange.value,
			diabetesRange: diabetesRange.value
		})

		// 监听昵称更新事件（如果其他地方更新了昵称）
		uni.$on('nicknameUpdated', (newNickname) => {
			console.log('收到昵称更新事件:', newNickname)
			formData.value.nickname = newNickname
		})
	})

	// 页面卸载时移除监听
	onUnmounted(() => {
		uni.$off('nicknameUpdated')
	})

	// ===== 头像相关方法 =====
	// 🖼️ 选择头像功能 - 只选择本地图片，不上传
	const changeAvatar = () => {
		// 选择图片
		uni.chooseImage({
			count: 1,
			success: (res) => {
				const imagePath = res.tempFilePaths[0]

				// 保存本地文件路径和显示路径
				localAvatarFile.value = imagePath  // 保存文件路径，用于后续上传
				localAvatar.value = imagePath      // 保存显示路径，用于预览
				hasAvatarChanged.value = true      // 标记已选择新头像

				// 显示成功提示
				uni.showToast({ title: '头像已选择，点击保存上传', icon: 'success' })
			},
			fail: () => {
				uni.showToast({ title: '选择图片失败', icon: 'none' })
			}
		})
	}

	/**
	 * 处理头像加载错误
	 */
	const handleAvatarError = (e) => {
		console.log('头像加载错误:', e)
		// 可以设置默认头像或重新选择
	}

	// ===== 选择器相关方法 =====
	/**
	 * 通用选择器方法 - 减少重复代码
	 * @param {string} field - 表单字段名
	 * @param {number} value - 选择的值
	 * @param {Function} closeModal - 关闭弹窗的函数（可选）
	 */
	const selectOption = (field, value, closeModal = null) => {
		formData.value[field] = value
		if (closeModal && typeof closeModal === 'function') {
			closeModal()
		}
	}

	/**
	 * 显示性别选择弹窗
	 */
	const showGenderPicker = () => {
		showGenderModal.value = true
	}

	/**
	 * 选择性别
	 * @param {number} index - 性别索引 (0: 未设置, 1: 男, 2: 女)
	 */
	const selectGender = (index) => {
		selectOption('gender', index, () => {
			showGenderModal.value = false
		})
	}

	/**
	 * 显示日期选择器
	 */
	const showDatePicker = () => {
		// 初始化选择的日期为当前生日值
		selectedDate.value = formData.value.birthday || ''
		showCalendarModal.value = true
	}

	// ===== 日期和定位相关状态 =====
	const selectedDate = ref('') // 当前选择的日期
	const isLocating = ref(false) // 是否正在定位
	const regionValue = ref(['', '', '']) // 地区选择器的值 [省, 市, 区]
	const regionText = ref('请选择地区') // 地区显示文本

	// 日历选择回调
	const onCalendarChange = (e) => {
		const { value } = e
		if (value) {
			selectedDate.value = value
		}
	}

	// 确认选择日期
	const onCalendarConfirm = () => {
		if (selectedDate.value) {
			formData.value.birthday = selectedDate.value
		}
		showCalendarModal.value = false
	}

	// 取消日历选择
	const onCalendarCancel = () => {
		showCalendarModal.value = false
		selectedDate.value = ''
	}

	const showBloodTypePicker = () => {
		showBloodTypeModal.value = true
	}

	const selectBloodType = (index) => {
		selectOption('bloodType', index, () => {
			showBloodTypeModal.value = false
		})
	}

	// 地区选择处理
	const onRegionChange = (e) => {
		console.log('地区选择变化:', e)
		const { value } = e.detail

		// 更新选择的值
		regionValue.value = value

		// 更新显示文本和表单数据
		if (value && value.length === 3 && value[0] && value[1] && value[2]) {
			const locationText = `${value[0]} ${value[1]} ${value[2]}`
			regionText.value = locationText
			formData.value.location = locationText

			console.log('地区选择完成:', {
				province: value[0],
				city: value[1],
				district: value[2]
			})

			uni.showToast({
				title: $t('user.messages.regionSelectSuccess'),
				icon: 'success',
				duration: 1500
			})
		} else {
			regionText.value = '请选择地区'
			formData.value.location = ''
		}
	}

	// 获取当前位置
	const getCurrentLocation = () => {
		if (isLocating.value) return

		// 先检查定位权限
		checkLocationPermission()
	}

	// 检查定位权限
	const checkLocationPermission = () => {
		// #ifdef MP-WEIXIN
		// 微信小程序权限检查
		uni.getSetting({
			success: (res) => {
				if (res.authSetting['scope.userLocation']) {
					// 已授权，直接获取位置
					getLocationData()
				} else if (res.authSetting['scope.userLocation'] === false) {
					// 用户拒绝过授权，需要引导用户手动开启
					uni.showModal({
						title: $t('user.messages.locationPermissionTitle'),
						content: $t('user.messages.locationPermissionContent'),
						confirmText: $t('user.messages.goToSettings'),
						success: (modalRes) => {
							if (modalRes.confirm) {
								uni.openSetting()
							}
						}
					})
				} else {
					// 未授权过，先显示说明再申请授权
					uni.showModal({
						title: $t('user.messages.locationPermissionRequest'),
						content: $t('user.messages.locationPermissionDesc'),
						confirmText: $t('user.messages.agree'),
						cancelText: $t('user.messages.refuse'),
						success: (modalRes) => {
							if (modalRes.confirm) {
								uni.authorize({
									scope: 'scope.userLocation',
									success: () => {
										getLocationData()
									},
									fail: () => {
										uni.showToast({
											title: $t('user.messages.locationPermissionDenied'),
											icon: 'none'
										})
									}
								})
							} else {
								uni.showToast({
									title: $t('user.messages.manualAddressInput'),
									icon: 'none'
								})
							}
						}
					})
				}
			}
		})
		// #endif

		// #ifdef APP-PLUS
		// App端直接获取位置
		getLocationData()
		// #endif

		// #ifdef H5
		// H5端直接获取位置
		getLocationData()
		// #endif
	}

	// 获取位置数据
	const getLocationData = () => {
		isLocating.value = true

		uni.showLoading({
			title: $t('user.messages.locating')
		})

		// 获取当前位置
		uni.getLocation({
			type: 'gcj02', // 返回可以用于uni.openLocation的经纬度
			success: (res) => {
				console.log('获取位置成功:', res)
				// 调用逆地理编码获取地址信息
				reverseGeocode(res.latitude, res.longitude)
			},
			fail: (err) => {
				console.error('获取位置失败:', err)
				uni.hideLoading()
				isLocating.value = false

				// 处理不同的错误情况
				let errorMsg = '定位失败'
				if (err.errMsg) {
					if (err.errMsg.includes('auth deny') || err.errMsg.includes('authorize')) {
						errorMsg = '定位权限被拒绝，请在设置中开启定位权限'
					} else if (err.errMsg.includes('timeout')) {
						errorMsg = '定位超时，请检查网络连接'
					} else if (err.errMsg.includes('system deny')) {
						errorMsg = '系统拒绝定位，请检查设备定位设置'
					} else if (err.errMsg.includes('requiredPrivateInfos')) {
						errorMsg = '定位功能配置错误，请联系开发者'
					}
				}

				uni.showModal({
					title: $t('user.messages.locationFailed'),
					content: errorMsg,
					showCancel: false,
					confirmText: $t('common.confirm')
				})
			}
		})
	}

	// 逆地理编码 - 将经纬度转换为地址
	const reverseGeocode = (latitude, longitude) => {
		// 方案1：使用腾讯地图API（推荐）
		// 需要申请腾讯地图API密钥：https://lbs.qq.com/
		// const key = 'YOUR_TENCENT_MAP_KEY'
		// const url = `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=${key}&get_poi=1`

		// 方案2：使用百度地图API
		// 需要申请百度地图API密钥：https://lbsyun.baidu.com/
		// const ak = 'YOUR_BAIDU_MAP_AK'
		// const url = `https://api.map.baidu.com/reverse_geocoding/v3/?ak=${ak}&output=json&coordtype=gcj02ll&location=${latitude},${longitude}`

		// 方案3：通过后端API调用（推荐用于生产环境）
		// uni.request({
		//     url: 'https://your-backend-api.com/geocode',
		//     method: 'POST',
		//     data: { latitude, longitude },
		//     success: (res) => {
		//         if (res.data.success) {
		//             formData.value.location = res.data.address
		//             showLocationSuccess()
		//         } else {
		//             showLocationError('地址解析失败')
		//         }
		//     },
		//     fail: () => {
		//         showLocationError('网络请求失败')
		//     }
		// })

		// 当前使用模拟数据（仅用于演示）
		setTimeout(() => {
			const mockAddress = generateMockAddress(latitude, longitude)
			formData.value.location = mockAddress
			regionText.value = mockAddress

			// 尝试解析地址并更新地区选择器的值
			const addressParts = mockAddress.split(' ')
			if (addressParts.length >= 2) {
				regionValue.value = [addressParts[0] || '', addressParts[1] || '', addressParts[2] || '']
			}

			showLocationSuccess()
		}, 1500)
	}

	// 定位成功处理
	const showLocationSuccess = () => {
		uni.hideLoading()
		isLocating.value = false

		uni.showToast({
			title: $t('user.messages.locationSuccess'),
			icon: 'success',
			duration: 2000
		})
	}



	// 生成模拟地址（基于经纬度生成合理的地址）
	const generateMockAddress = (latitude, longitude) => {
		// 中国主要城市的经纬度范围
		const cities = [
			{ name: '北京市', lat: [39.4, 40.4], lng: [115.7, 117.4], districts: ['东城区', '西城区', '朝阳区', '海淀区', '丰台区', '石景山区'] },
			{ name: '上海市', lat: [30.7, 31.9], lng: [120.9, 122.0], districts: ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区'] },
			{ name: '广州市', lat: [22.7, 23.8], lng: [112.9, 114.0], districts: ['越秀区', '荔湾区', '海珠区', '天河区', '白云区', '黄埔区'] },
			{ name: '深圳市', lat: [22.4, 22.8], lng: [113.7, 114.6], districts: ['罗湖区', '福田区', '南山区', '宝安区', '龙岗区', '盐田区'] },
			{ name: '杭州市', lat: [29.9, 30.6], lng: [119.7, 120.9], districts: ['上城区', '下城区', '江干区', '拱墅区', '西湖区', '滨江区'] },
			{ name: '成都市', lat: [30.1, 31.0], lng: [103.5, 104.9], districts: ['锦江区', '青羊区', '金牛区', '武侯区', '成华区', '龙泉驿区'] },
			{ name: '西安市', lat: [33.8, 34.8], lng: [108.2, 109.8], districts: ['新城区', '碑林区', '莲湖区', '灞桥区', '未央区', '雁塔区'] },
			{ name: '乌鲁木齐市', lat: [43.2, 44.2], lng: [86.5, 88.5], districts: ['天山区', '沙依巴克区', '新市区', '水磨沟区', '头屯河区', '达坂城区'] }
		]

		// 查找匹配的城市
		for (let city of cities) {
			if (latitude >= city.lat[0] && latitude <= city.lat[1] &&
				longitude >= city.lng[0] && longitude <= city.lng[1]) {
				const randomDistrict = city.districts[Math.floor(Math.random() * city.districts.length)]
				return `${city.name} ${randomDistrict}`
			}
		}

		// 如果没有匹配的城市，返回默认地址
		return '新疆维吾尔自治区 乌鲁木齐市 天山区'
	}

	const showExercisePicker = () => {
		uni.showActionSheet({
			itemList: exerciseOptions.value,
			success: (res) => {
				formData.value.exerciseFrequency = res.tapIndex
			}
		})
	}

	const showSmokingPicker = () => {
		uni.showActionSheet({
			itemList: smokingOptions.value,
			success: (res) => {
				formData.value.smokingHistory = res.tapIndex
			}
		})
	}

	const showDrinkingPicker = () => {
		uni.showActionSheet({
			itemList: drinkingOptions.value,
			success: (res) => {
				formData.value.drinkingHistory = res.tapIndex
			}
		})
	}

	const showSleepPicker = () => {
		uni.showActionSheet({
			itemList: sleepOptions.value,
			success: (res) => {
				formData.value.sleepDuration = res.tapIndex
			}
		})
	}

	const showSleepQualityPicker = () => {
		uni.showActionSheet({
			itemList: sleepQualityOptions.value,
			success: (res) => {
				formData.value.sleepQuality = res.tapIndex
			}
		})
	}

	const showStressPicker = () => {
		uni.showActionSheet({
			itemList: stressOptions.value,
			success: (res) => {
				formData.value.stressLevel = res.tapIndex
			}
		})
	}

	// 女性健康选择器方法
	const showMenstrualCyclePicker = () => {
		uni.showActionSheet({
			itemList: menstrualCycleOptions.value,
			success: (res) => {
				formData.value.menstrualCycle = res.tapIndex
			}
		})
	}

	const showBirthCountPicker = () => {
		uni.showActionSheet({
			itemList: birthCountOptions.value,
			success: (res) => {
				formData.value.birthCount = res.tapIndex
			}
		})
	}

	// ===== 健康信息设置方法 =====
	/**
	 * 设置过敏史状态
	 * @param {boolean} value - 是否有过敏史
	 */
	const setAllergy = (value) => {
		formData.value.hasAllergy = value
		// 如果选择"否"，清空过敏源数据
		if (!value) {
			allergens.value = []
			otherAllergies.value = ''
		}
	}

	/**
	 * 通用切换数组项方法 - 减少重复代码
	 * @param {Array} array - 目标数组
	 * @param {any} item - 要切换的项
	 */
	const toggleArrayItem = (array, item) => {
		const index = array.indexOf(item)
		if (index > -1) {
			array.splice(index, 1) // 取消选择
		} else {
			array.push(item) // 添加选择
		}
	}

	/**
	 * 切换过敏源选择状态
	 * @param {string} allergen - 过敏源名称
	 */
	const toggleAllergen = (allergen) => {
		toggleArrayItem(allergens.value, allergen)
	}

	/**
	 * 设置当前用药状态
	 * @param {boolean} value - 是否在用药
	 */
	const setMedication = (value) => {
		formData.value.hasMedication = value
		// 如果选择"否"，清空用药数据
		if (!value) {
			medicationList.value = ''
		}
	}

	/**
	 * 设置慢性病史状态
	 * @param {boolean} value - 是否有慢性病
	 */
	const setChronicDisease = (value) => {
		formData.value.hasChronicDisease = value
		// 如果选择"否"，清空慢性病数据
		if (!value) {
			chronicDiseases.value = []
			otherChronicDiseases.value = ''
			// 清空慢性病详细数值
			hypertensionRange.value = ''
			diabetesRange.value = ''
		}
	}

	/**
	 * 切换慢性病选择状态
	 * @param {string} disease - 慢性病名称
	 */
	const toggleChronicDisease = (disease) => {
		const wasSelected = chronicDiseases.value.includes(disease)
		toggleArrayItem(chronicDiseases.value, disease)

		// 如果取消选择，清空对应的数值
		if (wasSelected) {
			if (disease === '高血压') {
				hypertensionRange.value = ''
			} else if (disease === '糖尿病') {
				diabetesRange.value = ''
			}
		}
	}

	/**
	 * 设置手术与住院史状态
	 * @param {boolean} value - 是否有手术史
	 */
	const setSurgery = (value) => {
		formData.value.hasSurgery = value
		// 如果选择"否"，清空手术史数据
		if (!value) {
			surgeryDetails.value = ''
		}
	}

	// 女性健康设置方法
	const setMenopause = (value) => {
		formData.value.isMenopause = value
		// 如果已绝经，重置月经周期
		if (value) {
			formData.value.menstrualCycle = 0
		}
	}

	const setPregnancy = (value) => {
		formData.value.hasPregnancy = value
		// 如果未怀孕，重置生育次数
		if (!value) {
			formData.value.birthCount = 0
		}
	}

	/**
	 * 切换家族病史选择状态
	 * @param {string} disease - 家族病史名称
	 */
	const toggleFamilyDisease = (disease) => {
		toggleArrayItem(formData.value.familyDiseases, disease)
	}

	// 饮食偏好切换
	const toggleDietPreference = (preference) => {
		const index = formData.value.dietPreferences.indexOf(preference)
		if (index > -1) {
			formData.value.dietPreferences.splice(index, 1)
		} else {
			formData.value.dietPreferences.push(preference)
		}
	}

	// ===== 保存相关方法 =====

	// 健康档案数据分析函数已移除

	// 健康档案部分更新数据生成函数已移除

	// 健康档案完整数据映射函数已移除

	/**
	 * 保存个人资料
	 * 验证表单数据并保存到用户状态中
	 */
	const saveProfile = async () => {
		try {
			// 验证昵称
			const nicknameValidation = validateNickname(formData.value.nickname)
			if (!nicknameValidation.valid) {
				uni.showToast({
					title: nicknameValidation.message,
					icon: 'none'
				})
				return
			}

			uni.showLoading({
				title: '正在保存用户资料...'
			})

			// 🖼️ 第一步：如果选择了新头像，先上传头像
			let uploadedAvatarUrl = userInfo.value.avatar // 默认使用原头像

			if (hasAvatarChanged.value && localAvatarFile.value) {
				try {
					uni.showLoading({ title: '正在上传头像...' })
					console.log('📤 开始上传头像到服务器...')

					// 上传头像到服务器
					const uploadResult = await uploadUserAvatar(localAvatarFile.value)
					console.log('✅ 头像上传成功:', uploadResult)

					// 处理服务器返回的头像URL
					uploadedAvatarUrl = processAvatarUrl(uploadResult.data.avatar)
					console.log('🖼️ 头像上传完成，URL:', uploadedAvatarUrl)

				} catch (error) {
					uni.hideLoading()
					console.error('❌ 头像上传失败:', error)
					uni.showToast({ title: '头像上传失败，请重试', icon: 'none' })
					return // 头像上传失败就不继续保存
				}
			}

			// 📝 第二步：准备要保存的用户信息
			const saveData = {
				...formData.value,
				nickname: formData.value.nickname.trim(), // 确保昵称去除空格
				name: formData.value.nickname.trim(), // 同步name字段
				// 头像信息 - 使用上传后的头像URL
				avatar: uploadedAvatarUrl,
				// 健康信息
				allergens: allergens.value,
				otherAllergies: otherAllergies.value,
				medicationList: medicationList.value,
				chronicDiseases: chronicDiseases.value,
				otherChronicDiseases: otherChronicDiseases.value,
				surgeryDetails: surgeryDetails.value,
				// 慢性病详细数值
				hypertensionRange: hypertensionRange.value,
				diabetesRange: diabetesRange.value,
				updateTime: new Date().toISOString()
			}

			// 健康档案API相关代码已移除 - 直接保存到本地
			console.log('💾 直接保存到本地存储...');
			uni.showLoading({ title: '保存到本地...' });

			// 第二步：保存到本地（无论API是否成功都要执行）
			console.log('💾 开始保存到本地...');

			// 直接更新appStore，与My.vue保持一致
			appStore.setUserInfo({
				...userInfo.value,
				...saveData,
				auth: true
			})

			// 同时更新本地存储
			uni.setStorageSync('userInfo', {
				nickname: saveData.nickname || saveData.name || '',
				phone: saveData.phone || '未绑定手机号',
				avatar: saveData.avatar || userInfo.value.avatar || '/static/icon/user.svg',
				...saveData
			})

			console.log('✅ 本地状态同步成功:', saveData);

			// 第三步：触发全局事件，通知其他页面更新
			console.log('📢 发送全局事件通知...');
			uni.$emit('userInfoUpdated', saveData);
			uni.$emit('profileUpdated', saveData);

			// 如果昵称有变化，触发昵称更新事件
			if (saveData.nickname) {
				uni.$emit('nicknameUpdated', saveData.nickname);
			}

			// 如果头像有变化，触发头像更新事件并重置状态
			if (hasAvatarChanged.value && saveData.avatar) {
				uni.$emit('avatarUpdated', saveData.avatar);
				console.log('🖼️ 头像已同步到My.vue页面:', saveData.avatar);

				// 重置头像相关状态
				localAvatarFile.value = ''     // 清空本地文件路径
				localAvatar.value = saveData.avatar  // 更新本地显示为服务器头像
				hasAvatarChanged.value = false // 重置更改标记
			}

			uni.hideLoading()
			uni.showToast({
				title: '保存成功！',
				icon: 'success',
				duration: 2000
			})

			console.log('✅ 用户资料保存完成:', {
				server: '服务器保存完成',
				local: '本地状态同步完成',
				basic: '基本信息已保存',
				health: '健康档案已保存',
				lifestyle: '生活方式已保存',
				nickname: saveData.nickname
			});

			// 延迟返回上一页
			setTimeout(() => {
				uni.navigateBack()
			}, 2000)

		} catch (error) {
			console.error('❌ 保存用户信息失败:', error)
			uni.hideLoading()

			// 根据错误类型显示不同的提示
			let errorMessage = $t('user.messages.saveFailed');

			if (error.message) {
				if (error.message.includes('网络')) {
					errorMessage = '网络连接异常，请检查网络后重试';
				} else if (error.message.includes('昵称')) {
					errorMessage = '昵称格式不正确，请重新输入';
				} else if (error.message.includes('服务器')) {
					errorMessage = '服务器繁忙，请稍后重试';
				} else {
					errorMessage = error.message;
				}
			}

			uni.showToast({
				title: errorMessage,
				icon: 'none',
				duration: 3000
			})
		}
	}
</script>

<style lang="scss">
	/* ===== 页面容器 ===== */
	.edit-profile-container {
		min-height: 100vh;
		background: #fcfcfc;
		padding-bottom: 20rpx;
	}

	/* ===== 头像编辑区域 ===== */
	.avatar-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 80rpx 0 30rpx;
		background-color: #f3f9f7;
		margin-bottom: 16rpx;
	}

	.avatar-container {
		position: relative;
		margin-bottom: 0;
	}

	.avatar-circle {
		width: 150rpx;
		height: 150rpx;
		border-radius: 50%;
		background: #11a45d;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden; /* 确保图片不会超出圆形边界 */
		box-shadow: 0 8rpx 24rpx rgba(17, 164, 93, 0.3);
		transition: all 0.3s ease;
	}

	/* 头像图片样式 */
	.avatar-image {
		width: 100%;
		height: 100%;
		border-radius: 50%;
		object-fit: cover; /* 确保图片按比例填充 */
	}

	.camera-badge {
		position: absolute;
		bottom: 0;
		right: 0;
		width: 36rpx;
		height: 36rpx;
		border-radius: 50%;
		background: #1890ff;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 3rpx solid white;
	}

	.avatar-title {
		font-size: 32rpx;
		font-weight: 500;
		color: #109d57;
		margin-top: 40rpx;
		margin-bottom: 8rpx;
	}

	.avatar-subtitle {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 30rpx;
	}



	/* ===== 通用区域样式 ===== */
	.section {
		margin: 0 16rpx 16rpx;
	}

	.section-header {
		display: flex;
		align-items: center;
		padding: 20rpx 24rpx;
		background: #f8f9fa;
		border-radius: 12rpx;
		margin-bottom: 16rpx;
		// 
		
	}

	.section-header-icon {
		width: 48rpx;
		height: 48rpx;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 16rpx;
		
		
	}

	.section-header-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		
	}

	/* 不同区域的图标背景色 */
	.basic-header-icon {
		background: #f6ffed;
	}

	.health-header-icon {
		background: #f6ffed;
	}

	.medical-header-icon {
		background: #e6f7ff;
	}

	.lifestyle-header-icon {
		background: #fff7e6;
	}

	.women-health-header-icon {
		background: #fff0f6;
	}

	/* ===== 基本信息区域 ===== */

	/* 基本信息卡片容器 - 根据设计图优化 */
	.basic-card-container {
		background: white;
		border-radius: 24rpx; /* 增大圆角 */
		padding: 32rpx; /* 增加内边距 */
		margin: 0; /* 移除外边距 */
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04); /* 添加轻微阴影 */
	}

	/* 基本信息表单项样式 */
	.basic-form-item {
		margin-bottom: 32rpx; /* 增加项目间距 */
	}

	.basic-form-item:last-child {
		margin-bottom: 0;
	}

	/* 基本信息行布局（身高体重） */
	.basic-form-row {
		display: flex;
		gap: 24rpx; /* 增加间距 */
		margin-bottom: 32rpx;
	}

	.basic-form-item-half {
		flex: 1;
	}

	/* 基本信息标签样式 */
	.basic-label {
		font-size: 30rpx; /* 增大字体 */
		color: #333;
		margin-bottom: 16rpx; /* 增加下边距 */
		font-weight: 500;
		display: block;
	}

	/* 基本信息标签行（带定位按钮） */
	.basic-label-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
	}

	/* 定位按钮样式 */
	.basic-location-btn {
		display: flex;
		align-items: center;
		gap: 6rpx;
		font-size: 24rpx;
		color: #00b96b; /* 使用统一的绿色 */
		padding: 8rpx 16rpx;
		border: 1rpx solid #00b96b;
		border-radius: 20rpx;
		background: rgba(0, 185, 107, 0.1);
		transition: all 0.3s;
	}

	.basic-location-btn.locating {
		color: #1890ff;
		border-color: #1890ff;
		background: rgba(24, 144, 255, 0.1);
	}

	/* 基本信息输入框容器 */
	.basic-input-container {
		position: relative;
	}

	/* 基本信息输入框样式 - 根据设计图优化 */
	.basic-input-field {
		width: 100%;
		height: 96rpx; /* 增大高度 */
		padding: 0 24rpx;
		border: none; /* 移除边框 */
		border-radius: 16rpx; /* 增大圆角 */
		font-size: 30rpx; /* 增大字体 */
		color: #333;
		background: #f5f5f5; /* 灰色背景 */
		transition: all 0.3s;
		box-sizing: border-box;
	}

	.basic-input-field:focus {
		background: #f0f0f0; /* 聚焦时稍微深一点 */
		outline: none;
	}

	.basic-input-field::placeholder {
		color: #999; /* 调整占位符颜色 */
		font-size: 30rpx;
	}

	/* 基本信息选择器容器 */
	.basic-select-container {
		position: relative;
	}

	/* 基本信息选择器样式 - 根据设计图优化 */
	.basic-select-field {
		display: flex;
		align-items: center;
		height: 96rpx; /* 增大高度 */
		padding: 0 24rpx;
		border: none; /* 移除边框 */
		border-radius: 16rpx; /* 增大圆角 */
		background: #f5f5f5; /* 灰色背景 */
		transition: all 0.3s;
		cursor: pointer;
	}

	.basic-select-field:active {
		background: #f0f0f0; /* 点击时稍微深一点 */
	}

	/* 基本信息选择器图标 */
	.basic-select-icon {
		margin-right: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	/* 基本信息选择器文字 */
	.basic-select-text {
		flex: 1;
		font-size: 30rpx; /* 增大字体 */
		color: #333;
	}

	/* 基本信息区域悬停效果 */
	.basic-card-container:hover {
		transform: translateY(-2rpx); /* 轻微上移效果 */
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08); /* 增强阴影 */
	}

	/* 基本信息输入框和选择器悬停效果 */
	.basic-input-field:hover,
	.basic-select-field:hover {
		background: #eeeeee; /* 悬停时背景稍微深一点 */
	}

	/* 基本信息选择器箭头图标样式 */
	.basic-select-field .fui-icon[name="down"] {
		color: #bfbfbf;
		transition: transform 0.3s ease;
	}

	/* 基本信息选择器激活时箭头旋转 */
	.basic-select-field:active .fui-icon[name="down"] {
		transform: rotate(180deg);
	}

	/* ===== 通用卡片样式 ===== */
	.card {
		background: white;
		border-radius: 16rpx;
		padding: 24rpx;
		margin-bottom: 16rpx;
		border: 1rpx solid #f0f0f0;
		transition: all 0.3s;
	}

	.card.expandable-card.expanded {
		border-color: #52c41a;
		box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.1);
	}

	.card-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 12rpx;
		display: block;
	}

	.card-desc {
		font-size: 24rpx;
		color: #999;
		margin-bottom: 24rpx;
		line-height: 1.4;
		display: block;
	}

	/* ===== 健康信息区域 ===== */

	/* 健康信息卡片样式 - 根据设计图优化 */
	.health-card {
		background: white;
		border-radius: 24rpx; /* 增大圆角，更符合设计图 */
		padding: 32rpx; /* 增加内边距 */
		margin-bottom: 24rpx; /* 增加卡片间距 */
		border: 1rpx solid #f0f0f0;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04); /* 添加轻微阴影 */
		transition: all 0.3s;
	}

	.health-card.expandable-card.expanded {
		border-color: #52c41a;
		box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.1);
	}

	/* 健康信息卡片标题样式 */
	.health-card-title {
		font-size: 32rpx; /* 增大标题字体 */
		font-weight: 600;
		color: #333;
		margin-bottom: 16rpx; /* 增加标题下边距 */
		display: block;
	}

	/* 健康信息卡片描述样式 */
	.health-card-desc {
		font-size: 28rpx; /* 增大描述字体 */
		color: #666; /* 调整描述颜色 */
		margin-bottom: 32rpx; /* 增加描述下边距 */
		line-height: 1.5;
		display: block;
	}

	/* 健康信息单选按钮组样式 */
	.health-radio-group {
		display: flex;
		gap: 60rpx; /* 增大按钮间距 */
		margin-top: 8rpx;
	}

	.health-radio-item {
		display: flex;
		align-items: center;
		cursor: pointer;
		padding: 8rpx 0; /* 增加点击区域 */
	}

	/* 健康信息单选按钮圆圈样式 */
	.health-radio-circle {
		width: 40rpx; /* 增大圆圈尺寸 */
		height: 40rpx;
		border: 3rpx solid #e8e8e8; /* 调整边框颜色和粗细 */
		border-radius: 50%;
		margin-right: 16rpx; /* 增加圆圈与文字间距 */
		position: relative;
		transition: all 0.3s ease;
		background: white;
	}

	/* 健康信息单选按钮激活状态 */
	.health-radio-circle.active {
		border-color: #00b96b; /* 使用更鲜艳的绿色 */
		background: #00b96b;
		transform: scale(1.05); /* 轻微放大效果 */
	}

	/* 健康信息单选按钮激活状态内部圆点 */
	.health-radio-circle.active::after {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 16rpx; /* 增大内部圆点 */
		height: 16rpx;
		background: white;
		border-radius: 50%;
	}

	/* 健康信息单选按钮文字样式 */
	.health-radio-text {
		font-size: 30rpx; /* 增大文字尺寸 */
		color: #333;
		font-weight: 500; /* 增加字体粗细 */
		user-select: none;
		transition: color 0.3s ease; /* 添加颜色过渡效果 */
	}

	/* 健康信息单选按钮激活时的文字样式 */
	.health-radio-item:has(.health-radio-circle.active) .health-radio-text {
		color: #00b96b; /* 激活时文字也变绿 */
	}

	/* 健康信息卡片悬停效果 */
	.health-card:hover {
		transform: translateY(-2rpx); /* 轻微上移效果 */
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08); /* 增强阴影 */
	}

	/* 健康信息单选按钮悬停效果 */
	.health-radio-item:hover .health-radio-circle {
		border-color: #00b96b; /* 悬停时边框变绿 */
		transform: scale(1.02); /* 轻微放大 */
	}

	/* 复选框组 */
	.checkbox-group {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.checkbox-item {
		display: flex;
		align-items: center;
		cursor: pointer;
	}

	.checkbox {
		width: 32rpx;
		height: 32rpx;
		border: 2rpx solid #ddd;
		border-radius: 6rpx;
		margin-right: 16rpx;
		position: relative;
		transition: all 0.3s;
	}

	.checkbox.checked {
		border-color: #4CAF50;
		background: #4CAF50;
	}

	.checkbox.checked::after {
		content: '✓';
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		color: white;
		font-size: 20rpx;
		font-weight: bold;
	}

	.checkbox-text {
		font-size: 28rpx;
		color: #333;
	}

	/* 家族病史 */
	.family-history {
		padding: 30rpx;
	}

	.other-label {
		font-size: 28rpx;
		color: #333;
		margin: 30rpx 0 20rpx;
		font-weight: 500;
	}

	.textarea-container {
		position: relative;
	}

	.textarea-field {
		width: 100%;
		min-height: 120rpx;
		padding: 20rpx;
		border: 1rpx solid #e0e0e0;
		border-radius: 12rpx;
		font-size: 28rpx;
		color: #333;
		background: #f8f9fa;
		resize: none;
	}

	/* ===== 医疗历史区域 ===== */

	/* 医疗历史区域布局样式 */
	.medical-section {
		margin: 0 16rpx 16rpx;
	}

	/* 医疗历史标题区域样式 - 根据设计图优化 */
	.medical-header {
		display: flex;
		align-items: center;
		padding: 24rpx;
		background: #f3f9f7; /* 浅绿色背景 */
		border-radius: 16rpx; /* 增大圆角 */
		margin-bottom: 16rpx;
	}

	/* 医疗历史标题图标样式 */
	.medical-header-icon {
		width: 48rpx; /* 调整图标大小 */
		height: 48rpx;
		background: #e6f7ed; /* 更浅的绿色背景 */
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 16rpx;
	}

	/* 医疗历史标题文字样式 */
	.medical-header-title {
		font-size: 32rpx; /* 增大字体 */
		font-weight: 600;
		color: #333;
	}

	/* 医疗历史卡片样式 - 根据设计图优化 */
	.medical-card {
		background: white;
		border-radius: 24rpx; /* 增大圆角 */
		padding: 32rpx; /* 增加内边距 */
		margin-bottom: 24rpx; /* 增加卡片间距 */
		border: 1rpx solid #f0f0f0;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04); /* 添加轻微阴影 */
		transition: all 0.3s;
	}

	/* 医疗历史卡片标题样式 */
	.medical-card-title {
		font-size: 32rpx; /* 增大标题字体 */
		font-weight: 600;
		color: #333;
		margin-bottom: 16rpx; /* 增加标题下边距 */
		display: block;
	}

	/* 医疗历史卡片描述样式 */
	.medical-card-desc {
		font-size: 28rpx; /* 增大描述字体 */
		color: #666; /* 调整描述颜色 */
		margin-bottom: 32rpx; /* 增加描述下边距 */
		line-height: 1.5;
		display: block;
	}

	/* 医疗历史单选按钮组样式 */
	.medical-radio-group {
		display: flex;
		gap: 60rpx; /* 增大按钮间距 */
		margin-top: 8rpx;
	}

	/* 医疗历史单选按钮项样式 */
	.medical-radio-item {
		display: flex;
		align-items: center;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	/* 医疗历史单选按钮圆圈样式 */
	.medical-radio-circle {
		width: 40rpx; /* 增大圆圈尺寸 */
		height: 40rpx;
		border: 3rpx solid #e8e8e8; /* 调整边框颜色和粗细 */
		border-radius: 50%;
		margin-right: 16rpx; /* 增加圆圈与文字间距 */
		background: white;
		transition: all 0.3s ease;
		position: relative;
	}

	/* 医疗历史单选按钮激活状态 */
	.medical-radio-circle.active {
		border-color: #00b96b; /* 使用统一的绿色 */
		background: #00b96b;
		transform: scale(1.05); /* 轻微放大效果 */
	}

	.medical-radio-circle.active::after {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 16rpx;
		height: 16rpx;
		background: white;
		border-radius: 50%;
	}

	/* 医疗历史单选按钮文字样式 */
	.medical-radio-text {
		font-size: 30rpx; /* 增大文字尺寸 */
		color: #333;
		font-weight: 500; /* 增加字体粗细 */
		user-select: none;
		transition: color 0.3s ease; /* 添加颜色过渡效果 */
	}

	/* 医疗历史复选框组样式 */
	.medical-checkbox-group {
		display: flex;
		flex-direction: column;
		gap: 24rpx; /* 增加选项间距 */
		margin-bottom: 32rpx;
	}

	/* 医疗历史复选框项样式 */
	.medical-checkbox-item {
		display: flex;
		align-items: center;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	/* 医疗历史复选框样式 - 根据设计图优化 */
	.medical-checkbox {
		width: 40rpx; /* 增大复选框尺寸 */
		height: 40rpx;
		border: 3rpx solid #e8e8e8; /* 调整边框颜色和粗细 */
		border-radius: 8rpx; /* 调整圆角 */
		margin-right: 16rpx; /* 增加复选框与文字间距 */
		position: relative;
		transition: all 0.3s ease;
		background: white;
	}

	/* 医疗历史复选框选中状态 */
	.medical-checkbox.checked {
		border-color: #00b96b; /* 使用统一的绿色 */
		background: #00b96b;
		transform: scale(1.05); /* 轻微放大效果 */
	}

	.medical-checkbox.checked::after {
		content: '✓';
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		color: white;
		font-size: 24rpx; /* 增大勾号尺寸 */
		font-weight: bold;
	}

	/* 医疗历史复选框文字样式 */
	.medical-checkbox-text {
		font-size: 30rpx; /* 增大文字尺寸 */
		color: #333;
		font-weight: 500; /* 增加字体粗细 */
		user-select: none;
		transition: color 0.3s ease; /* 添加颜色过渡效果 */
	}

	/* 医疗历史其他标签样式 */
	.medical-other-label {
		font-size: 30rpx; /* 增大字体 */
		color: #333;
		margin-bottom: 16rpx; /* 增加下边距 */
		margin-top: 16rpx; /* 增加上边距 */
		font-weight: 500;
		display: block;
	}

	/* 医疗历史文本区域容器 */
	.medical-textarea-container {
		position: relative;
	}

	/* 医疗历史文本区域样式 - 根据设计图优化 */
	.medical-textarea-field {
		width: 100%;
		min-height: 120rpx; /* 增大最小高度 */
		padding: 24rpx; /* 增加内边距 */
		border: none; /* 移除边框 */
		border-radius: 16rpx; /* 增大圆角 */
		font-size: 30rpx; /* 增大字体 */
		color: #333;
		background: #f5f5f5; /* 灰色背景 */
		transition: all 0.3s;
		box-sizing: border-box;
		resize: none; /* 禁止调整大小 */
		line-height: 1.5;
	}

	.medical-textarea-field:focus {
		background: #f0f0f0; /* 聚焦时稍微深一点 */
		outline: none;
	}

	.medical-textarea-field::placeholder {
		color: #999; /* 调整占位符颜色 */
		font-size: 30rpx;
	}

	/* 医疗历史详情副标题样式 */
	.medical-details-subtitle {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 16rpx;
		margin-top: 32rpx;
		display: block;
		font-weight: 500;
	}

	/* 医疗历史卡片悬停效果 */
	.medical-card:hover {
		transform: translateY(-2rpx); /* 轻微上移效果 */
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08); /* 增强阴影 */
	}

	/* 医疗历史单选按钮和复选框悬停效果 */
	.medical-radio-item:hover .medical-radio-circle,
	.medical-checkbox-item:hover .medical-checkbox {
		border-color: #00b96b; /* 悬停时边框变绿 */
		transform: scale(1.02); /* 轻微放大 */
	}

	/* 医疗历史文本区域悬停效果 */
	.medical-textarea-field:hover {
		background: #eeeeee; /* 悬停时背景稍微深一点 */
	}

	/* 医疗历史其他补充 */
	.medical-other-label {
		font-size: 30rpx;
		color: #333;
		margin: 32rpx 0 20rpx;
		font-weight: 600;
		display: block;
	}

	.medical-textarea-container {
		position: relative;
	}

	.medical-textarea-field {
		width: 100%;
		min-height: 140rpx;
		padding: 24rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 16rpx;
		font-size: 28rpx;
		color: #333;
		background: #f8f9fa;
		resize: none;
		line-height: 1.5;
	}

	/* ===== 生活方式区域 ===== */

	/* 生活方式区域布局样式 */
	.lifestyle-section {
		margin: 0 16rpx 16rpx;
	}

	/* 生活方式标题区域样式 - 根据设计图优化 */
	.lifestyle-header {
		display: flex;
		align-items: center;
		padding: 24rpx;
		background: #f3f9f7; /* 浅绿色背景 */
		border-radius: 16rpx; /* 增大圆角 */
		margin-bottom: 16rpx;
	}

	/* 生活方式标题图标样式 */
	.lifestyle-header-icon {
		width: 48rpx; /* 调整图标大小 */
		height: 48rpx;
		background: #e6f7ed; /* 更浅的绿色背景 */
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 16rpx;
	}

	/* 生活方式标题文字样式 */
	.lifestyle-header-title {
		font-size: 32rpx; /* 增大字体 */
		font-weight: 600;
		color: #333;
	}

	/* 生活方式卡片容器样式 - 根据设计图优化 */
	.lifestyle-card-container {
		background: white;
		border-radius: 24rpx; /* 增大圆角 */
		padding: 32rpx; /* 增加内边距 */
		margin: 0; /* 移除外边距 */
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04); /* 添加轻微阴影 */
	}

	/* 生活方式卡片标题样式 */
	.lifestyle-card-title {
		font-size: 32rpx; /* 增大标题字体 */
		font-weight: 600;
		color: #333;
		margin-bottom: 32rpx; /* 增加标题下边距 */
		display: block;
	}

	/* 生活方式表单项样式 */
	.lifestyle-form-item {
		margin-bottom: 32rpx; /* 增加项目间距 */
	}

	.lifestyle-form-item:last-child {
		margin-bottom: 0;
	}

	/* 生活方式标签样式 */
	.lifestyle-label {
		font-size: 30rpx; /* 增大字体 */
		color: #333;
		margin-bottom: 16rpx; /* 增加下边距 */
		font-weight: 500;
		display: block;
	}

	/* 生活方式选择器容器 */
	.lifestyle-select-container {
		position: relative;
		cursor: pointer;
	}

	/* 生活方式选择器样式 - 根据设计图优化 */
	.lifestyle-select-field {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 96rpx; /* 增大高度 */
		padding: 0 24rpx;
		border: none; /* 移除边框 */
		border-radius: 16rpx; /* 增大圆角 */
		background: #f5f5f5; /* 灰色背景 */
		transition: all 0.3s;
		cursor: pointer;
	}

	.lifestyle-select-field:active {
		background: #f0f0f0; /* 点击时稍微深一点 */
	}

	/* 生活方式选择器文字 */
	.lifestyle-select-text {
		font-size: 30rpx; /* 增大字体 */
		color: #333;
		flex: 1;
	}

	/* 生活方式复选框组样式 */
	.lifestyle-checkbox-group {
		display: flex;
		flex-direction: column;
		gap: 24rpx; /* 增加选项间距 */
	}

	/* 生活方式复选框项样式 */
	.lifestyle-checkbox-item {
		display: flex;
		align-items: center;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	/* 生活方式复选框样式 - 根据设计图优化 */
	.lifestyle-checkbox {
		width: 40rpx; /* 增大复选框尺寸 */
		height: 40rpx;
		border: 3rpx solid #e8e8e8; /* 调整边框颜色和粗细 */
		border-radius: 8rpx; /* 调整圆角 */
		margin-right: 16rpx; /* 增加复选框与文字间距 */
		position: relative;
		transition: all 0.3s ease;
		background: white;
	}

	/* 生活方式复选框选中状态 */
	.lifestyle-checkbox.checked {
		border-color: #00b96b; /* 使用统一的绿色 */
		background: #00b96b;
		transform: scale(1.05); /* 轻微放大效果 */
	}

	.lifestyle-checkbox.checked::after {
		content: '✓';
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		color: white;
		font-size: 24rpx; /* 增大勾号尺寸 */
		font-weight: bold;
	}

	/* 生活方式复选框文字样式 */
	.lifestyle-checkbox-text {
		font-size: 30rpx; /* 增大文字尺寸 */
		color: #333;
		font-weight: 500; /* 增加字体粗细 */
		user-select: none;
		transition: color 0.3s ease; /* 添加颜色过渡效果 */
	}

	/* 生活方式卡片悬停效果 */
	.lifestyle-card-container:hover {
		transform: translateY(-2rpx); /* 轻微上移效果 */
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08); /* 增强阴影 */
	}

	/* 生活方式选择器和复选框悬停效果 */
	.lifestyle-select-field:hover {
		background: #eeeeee; /* 悬停时背景稍微深一点 */
	}

	.lifestyle-checkbox-item:hover .lifestyle-checkbox {
		border-color: #00b96b; /* 悬停时边框变绿 */
		transform: scale(1.02); /* 轻微放大 */
	}

	/* 生活方式选择器箭头图标样式 */
	.lifestyle-select-field .fui-icon[name="down"] {
		color: #bfbfbf;
		transition: transform 0.3s ease;
	}

	/* 生活方式选择器激活时箭头旋转 */
	.lifestyle-select-field:active .fui-icon[name="down"] {
		transform: rotate(180deg);
	}

	/* 基本信息区域布局样式 */
	.basic-section {
		margin: 0 16rpx 16rpx;
	}

	/* 基本信息标题区域样式 - 根据设计图优化 */
	.basic-header {
		display: flex;
		align-items: center;
		padding: 24rpx;
		background: #f3f9f7; /* 浅绿色背景 */
		border-radius: 16rpx; /* 增大圆角 */
		margin-bottom: 16rpx;
	}

	/* 基本信息标题图标样式 */
	.basic-header-icon {
		width: 48rpx; /* 调整图标大小 */
		height: 48rpx;
		background: #e6f7ed; /* 更浅的绿色背景 */
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 16rpx;
	}

	/* 基本信息标题文字样式 */
	.basic-header-title {
		font-size: 32rpx; /* 增大字体 */
		font-weight: 600;
		color: #333;
	}

	/* 基本信息表单项样式 */
	.basic-form-item {
		margin-bottom: 24rpx;
	}

	.basic-form-item:last-child {
		margin-bottom: 0;
	}

	.basic-form-item-half {
		flex: 1;
	}

	/* 弹窗遮罩层 */
	.modal-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 9999;
	}

	/* 弹窗样式 */
	.gender-modal, .bloodtype-modal {
		background: white;
		border-radius: 24rpx;
		padding: 0;
		width: 680rpx;
		max-width: 85vw;
		overflow: hidden;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	}

	.modal-header {
		padding: 48rpx 48rpx 32rpx;
	}

	.modal-title {
		font-size: 36rpx;
		font-weight: 500;
		color: #333;
		text-align: left;
	}

	.modal-content {
		padding: 0 48rpx 32rpx;
	}

	/* 性别选项卡片样式 */
	.gender-option-card {
		display: flex;
		align-items: center;
		padding: 32rpx 40rpx;
		margin-bottom: 24rpx;
		background: #f8f9fa;
		border: 2rpx solid #f0f0f0;
		border-radius: 16rpx;
		position: relative;
		transition: all 0.2s;
	}

	.gender-option-card:last-child {
		margin-bottom: 0;
	}

	.gender-option-card.selected {
		background: #e8f5e8;
		border-color: #52c41a;
	}

	.gender-icon {
		margin-right: 24rpx;
		width: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.gender-text {
		font-size: 32rpx;
		color: #333;
		flex: 1;
	}

	.check-icon {
		width: 48rpx;
		height: 48rpx;
		background: #52c41a;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.modal-footer {
		padding: 0 48rpx 48rpx;
		text-align: right;
	}

	.cancel-text {
		font-size: 32rpx;
		color: #666;
		padding: 16rpx 32rpx;
	}

	/* 可展开卡片样式 */
	.expandable-card {
		transition: all 0.3s ease;
		overflow: hidden;
	}



	/* ===== 健康信息展开内容样式 ===== */

	/* 过敏源详情样式 */
	.allergy-details {
		margin-top: 32rpx;
		padding-top: 32rpx;
		border-top: 1rpx solid #f0f0f0;
		animation: slideDown 0.3s ease;
	}

	@keyframes slideDown {
		from {
			opacity: 0;
			transform: translateY(-20rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.allergy-subtitle {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 24rpx;
		font-weight: 500;
	}

	.allergy-options {
		margin-bottom: 32rpx;
	}

	.allergy-option {
		display: flex;
		align-items: center;
		padding: 24rpx 0;
		border-bottom: 1rpx solid #f8f9fa;
	}

	.allergy-option:last-child {
		border-bottom: none;
	}

	.allergy-checkbox {
		width: 40rpx;
		height: 40rpx;
		border: 2rpx solid #ddd;
		border-radius: 8rpx;
		margin-right: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.2s;
	}

	.allergy-checkbox.checked {
		background: #52c41a;
		border-color: #52c41a;
	}

	.allergy-checkbox.checked::after {
		content: '✓';
		color: white;
		font-size: 24rpx;
		font-weight: bold;
	}

	.allergy-option-text {
		font-size: 30rpx;
		color: #333;
		flex: 1;
	}

	.allergy-input-container {
		margin-top: 16rpx;
	}

	.allergy-input {
		width: 100%;
		min-height: 120rpx;
		padding: 24rpx;
		background: #f8f9fa;
		border: 1rpx solid #e8e8e8;
		border-radius: 16rpx;
		font-size: 28rpx;
		color: #333;
		line-height: 1.5;
		resize: none;
	}

	.allergy-input::placeholder {
		color: #999;
	}

	/* 当前用药详情样式 */
	.medication-details {
		margin-top: 32rpx;
		padding-top: 32rpx;
		border-top: 1rpx solid #f0f0f0;
		animation: slideDown 0.3s ease;
	}

	.medication-input-container {
		margin-top: 16rpx;
	}

	/* 慢性病史详情样式 */
	.chronic-disease-details {
		margin-top: 32rpx;
		padding-top: 32rpx;
		border-top: 1rpx solid #f0f0f0;
		animation: slideDown 0.3s ease;
	}

	/* 慢性病详情卡片样式 */
	.chronic-disease-detail-card {
		margin: 16rpx 0 24rpx 48rpx; /* 左边距对齐复选框后的内容 */
		padding: 24rpx;
		background: #f8fffe; /* 浅绿色背景 */
		border: 2rpx solid #e6f7ed; /* 绿色边框 */
		border-radius: 16rpx;
		animation: slideDown 0.3s ease;
	}

	/* 慢性病详情标签样式 */
	.chronic-detail-label {
		display: block;
		font-size: 28rpx;
		color: #333;
		margin-bottom: 12rpx;
		font-weight: 500;
	}

	/* 慢性病输入容器样式 */
	.chronic-input-container {
		position: relative;
	}

	/* 慢性病输入框样式 */
	.chronic-input-field {
		width: 100%;
		height: 80rpx;
		padding: 0 20rpx;
		border: 2rpx solid #d9f7be; /* 浅绿色边框 */
		border-radius: 12rpx;
		font-size: 28rpx;
		color: #333;
		background: white;
		transition: all 0.3s ease;
		box-sizing: border-box;
	}

	.chronic-input-field:focus {
		border-color: #00b96b; /* 聚焦时绿色边框 */
		background: #fcfffe;
		outline: none;
	}

	.chronic-input-field::placeholder {
		color: #999;
		font-size: 26rpx;
	}

	/* 慢性病详情卡片悬停效果 */
	.chronic-disease-detail-card:hover {
		background: #f0fff4;
		border-color: #b7eb8f;
		transform: translateY(-1rpx);
	}

	/* 手术与住院史详情样式 */
	.surgery-details {
		margin-top: 32rpx;
		padding-top: 32rpx;
		border-top: 1rpx solid #f0f0f0;
		animation: slideDown 0.3s ease;
	}

	/* ===== 女性健康区域 ===== */

	/* 女性健康区域布局样式 */
	.women-health-section {
		margin: 0 16rpx 16rpx;
	}

	/* 女性健康标题区域样式 - 根据设计图优化 */
	.women-health-header {
		display: flex;
		align-items: center;
		padding: 24rpx;
		background: #f3f9f7; /* 浅绿色背景 */
		border-radius: 16rpx; /* 增大圆角 */
		margin-bottom: 16rpx;
	}

	/* 女性健康标题图标样式 */
	.women-health-header-icon {
		width: 48rpx; /* 调整图标大小 */
		height: 48rpx;
		background: #e6f7ed; /* 更浅的绿色背景 */
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 16rpx;
	}

	/* 女性健康标题文字样式 */
	.women-health-header-title {
		font-size: 32rpx; /* 增大字体 */
		font-weight: 600;
		color: #333;
	}

	/* 女性健康卡片容器样式 - 根据设计图优化 */
	.women-health-card-container {
		background: white;
		border-radius: 24rpx; /* 增大圆角 */
		padding: 32rpx; /* 增加内边距 */
		margin: 0; /* 移除外边距 */
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04); /* 添加轻微阴影 */
	}

	/* 女性健康卡片标题样式 */
	.women-health-card-title {
		font-size: 32rpx; /* 增大标题字体 */
		font-weight: 600;
		color: #333;
		margin-bottom: 32rpx; /* 增加标题下边距 */
		display: block;
	}

	/* 女性健康表单项样式 */
	.women-health-form-item {
		margin-bottom: 32rpx; /* 增加项目间距 */
	}

	.women-health-form-item:last-child {
		margin-bottom: 0;
	}

	/* 女性健康标签样式 */
	.women-health-label {
		font-size: 30rpx; /* 增大字体 */
		color: #333;
		margin-bottom: 16rpx; /* 增加下边距 */
		font-weight: 500;
		display: block;
	}

	/* 女性健康单选按钮组样式 - 根据设计图优化 */
	.women-health-radio-group {
		display: flex;
		gap: 80rpx; /* 增大选项间距 */
		align-items: center;
	}

	/* 女性健康单选按钮项样式 */
	.women-health-radio-item {
		display: flex;
		align-items: center;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	/* 女性健康单选按钮圆圈样式 - 根据设计图优化 */
	.women-health-radio-circle {
		width: 48rpx; /* 增大单选按钮尺寸 */
		height: 48rpx;
		border: 4rpx solid #e8e8e8; /* 调整边框颜色和粗细 */
		border-radius: 50%;
		margin-right: 16rpx; /* 增加单选按钮与文字间距 */
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
		background: white;
	}

	/* 女性健康单选按钮选中状态 */
	.women-health-radio-circle.active {
		border-color: #00b96b; /* 使用统一的绿色 */
		background: white; /* 保持白色背景 */
		transform: scale(1.05); /* 轻微放大效果 */
	}

	.women-health-radio-circle.active::after {
		content: '';
		width: 24rpx; /* 增大内圆尺寸 */
		height: 24rpx;
		background: #00b96b; /* 使用统一的绿色 */
		border-radius: 50%;
	}

	/* 女性健康单选按钮文字样式 */
	.women-health-radio-text {
		font-size: 30rpx; /* 增大文字尺寸 */
		color: #333;
		font-weight: 500; /* 增加字体粗细 */
		user-select: none;
		transition: color 0.3s ease; /* 添加颜色过渡效果 */
	}

	/* 女性健康选择器容器 */
	.women-health-select-container {
		position: relative;
		cursor: pointer;
	}

	/* 女性健康选择器样式 - 根据设计图优化 */
	.women-health-select-field {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 96rpx; /* 增大高度 */
		padding: 0 24rpx;
		border: none; /* 移除边框 */
		border-radius: 16rpx; /* 增大圆角 */
		background: #f5f5f5; /* 灰色背景 */
		transition: all 0.3s;
		cursor: pointer;
	}

	.women-health-select-field:active {
		background: #f0f0f0; /* 点击时稍微深一点 */
	}

	/* 女性健康选择器文字 */
	.women-health-select-text {
		font-size: 30rpx; /* 增大字体 */
		color: #333;
		flex: 1;
	}

	/* 女性健康卡片悬停效果 */
	.women-health-card-container:hover {
		transform: translateY(-2rpx); /* 轻微上移效果 */
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08); /* 增强阴影 */
	}

	/* 女性健康选择器和单选按钮悬停效果 */
	.women-health-select-field:hover {
		background: #eeeeee; /* 悬停时背景稍微深一点 */
	}

	.women-health-radio-item:hover .women-health-radio-circle {
		border-color: #00b96b; /* 悬停时边框变绿 */
		transform: scale(1.02); /* 轻微放大 */
	}

	/* 女性健康选择器箭头图标样式 */
	.women-health-select-field .fui-icon[name="down"] {
		color: #bfbfbf;
		transition: transform 0.3s ease;
	}

	/* 女性健康选择器激活时箭头旋转 */
	.women-health-select-field:active .fui-icon[name="down"] {
		transform: rotate(180deg);
	}

	.women-health-arrow-icon {
		font-size: 24rpx;
		color: #666;
		margin-left: 16rpx;
	}

	/* 日历弹窗样式 */
	.calendar-modal-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}

	.calendar-modal {
		width: 680rpx;
		background: white;
		border-radius: 24rpx;
		overflow: hidden;
		animation: modalSlideIn 0.3s ease;
	}

	@keyframes modalSlideIn {
		from {
			opacity: 0;
			transform: scale(0.8);
		}
		to {
			opacity: 1;
			transform: scale(1);
		}
	}

	.calendar-header {
		padding: 40rpx 40rpx 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.calendar-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		text-align: center;
	}

	.calendar-content {
		padding: 20rpx;
	}

	.calendar-footer {
		display: flex;
		border-top: 1rpx solid #f0f0f0;
	}

	.calendar-btn {
		flex: 1;
		padding: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
	}

	.cancel-btn {
		border-right: 1rpx solid #f0f0f0;
	}

	.cancel-btn:active {
		background: #f8f9fa;
	}

	.confirm-btn {
		background: #52c41a;
	}

	.confirm-btn:active {
		background: #389e0d;
	}

	.calendar-btn-text {
		font-size: 32rpx;
		font-weight: 500;
	}

	.cancel-text {
		color: #666;
	}

	.confirm-text {
		color: white;
	}

	/* 固定底部保存按钮样式 */
	.save-button-container {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background: white;
		padding: 20rpx 30rpx;
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		border-top: 1rpx solid #f0f0f0;
		z-index: 1000;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.save-button {
		width: 100%;
		height: 88rpx;
		background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: all 0.3s ease;
		box-shadow: 0 4rpx 16rpx rgba(82, 196, 26, 0.3);
	}

	.save-button:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.4);
	}

	.save-button-text {
		font-size: 32rpx;
		font-weight: 600;
		color: white;
		letter-spacing: 2rpx;
	}

	/* 为有保存按钮的页面添加底部间距 */
	.edit-profile-container.with-save-button {
		padding-bottom: calc(128rpx + env(safe-area-inset-bottom));
	}





	.basic-label {
		font-size: 26rpx;
		font-weight: 500;
		color: #666;
		margin-bottom: 12rpx;
		display: block;
	}

	/* 基本信息输入框样式 */
	.basic-input-container {
		width: 100%;
	}

	.basic-input-field {
		width: 100%;
		padding: 24rpx 20rpx;
		border: 1rpx solid #e8e8e8;
		border-radius: 12rpx;
		font-size: 28rpx;
		color: #333;
		background: white;
		
		box-sizing: border-box;
		height: 80rpx;
	}

	.basic-input-field::placeholder {
		color: #999;
		font-size: 26rpx;
	}

	/* 基本信息选择器样式 */
	.basic-select-container {
		cursor: pointer;
	}

	.basic-select-field {
		display: flex;
		align-items: center;
		padding: 20rpx;
		background: white;
		border: 1rpx solid #e8e8e8;
		border-radius: 12rpx;
		transition: all 0.3s;
		height: 80rpx;
		box-sizing: border-box;
	}

	.basic-select-field:active {
		border-color: #52c41a;
	}

	.basic-select-icon {
		width: 40rpx;
		height: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 16rpx;
		border-radius: 50%;
	}

	/* 性别图标 - 绿色 */
	.basic-select-icon.gender-icon {
		background: #f0f9ff;
		color: #52c41a;
	}

	/* 生日图标 - 蓝色 */
	.basic-select-icon.birthday-icon {
		background: #f0f9ff;
		color: #1890ff;
	}

	/* 血型图标 - 红色 */
	.basic-select-icon.blood-icon {
		background: #fff2f0;
		color: #ff4d4f;
	}

	/* 地址图标 - 绿色 */
	.basic-select-icon.location-icon {
		background: #f0f9ff;
		color: #52c41a;
	}

	.basic-select-text {
		font-size: 28rpx;
		color: #333;
		flex: 1;
		text-align: left;
		margin-left: 8rpx;
	}

	.basic-arrow-icon {
		font-size: 20rpx;
		color: #bfbfbf;
		margin-left: auto;
	}

	/* 基本信息行布局样式 */
	.basic-form-row {
		display: flex;
		gap: 20rpx;
		margin-bottom: 20rpx;
	}

	/* 基本信息地址相关样式 */
	.basic-label-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.basic-location-btn {
		font-size: 26rpx;
		color: #52c41a;
		padding: 8rpx 16rpx;
		border: 1rpx solid #52c41a;
		border-radius: 20rpx;
		background: transparent;
		display: flex;
		align-items: center;
		gap: 8rpx;
		cursor: pointer;
		transition: all 0.3s;
	}

	.basic-location-btn:active {
		background: #f6ffed;
	}

	.basic-location-btn.locating {
		color: #1890ff;
		border-color: #1890ff;
		pointer-events: none;
	}

	.basic-location-btn.locating text:first-child {
		animation: rotate 1s linear infinite;
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	.textarea-field::placeholder {
		color: #ccc;
	}

	/* 输入框样式 */
	.input-field {
		width: 100%;
		height: 80rpx;
		padding: 0 24rpx;
		border: 2rpx solid #4CAF50;
		border-radius: 12rpx;
		font-size: 28rpx;
		color: #333;
		background: white;
	}

	.input-field::placeholder {
		color: #ccc;
	}

	/* 字体大小响应式样式 */
	.font-size-small {
		.label {
			font-size: 26rpx !important;
		}

		.input-field {
			font-size: 26rpx !important;
		}

		.select-text {
			font-size: 26rpx !important;
		}

		.health-header-title {
			font-size: 30rpx !important;
		}

		.health-card-title {
			font-size: 30rpx !important;
		}

		.health-card-desc {
			font-size: 22rpx !important;
		}

		.health-radio-text,
		.medical-radio-text,
		.medical-checkbox-text,
		.radio-text,
		.checkbox-text {
			font-size: 26rpx !important;
		}

		.medical-header-title {
			font-size: 30rpx !important;
		}

		.medical-card-title {
			font-size: 30rpx !important;
		}

		.medical-card-desc {
			font-size: 22rpx !important;
		}

		.medical-other-label {
			font-size: 26rpx !important;
		}

		.lifestyle-header-title {
			font-size: 30rpx !important;
		}

		.lifestyle-card-title {
			font-size: 30rpx !important;
		}

		.lifestyle-label {
			font-size: 26rpx !important;
		}

		.lifestyle-select-text,
		.lifestyle-checkbox-text {
			font-size: 24rpx !important;
		}

		.basic-header-title {
			font-size: 30rpx !important;
		}

		.basic-label {
			font-size: 26rpx !important;
		}

		.basic-input-field,
		.basic-select-text {
			font-size: 24rpx !important;
		}

		.basic-location-btn {
			font-size: 22rpx !important;
		}

		.textarea-field {
			font-size: 26rpx !important;
		}

		.avatar-title {
			font-size: 30rpx !important;
		}

		.avatar-subtitle {
			font-size: 22rpx !important;
		}

		.section-title {
			font-size: 30rpx !important;
		}
	}

	.font-size-medium {
		.label {
			font-size: 28rpx !important;
		}

		.input-field {
			font-size: 28rpx !important;
		}

		.select-text {
			font-size: 28rpx !important;
		}

		.health-header-title {
			font-size: 32rpx !important;
		}

		.health-card-title {
			font-size: 32rpx !important;
		}

		.health-card-desc {
			font-size: 24rpx !important;
		}

		.health-radio-text,
		.medical-radio-text,
		.medical-checkbox-text,
		.radio-text,
		.checkbox-text {
			font-size: 28rpx !important;
		}

		.medical-header-title {
			font-size: 32rpx !important;
		}

		.medical-card-title {
			font-size: 32rpx !important;
		}

		.medical-card-desc {
			font-size: 24rpx !important;
		}

		.medical-other-label {
			font-size: 28rpx !important;
		}

		.lifestyle-header-title {
			font-size: 32rpx !important;
		}

		.lifestyle-card-title {
			font-size: 32rpx !important;
		}

		.lifestyle-label {
			font-size: 28rpx !important;
		}

		.lifestyle-select-text,
		.lifestyle-checkbox-text {
			font-size: 26rpx !important;
		}

		.basic-header-title {
			font-size: 32rpx !important;
		}

		.basic-label {
			font-size: 28rpx !important;
		}

		.basic-input-field,
		.basic-select-text {
			font-size: 26rpx !important;
		}

		.basic-location-btn {
			font-size: 24rpx !important;
		}

		.textarea-field {
			font-size: 28rpx !important;
		}

		.avatar-title {
			font-size: 32rpx !important;
		}

		.avatar-subtitle {
			font-size: 24rpx !important;
		}

		.section-title {
			font-size: 32rpx !important;
		}
	}

	.font-size-large {
		.label {
			font-size: 32rpx !important;
		}

		.input-field {
			font-size: 32rpx !important;
		}

		.select-text {
			font-size: 32rpx !important;
		}

		.health-header-title {
			font-size: 36rpx !important;
		}

		.health-card-title {
			font-size: 36rpx !important;
		}

		.health-card-desc {
			font-size: 28rpx !important;
		}

		.health-radio-text,
		.medical-radio-text,
		.medical-checkbox-text,
		.radio-text,
		.checkbox-text {
			font-size: 32rpx !important;
		}

		.medical-header-title {
			font-size: 36rpx !important;
		}

		.medical-card-title {
			font-size: 36rpx !important;
		}

		.medical-card-desc {
			font-size: 28rpx !important;
		}

		.medical-other-label {
			font-size: 32rpx !important;
		}

		.lifestyle-header-title {
			font-size: 36rpx !important;
		}

		.lifestyle-card-title {
			font-size: 36rpx !important;
		}

		.lifestyle-label {
			font-size: 32rpx !important;
		}

		.lifestyle-select-text,
		.lifestyle-checkbox-text {
			font-size: 30rpx !important;
		}

		.basic-header-title {
			font-size: 36rpx !important;
		}

		.basic-label {
			font-size: 32rpx !important;
		}

		.basic-input-field,
		.basic-select-text {
			font-size: 30rpx !important;
		}

		.basic-location-btn {
			font-size: 28rpx !important;
		}

		.textarea-field {
			font-size: 32rpx !important;
		}

		.avatar-title {
			font-size: 36rpx !important;
		}

		.avatar-subtitle {
			font-size: 28rpx !important;
		}

		.section-title {
			font-size: 36rpx !important;
		}
	}
</style>