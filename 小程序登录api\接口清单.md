# 健康助手小程序API接口清单

## 完整接口列表（按页面分类）

### 1. 用户个人中心页面 (Profile/UserCenter) - 3个接口

| 序号 | 方法 | 接口地址 | 接口名称 | 认证 |
|------|------|---------|---------|------|
| 1.1 | GET | `/applet/v1/app/profile` | 获取用户个人资料 | ✅ |
| 1.2 | POST | `/applet/v1/app/update_profile` | 更新用户个人资料 | ✅ |
| 1.3 | POST | `/applet/v1/app/upload_avatar` | 上传头像 | ✅ |

### 2. VIP会员页面 (VIP/Membership) - 1个接口

| 序号 | 方法 | 接口地址 | 接口名称 | 认证 |
|------|------|---------|---------|------|
| 2.1 | GET | `/applet/index/vip_list` | VIP价格列表 | ❌ |

### 3. 分销员管理页面 (Referrer/Distribution) - 7个接口

| 序号 | 方法 | 接口地址 | 接口名称 | 认证 |
|------|------|---------|---------|------|
| 3.1 | POST | `/applet/user/apply_ref` | 申请成为分销员 | ✅ |
| 3.2 | GET | `/applet/user/get_moneys` | 获取分销员收入信息 | ✅ |
| 3.3 | GET | `/applet/user/get_balance_r` | 获取余额变动记录 | ✅ |
| 3.4 | GET | `/applet/user/l_level_users` | 获取下级用户列表 | ✅ |
| 3.5 | POST | `/applet/user/up_dist_level` | 修改下级分销等级 | ✅ |
| 3.6 | GET | `/applet/user/get_poster` | 获取分销海报列表 | ✅ |
| 3.7 | GET | `/applet/index/dist_level` | 获取分销等级列表 | ❌ |

### 4. AI健康咨询聊天页面 (Chat/Consultation) - 11个接口

| 序号 | 方法 | 接口地址 | 接口名称 | 认证 |
|------|------|---------|---------|------|
| 4.1 | GET | `/applet/v1/chat/conversations` | 获取对话列表 | ✅ |
| 4.2 | GET | `/applet/v1/chat/conversations/{conversationId}/messages` | 获取对话消息 | ✅ |
| 4.3 | POST | `/applet/v1/chat/conversations/{conversationId}/messages` | 发送消息 | ✅ |
| 4.4 | POST | `/applet/v1/chat/conversations` | 创建新对话 | ✅ |
| 4.5 | DELETE | `/applet/v1/chat/conversations/{conversationId}` | 删除对话 | ✅ |
| 4.6 | PUT | `/applet/v1/chat/conversations/{conversationId}/title` | 更新对话标题 | ✅ |
| 4.7 | DELETE | `/applet/v1/chat/conversations/all` | 删除所有对话 | ✅ |
| 4.8 | POST | `/applet/v1/chat/upload_image` | 上传聊天图片 | ✅ |
| 4.9 | POST | `/applet/v1/chat/speech_to_text` | 语音转文字 | ✅ |
| 4.10 | POST | `/applet/v1/trans/tts` | 文本转语音 | ✅ |
| 4.11 | GET | `/applet/v1/trans/tts/languages` | 获取TTS支持的语言列表 | ❌ |

### 5. 医生列表页面 (DoctorList) - 2个接口

| 序号 | 方法 | 接口地址 | 接口名称 | 认证 |
|------|------|---------|---------|------|
| 5.1 | GET | `/applet/v1/doctors` | 获取医生列表 | ❌ |
| 5.2 | GET | `/applet/v1/doctors/with-interaction` | 获取包含互动状态的医生列表 | ✅ |

### 6. 医生详情页面 (DoctorDetail) - 7个接口

| 序号 | 方法 | 接口地址 | 接口名称 | 认证 |
|------|------|---------|---------|------|
| 6.1 | GET | `/applet/v1/doctors/{doctorId}` | 获取单个医生详情 | ❌ |
| 6.2 | POST | `/applet/v1/doctors/{doctorId}/like` | 点赞医生 | ✅ |
| 6.3 | DELETE | `/applet/v1/doctors/{doctorId}/like` | 取消点赞医生 | ✅ |
| 6.4 | POST | `/applet/v1/doctors/{doctorId}/favorite` | 收藏医生 | ✅ |
| 6.5 | DELETE | `/applet/v1/doctors/{doctorId}/favorite` | 取消收藏医生 | ✅ |
| 6.6 | GET | `/applet/v1/doctors/{doctorId}/status` | 获取医生互动状态 | ✅ |
| 6.7 | GET | `/applet/v1/products/doctor/{doctorId}` | 获取医生的产品 | ❌ |

### 7. 我的收藏页面 (MyFavorites) - 2个接口

| 序号 | 方法 | 接口地址 | 接口名称 | 认证 |
|------|------|---------|---------|------|
| 7.1 | GET | `/applet/v1/doctors/favorites` | 获取用户收藏的医生列表 | ✅ |
| 7.2 | GET | `/applet/v1/doctors/likes` | 获取用户点赞的医生列表 | ✅ |

### 8. 医生产品管理页面 (DoctorProductManagement) - 12个接口

| 序号 | 方法 | 接口地址 | 接口名称 | 认证 |
|------|------|---------|---------|------|
| 8.1 | POST | `/applet/v1/doctor/products` | 创建产品 | ✅ |
| 8.2 | GET | `/applet/v1/doctor/products` | 获取产品列表 | ✅ |
| 8.3 | GET | `/applet/v1/doctor/products/{productId}` | 获取产品详情 | ✅ |
| 8.4 | PUT | `/applet/v1/doctor/products/{productId}` | 更新产品 | ✅ |
| 8.5 | DELETE | `/applet/v1/doctor/products/{productId}` | 删除产品 | ✅ |
| 8.6 | GET | `/applet/v1/doctor/products/statistics/overview` | 获取产品统计 | ✅ |
| 8.7 | GET | `/applet/v1/doctor/products/orders/list` | 获取产品订单列表 | ✅ |
| 8.8 | POST | `/applet/v1/doctor/products/upload-image` | 单张产品图片上传 | ✅ |
| 8.9 | POST | `/applet/v1/doctor/products/upload-images` | 批量产品图片上传 | ✅ |
| 8.10 | GET | `/applet/v1/doctor/products/orders/pending-shipment` | 获待发货订单列表 | ✅ |
| 8.11 | POST | `/applet/v1/doctor/products/orders/{orderId}/ship` | 订单发货 | ✅ |
| 8.12 | GET | `/applet/v1/doctor/products/orders/{orderId}/shipping-status` | 获取订单物流状态 | ✅ |

### 9. 产品商城页面 (ProductMall) - 2个接口

| 序号 | 方法 | 接口地址 | 接口名称 | 认证 |
|------|------|---------|---------|------|
| 9.1 | GET | `/applet/v1/products` | 获取产品列表（用户端） | ❌ |
| 9.2 | GET | `/applet/v1/products/categories` | 获取产品分类列表 | ❌ |

### 10. 产品详情页面 (ProductDetail) - 3个接口

| 序号 | 方法 | 接口地址 | 接口名称 | 认证 |
|------|------|---------|---------|------|
| 10.1 | GET | `/applet/v1/products/{productId}` | 获取产品详情（用户端） | ❌ |
| 10.2 | POST | `/applet/v1/cart/add` | 加入购物车 | ✅ |
| 10.3 | POST | `/applet/v1/products/orders` | 创建订单（用户端） | ✅ |

### 11. 购物车页面 (ShoppingCart) - 5个接口

| 序号 | 方法 | 接口地址 | 接口名称 | 认证 |
|------|------|---------|---------|------|
| 11.1 | GET | `/applet/v1/cart` | 获取购物车列表 | ✅ |
| 11.2 | PUT | `/applet/v1/cart/{cartId}` | 更新购物车商品 | ✅ |
| 11.3 | DELETE | `/applet/v1/cart/{cartId}` | 删除购物车商品 | ✅ |
| 11.4 | DELETE | `/applet/v1/cart/clear` | 清空购物车 | ✅ |
| 11.5 | DELETE | `/applet/v1/cart/batch` | 批量删除购物车商品 | ✅ |

### 12. 我的订单页面 (MyOrders) - 5个接口

| 序号 | 方法 | 接口地址 | 接口名称 | 认证 |
|------|------|---------|---------|------|
| 12.1 | GET | `/applet/v1/products/orders/my` | 获取我的订单 | ✅ |
| 12.2 | GET | `/applet/v1/products/orders/{orderId}` | 获取订单详情 | ✅ |
| 12.3 | POST | `/applet/v1/products/orders/{orderId}/cancel` | 取消订单 | ✅ |
| 12.4 | GET | `/applet/v1/products/orders/my/shipped` | 获取我的已发货订单 | ✅ |
| 12.5 | GET | `/applet/v1/products/orders/{orderId}/shipping-status` | 获取订单物流状态 | ✅ |

### 13. 订单支付页面 (OrderPayment) - 3个接口

| 序号 | 方法 | 接口地址 | 接口名称 | 认证 |
|------|------|---------|---------|------|
| 13.1 | POST | `/applet/v1/products/orders/{orderId}/payment` | 创建订单支付 | ✅ |
| 13.2 | GET | `/applet/v1/products/orders/{orderId}/payment/status` | 查询订单支付状态 | ✅ |
| 13.3 | POST | `/applet/v1/products/orders/{orderId}/payment/sync` | 同步订单支付状态 | ✅ |

### 14. 健康档案页面 (HealthProfile) - 4个接口

| 序号 | 方法 | 接口地址 | 接口名称 | 认证 |
|------|------|---------|---------|------|
| 14.1 | GET | `/applet/v1/health-profile` | 获取健康档案 | ✅ |
| 14.2 | POST | `/applet/v1/health-profile` | 创建/完整更新健康档案 | ✅ |
| 14.3 | PATCH | `/applet/v1/health-profile` | 部分更新健康档案 | ✅ |
| 14.4 | DELETE | `/applet/v1/health-profile` | 删除健康档案 | ✅ |

### 15. 地址管理页面 (AddressManagement) - 7个接口

| 序号 | 方法 | 接口地址 | 接口名称 | 认证 |
|------|------|---------|---------|------|
| 15.1 | POST | `/applet/v1/addresses` | 创建用户地址 | ✅ |
| 15.2 | GET | `/applet/v1/addresses` | 获取用户地址列表 | ✅ |
| 15.3 | GET | `/applet/v1/addresses/simple` | 获取简化地址列表 | ✅ |
| 15.4 | GET | `/applet/v1/addresses/{addressId}` | 获取地址详情 | ✅ |
| 15.5 | PUT | `/applet/v1/addresses/{addressId}` | 更新用户地址 | ✅ |
| 15.6 | DELETE | `/applet/v1/addresses/{addressId}` | 删除用户地址 | ✅ |
| 15.7 | POST | `/applet/v1/addresses/set-default` | 设置默认地址 | ✅ |

---

## 统计信息

- **总页面数**: 15个
- **总接口数**: 74个
- **需要认证的接口**: 64个
- **无需认证的接口**: 10个

## 认证说明

- ✅ 表示需要认证（需要在请求头中添加 `Authorization: Bearer {token}`）
- ❌ 表示无需认证（公开接口）
