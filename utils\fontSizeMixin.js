/**
 * 字体大小混入工具
 * 为页面提供统一的字体大小响应功能
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/store/app.js'

export function useFontSize() {
    // 使用全局应用状态
    const appStore = useAppStore()

    // 字体大小类名
    const fontSizeClass = computed(() => {
        return `font-size-${appStore.fontSize}`
    })

    // 字体大小更新key，用于强制更新
    const fontSizeUpdateKey = ref(0)

    // 字体大小变化处理
    const handleFontSizeChange = (data) => {
        console.log('页面接收到字体大小变化:', data.fontSize)
        fontSizeUpdateKey.value++
    }

    // 初始化字体大小监听
    const initFontSizeListener = () => {
        // 监听字体大小变化事件
        uni.$on('fontSizeChanged', handleFontSizeChange)
    }

    // 清理字体大小监听
    const cleanupFontSizeListener = () => {
        // 移除字体大小变化监听
        uni.$off('fontSizeChanged', handleFontSizeChange)
    }

    return {
        appStore,
        fontSizeClass,
        fontSizeUpdateKey,
        handleFontSizeChange,
        initFontSizeListener,
        cleanupFontSizeListener
    }
}

/**
 * 字体大小页面混入
 * 自动处理生命周期
 */
export function useFontSizePage() {
    const fontSizeUtils = useFontSize()

    // 页面挂载时初始化
    onMounted(() => {
        fontSizeUtils.initFontSizeListener()
    })

    // 页面卸载时清理
    onUnmounted(() => {
        fontSizeUtils.cleanupFontSizeListener()
    })

    return fontSizeUtils
}

/**
 * 生成标准的字体大小CSS样式
 * @param {Object} selectors - CSS选择器和对应的字体大小配置
 * @returns {String} - 生成的CSS样式字符串
 */
export function generateFontSizeCSS(selectors) {
    const sizes = {
        small: { ratio: 0.875 }, // 87.5%
        medium: { ratio: 1 },    // 100%
        large: { ratio: 1.125 }  // 112.5%
    }

    let css = ''

    Object.keys(sizes).forEach(size => {
        css += `.font-size-${size} {\n`
        
        Object.keys(selectors).forEach(selector => {
            const baseSize = selectors[selector]
            const adjustedSize = Math.round(baseSize * sizes[size].ratio)
            css += `    ${selector} {\n`
            css += `        font-size: ${adjustedSize}rpx !important;\n`
            css += `    }\n\n`
        })
        
        css += `}\n\n`
    })

    return css
}
