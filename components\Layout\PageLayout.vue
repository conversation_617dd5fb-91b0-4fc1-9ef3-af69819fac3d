<!-- 统一的页面布局组件 -->
<!-- 整合了导航栏、内容区域、底部栏等公共布局逻辑 -->

<template>
  <view class="page-layout" :class="layoutClass" :style="layoutStyle">
    <!-- 自定义导航栏 -->
    <CustomNavbar 
      v-if="showNavbar" 
      v-bind="navbarProps"
      @back="handleBack"
      @action="handleNavbarAction"
    />
    
    <!-- 页面内容区域 -->
    <view class="page-content" :class="contentClass" :style="contentStyle">
      <!-- 页面头部插槽 -->
      <slot name="header" />
      
      <!-- 主要内容区域 -->
      <view class="content-main">
        <slot />
      </view>
      
      <!-- 页面底部插槽 -->
      <slot name="footer" />
    </view>
    
    <!-- 自定义底部栏 -->
    <CustomTabbar v-if="showTabbar" />
    
    <!-- 全局加载遮罩 -->
    <view v-if="loading" class="loading-overlay">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text ug">{{ loadingText }}</text>
      </view>
    </view>
    
    <!-- 全局错误提示 -->
    <view v-if="error" class="error-overlay" @tap="clearError">
      <view class="error-content">
        <text class="error-text ug">{{ error }}</text>
        <view class="error-actions">
          <view class="error-btn" @tap="clearError">
            <text class="ug">{{ $t('common.close') }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed, ref } from 'vue'
import { usePageSetup } from '@/composables/usePageSetup'
import CustomNavbar from '@/components/CustomNavbar/CustomNavbar.vue'
import CustomTabbar from '@/components/CustomTabbar/CustomTabbar.vue'

// ==================== Props定义 ====================
const props = defineProps({
  // 导航栏配置
  showNavbar: {
    type: Boolean,
    default: true
  },
  navbarProps: {
    type: Object,
    default: () => ({})
  },
  
  // 底部栏配置
  showTabbar: {
    type: Boolean,
    default: false
  },
  
  // 页面配置
  pageClass: {
    type: String,
    default: ''
  },
  backgroundColor: {
    type: String,
    default: ''
  },
  
  // 内容区域配置
  contentClass: {
    type: String,
    default: ''
  },
  padding: {
    type: [String, Object],
    default: ''
  },
  
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  loadingText: {
    type: String,
    default: 'common.loading'
  },
  
  // 错误状态
  error: {
    type: String,
    default: ''
  },
  
  // 页面设置选项
  pageOptions: {
    type: Object,
    default: () => ({})
  }
})

// ==================== Events定义 ====================
const emit = defineEmits([
  'back',
  'navbarAction',
  'clearError'
])

// ==================== 页面设置 ====================
const { fontClass, pageStyle, $t } = usePageSetup({
  pageClass: props.pageClass,
  ...props.pageOptions
})

// ==================== 计算属性 ====================
const layoutClass = computed(() => ({
  ...fontClass.value,
  'has-navbar': props.showNavbar,
  'has-tabbar': props.showTabbar,
  'is-loading': props.loading,
  'has-error': !!props.error
}))

const layoutStyle = computed(() => ({
  ...pageStyle.value,
  backgroundColor: props.backgroundColor || undefined
}))

const contentClass = computed(() => ({
  [props.contentClass]: !!props.contentClass,
  'content-with-navbar': props.showNavbar,
  'content-with-tabbar': props.showTabbar
}))

const contentStyle = computed(() => {
  const style = {}
  
  // 处理padding
  if (props.padding) {
    if (typeof props.padding === 'string') {
      style.padding = props.padding
    } else {
      Object.assign(style, props.padding)
    }
  }
  
  return style
})

// ==================== 事件处理 ====================
const handleBack = () => {
  emit('back')
}

const handleNavbarAction = (action) => {
  emit('navbarAction', action)
}

const clearError = () => {
  emit('clearError')
}
</script>

<style lang="scss" scoped>
.page-layout {
  min-height: 100vh;
  background-color: var(--bg-color-page);
  position: relative;
  display: flex;
  flex-direction: column;
  
  // 有导航栏时的样式
  &.has-navbar {
    padding-top: var(--navbar-height);
  }
  
  // 有底部栏时的样式
  &.has-tabbar {
    padding-bottom: var(--tabbar-height);
  }
  
  // 加载状态样式
  &.is-loading {
    overflow: hidden;
  }
}

.page-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; // 确保flex子项可以收缩
  
  // 默认内边距
  padding: var(--page-padding-vertical) var(--page-padding-horizontal);
  
  // 有导航栏时调整内边距
  &.content-with-navbar {
    padding-top: var(--spacing-3);
  }
  
  // 有底部栏时调整内边距
  &.content-with-tabbar {
    padding-bottom: var(--spacing-3);
  }
}

.content-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

// ==================== 加载遮罩样式 ====================
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
}

.loading-content {
  background-color: var(--bg-color-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 200rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-color-light);
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-3);
}

.loading-text {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// ==================== 错误遮罩样式 ====================
.error-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  padding: var(--spacing-4);
}

.error-content {
  background-color: var(--bg-color-white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  max-width: 80%;
  text-align: center;
}

.error-text {
  font-size: var(--font-size-base);
  color: var(--text-color-regular);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-4);
}

.error-actions {
  display: flex;
  justify-content: center;
}

.error-btn {
  background-color: var(--primary-color);
  color: var(--text-color-inverse);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-sm);
  
  &:active {
    opacity: 0.8;
  }
}

// ==================== 响应式适配 ====================
@media (max-width: 750rpx) {
  .page-content {
    padding: var(--spacing-2) var(--spacing-3);
  }
  
  .loading-content {
    padding: var(--spacing-4);
    min-width: 160rpx;
  }
  
  .loading-spinner {
    width: 48rpx;
    height: 48rpx;
  }
}

// ==================== 暗色主题适配 ====================
.dark-theme {
  .loading-content {
    background-color: var(--bg-color-content);
  }
  
  .error-content {
    background-color: var(--bg-color-content);
  }
  
  .loading-text {
    color: var(--text-color-secondary);
  }
  
  .error-text {
    color: var(--text-color-regular);
  }
}

// ==================== RTL布局适配 ====================
.rtl {
  .error-actions {
    flex-direction: row-reverse;
  }
}
</style>
