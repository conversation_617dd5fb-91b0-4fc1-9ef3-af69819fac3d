/* ===== RTL (Right-to-Left) 布局支持样式 ===== */
/* 专门为维吾尔文等RTL语言提供的样式支持 */

/* 基础RTL布局 */
.rtl-container {
  direction: rtl;
  text-align: right;
}

.ltr-container {
  direction: ltr;
  text-align: left;
}

/* Flexbox RTL 支持 */
.rtl-flex {
  display: flex;
  flex-direction: row-reverse;
}

.rtl-flex-column {
  display: flex;
  flex-direction: column;
}

.rtl-flex-center {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: center;
}

.rtl-flex-between {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: space-between;
}

.rtl-flex-start {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: flex-start;
}

.rtl-flex-end {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: flex-end;
}

/* 边距 RTL 支持 */
.rtl-margin-left {
  margin-right: var(--margin-value, 16rpx);
  margin-left: 0;
}

.rtl-margin-right {
  margin-left: var(--margin-value, 16rpx);
  margin-right: 0;
}

.rtl-padding-left {
  padding-right: var(--padding-value, 16rpx);
  padding-left: 0;
}

.rtl-padding-right {
  padding-left: var(--padding-value, 16rpx);
  padding-right: 0;
}

/* 定位 RTL 支持 */
.rtl-absolute-left {
  position: absolute;
  right: var(--position-value, 0);
  left: auto;
}

.rtl-absolute-right {
  position: absolute;
  left: var(--position-value, 0);
  right: auto;
}

.rtl-fixed-left {
  position: fixed;
  right: var(--position-value, 0);
  left: auto;
}

.rtl-fixed-right {
  position: fixed;
  left: var(--position-value, 0);
  right: auto;
}

/* 边框 RTL 支持 */
.rtl-border-left {
  border-right: var(--border-style, 1rpx solid #eee);
  border-left: none;
}

.rtl-border-right {
  border-left: var(--border-style, 1rpx solid #eee);
  border-right: none;
}

/* 圆角 RTL 支持 */
.rtl-radius-left {
  border-top-right-radius: var(--radius-value, 8rpx);
  border-bottom-right-radius: var(--radius-value, 8rpx);
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.rtl-radius-right {
  border-top-left-radius: var(--radius-value, 8rpx);
  border-bottom-left-radius: var(--radius-value, 8rpx);
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

/* 文本对齐 RTL 支持 */
.rtl-text-left {
  text-align: right;
}

.rtl-text-right {
  text-align: left;
}

.rtl-text-center {
  text-align: center;
}

/* 浮动 RTL 支持 */
.rtl-float-left {
  float: right;
}

.rtl-float-right {
  float: left;
}

/* 变换 RTL 支持 */
.rtl-transform-x {
  transform: scaleX(-1);
}

/* 图标 RTL 支持 */
.rtl-icon-arrow-left::before {
  content: '→';
}

.rtl-icon-arrow-right::before {
  content: '←';
}

.rtl-icon-chevron-left::before {
  content: '›';
}

.rtl-icon-chevron-right::before {
  content: '‹';
}

/* 列表 RTL 支持 */
.rtl-list-item {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
}

.rtl-list-icon {
  margin-left: 16rpx;
  margin-right: 0;
}

.rtl-list-content {
  flex: 1;
  text-align: right;
}

.rtl-list-arrow {
  margin-right: 16rpx;
  margin-left: 0;
  transform: scaleX(-1);
}

/* 表单 RTL 支持 */
.rtl-form-item {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
}

.rtl-form-label {
  text-align: right;
  margin-left: 16rpx;
  margin-right: 0;
}

.rtl-form-input {
  text-align: right;
  direction: rtl;
}

.rtl-form-select {
  text-align: right;
  direction: rtl;
}

/* 按钮组 RTL 支持 */
.rtl-btn-group {
  display: flex;
  flex-direction: row-reverse;
}

.rtl-btn-group .btn:first-child {
  border-radius: 0 var(--radius-value, 8rpx) var(--radius-value, 8rpx) 0;
}

.rtl-btn-group .btn:last-child {
  border-radius: var(--radius-value, 8rpx) 0 0 var(--radius-value, 8rpx);
}

/* 导航栏 RTL 支持 */
.rtl-navbar {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: space-between;
}

.rtl-navbar-back {
  order: 3;
}

.rtl-navbar-title {
  order: 2;
  text-align: center;
}

.rtl-navbar-action {
  order: 1;
}

/* 卡片 RTL 支持 */
.rtl-card-header {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: space-between;
}

.rtl-card-content {
  text-align: right;
  direction: rtl;
}

.rtl-card-footer {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: space-between;
}

/* 标签页 RTL 支持 */
.rtl-tabs {
  display: flex;
  flex-direction: row-reverse;
}

.rtl-tab-item {
  text-align: center;
}

/* 步骤条 RTL 支持 */
.rtl-steps {
  display: flex;
  flex-direction: row-reverse;
}

.rtl-step {
  position: relative;
}

.rtl-step::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 100%;
  left: auto;
  width: 100%;
  height: 2rpx;
  background: #eee;
  transform: translateY(-50%);
}

/* 弹窗 RTL 支持 */
.rtl-modal-content {
  text-align: right;
  direction: rtl;
}

.rtl-modal-header {
  text-align: right;
}

.rtl-modal-footer {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
}

/* 下拉菜单 RTL 支持 */
.rtl-dropdown {
  left: auto;
  right: 0;
}

.rtl-dropdown-item {
  text-align: right;
  direction: rtl;
}

/* 消息提示 RTL 支持 */
.rtl-toast {
  text-align: right;
  direction: rtl;
}

/* 进度条 RTL 支持 */
.rtl-progress {
  direction: rtl;
}

.rtl-progress-bar {
  transform: scaleX(-1);
}

.rtl-progress-text {
  text-align: right;
}

/* 网格 RTL 支持 */
.rtl-grid {
  display: grid;
  direction: rtl;
}

.rtl-grid-item {
  text-align: right;
}

/* 响应式 RTL 支持 */
@media (max-width: 768rpx) {
  .rtl-responsive {
    direction: rtl;
    text-align: right;
  }
}

/* 动画 RTL 支持 */
.rtl-slide-left {
  transform: translateX(100%);
}

.rtl-slide-right {
  transform: translateX(-100%);
}

/* 特殊组件 RTL 支持 */
.rtl-swiper {
  direction: rtl;
}

.rtl-picker {
  direction: rtl;
  text-align: right;
}

.rtl-calendar {
  direction: rtl;
}

.rtl-calendar-header {
  flex-direction: row-reverse;
}

.rtl-calendar-body {
  direction: rtl;
}
