// 国际化混入文件
import { t, getCurrentLanguage, getAvailableLanguages } from '@/locale/index.js'
import { useAppStore } from '@/store/app.js'

export default {
  data() {
    return {
      currentLanguage: getCurrentLanguage(),
      // 强制更新的key
      i18nUpdateKey: 0
    }
  },

  computed: {
    // 计算当前语言的字体类
    fontClass() {
      const appStore = useAppStore()
      return {
        'ug': appStore.isUyghur,
        [`lang-${appStore.lang}`]: true
      }
    },

    // 计算当前语言状态
    languageState() {
      const appStore = useAppStore()
      return {
        lang: appStore.lang,
        isUyghur: appStore.isUyghur
      }
    }
  },

  methods: {
    // 翻译方法
    $t(key, lang = null) {
      return t(key, lang)
    },

    // 获取当前语言
    getCurrentLanguage() {
      return getCurrentLanguage()
    },

    // 获取可用语言列表
    getAvailableLanguages() {
      return getAvailableLanguages()
    },

    // 切换语言
    switchLanguage(lang) {
      const appStore = useAppStore()
      appStore.setLanguage(lang)
    },

    // 强制更新国际化内容
    forceUpdateI18n() {
      this.i18nUpdateKey++
      this.currentLanguage = getCurrentLanguage()
      this.$forceUpdate()
    }
  },

  // 监听语言变化
  created() {
    // 监听语言切换事件
    uni.$on('languageChanged', (data) => {
      this.currentLanguage = data.language
      this.forceUpdateI18n()
    })

    // 监听字体变化事件
    uni.$on('languageFontChanged', (data) => {
      this.currentLanguage = data.language
      this.forceUpdateI18n()
    })
  },

  beforeDestroy() {
    // 移除事件监听
    uni.$off('languageChanged')
    uni.$off('languageFontChanged')
  },

  // Vue 3 兼容
  beforeUnmount() {
    // 移除事件监听
    uni.$off('languageChanged')
    uni.$off('languageFontChanged')
  }
}
