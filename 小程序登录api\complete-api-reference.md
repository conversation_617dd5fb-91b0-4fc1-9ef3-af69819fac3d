# 健康助手小程序API接口文档 - 按页面分类

## 目录索引

| 页面模块 | 主要接口 | 接口数量 |
|---------|---------|---------|
| [1. 用户个人中心页面](#1-用户个人中心页面-profileusercenter) | 个人资料管理、头像上传 | 3个 |
| [2. VIP会员页面](#2-vip会员页面-vipmembership) | VIP价格列表 | 1个 |
| [3. 分销员管理页面](#3-分销员管理页面-referrerdistribution) | 分销员申请、收入管理、下级管理 | 7个 |
| [4. AI健康咨询聊天页面](#4-ai健康咨询聊天页面-chatconsultation) | 对话管理、消息发送、语音转换 | 11个 |
| [5. 医生列表页面](#5-医生列表页面-doctorlist) | 医生搜索、筛选 | 2个 |
| [6. 医生详情页面](#6-医生详情页面-doctordetail) | 医生信息、点赞收藏、产品查看 | 7个 |
| [7. 我的收藏页面](#7-我的收藏页面-myfavorites) | 收藏医生、点赞医生列表 | 2个 |
| [8. 医生产品管理页面](#8-医生产品管理页面-doctorproductmanagement) | 产品CRUD、订单管理、发货 | 12个 |
| [9. 产品商城页面](#9-产品商城页面-productmall) | 产品浏览、分类筛选 | 2个 |
| [10. 产品详情页面](#10-产品详情页面-productdetail) | 产品详情、购物车、订单创建 | 3个 |
| [11. 购物车页面](#11-购物车页面-shoppingcart) | 购物车管理、批量操作 | 5个 |
| [12. 我的订单页面](#12-我的订单页面-myorders) | 订单查询、取消、物流跟踪 | 5个 |
| [13. 订单支付页面](#13-订单支付页面-orderpayment) | 支付创建、状态查询 | 3个 |
| [14. 健康档案页面](#14-健康档案页面-healthprofile) | 健康信息管理 | 4个 |
| [15. 地址管理页面](#15-地址管理页面-addressmanagement) | 收货地址CRUD | 7个 |
**总计：74个API接口**

---

## 1. 用户个人中心页面 (Profile/UserCenter)

### 1.1 获取用户个人资料接口
- **接口地址**: `GET /applet/v1/app/profile`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**: 无需请求参数，通过Authorization头中的token识别用户身份
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"id": 1, "nickname": "用户", ...}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 1.2 更新用户个人资料接口
- **接口地址**: `POST /applet/v1/app/update_profile`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "nickname": "新昵称",           // 可选，用户昵称，最大50字符
    "sex": 1,                     // 可选，性别，0:未知 1:男 2:女
    "birthday": "1990-01-01",     // 可选，生日，YYYY-MM-DD格式
    "avatar": "base64_data"       // 可选，头像base64数据
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | nickname | string | 否 | 用户昵称，最大50字符 | "新昵称" |
  | sex | integer | 否 | 性别，0:未知 1:男 2:女 | 1 |
  | birthday | string | 否 | 生日，YYYY-MM-DD格式 | "1990-01-01" |
  | avatar | string | 否 | 头像base64数据 | "data:image/jpeg;base64,..." |
- **成功响应**: `{"code": 200, "status": 0, "message": "更新成功", "data": {...}}`
- **失败响应**: `{"code": 400, "status": -1, "message": "参数错误", "data": null}`

### 1.3 上传头像接口
- **接口地址**: `POST /applet/v1/app/upload_avatar`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: multipart/form-data
- **请求参数**:
  ```
  avatar: File  // 必填，头像文件，支持jpg/jpeg/png格式，最大5MB
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | avatar | File | 是 | 头像文件，支持jpg/jpeg/png格式，最大5MB | 图片文件 |
- **成功响应**: `{"code": 200, "status": 0, "message": "上传成功", "data": {"avatar_url": "xxx"}}`
- **失败响应**: `{"code": 400, "status": -1, "message": "文件格式错误", "data": null}`

## 2. VIP会员页面 (VIP/Membership)

### 2.1 VIP价格列表接口
- **接口地址**: `GET /applet/index/vip_list`
- **请求方式**: GET
- **是否需要认证**: 否
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**: 无需请求参数
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": [{"id": 1, "name": "月卡", "price": "30.00"}]}`
- **失败响应**: `{"code": 500, "status": -1, "message": "服务器错误", "data": null}`

## 3. 分销员管理页面 (Referrer/Distribution)

### 3.1 申请成为分销员接口
- **接口地址**: `POST /applet/user/apply_ref`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "reason": "申请理由"  // 可选，申请成为分销员的理由，最大200字符
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | reason | string | 否 | 申请理由，最大200字符 | "我有丰富的销售经验" |
- **成功响应**: `{"code": 200, "status": 0, "message": "申请成功", "data": null}`
- **失败响应**: `{"code": 400, "status": -1, "message": "已是分销员", "data": null}`

### 3.2 获取分销员收入信息接口
- **接口地址**: `GET /applet/user/get_moneys`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**: 无需请求参数，通过Authorization头中的token识别用户身份
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"total_income": "1000.00"}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 3.3 获取余额变动记录接口
- **接口地址**: `GET /applet/user/get_balance_r`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "page": 1,        // 可选，页码，默认1
    "page_size": 20   // 可选，每页数量，默认20，最大100
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | page | integer | 否 | 页码，默认1 | 1 |
  | page_size | integer | 否 | 每页数量，默认20，最大100 | 20 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"records": [], "total": 100}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 3.4 获取下级用户列表接口
- **接口地址**: `GET /applet/user/l_level_users`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "page": 1,        // 可选，页码，默认1
    "page_size": 20   // 可选，每页数量，默认20，最大100
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | page | integer | 否 | 页码，默认1 | 1 |
  | page_size | integer | 否 | 每页数量，默认20，最大100 | 20 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"users": [], "total": 50}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 3.5 修改下级分销等级接口
- **接口地址**: `POST /applet/user/up_dist_level`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "user_id": 123,  // 必填，下级用户ID
    "level": 2       // 必填，分销等级，1-5
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | user_id | integer | 是 | 下级用户ID | 123 |
  | level | integer | 是 | 分销等级，1-5 | 2 |
- **成功响应**: `{"code": 200, "status": 0, "message": "修改成功", "data": null}`
- **失败响应**: `{"code": 400, "status": -1, "message": "用户不存在", "data": null}`

### 3.6 获取分销海报列表接口
- **接口地址**: `GET /applet/user/get_poster`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**: 无需请求参数，通过Authorization头中的token识别用户身份
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": [{"id": 1, "image_url": "xxx"}]}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 3.7 获取分销等级列表接口
- **接口地址**: `GET /applet/index/dist_level`
- **请求方式**: GET
- **是否需要认证**: 否
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**: 无需请求参数
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": [{"id": 1, "name": "初级"}]}`
- **失败响应**: `{"code": 500, "status": -1, "message": "服务器错误", "data": null}`

## 4. AI健康咨询聊天页面 (Chat/Consultation)

### 4.1 获取对话列表接口
- **接口地址**: `GET /applet/v1/chat/conversations`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "page": 1,        // 可选，页码，默认1
    "page_size": 20   // 可选，每页数量，默认20，最大100
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | page | integer | 否 | 页码，默认1 | 1 |
  | page_size | integer | 否 | 每页数量，默认20，最大100 | 20 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"conversations": [], "total": 10}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 4.2 获取对话消息接口
- **接口地址**: `GET /applet/v1/chat/conversations/{conversationId}/messages`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "page": 1,        // 可选，页码，默认1
    "page_size": 50   // 可选，每页数量，默认50，最大100
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | page | integer | 否 | 页码，默认1 | 1 |
  | page_size | integer | 否 | 每页数量，默认50，最大100 | 50 |
  | conversationId | string | 是 | 对话ID（URL路径参数） | "conv_123" |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"messages": [], "total": 100}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "对话不存在", "data": null}`

### 4.3 发送消息接口
- **接口地址**: `POST /applet/v1/chat/conversations/{conversationId}/messages`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "content": "消息内容",  // 必填，消息内容，最大2000字符
    "type": "text"        // 可选，消息类型，text/image/audio，默认text
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | conversationId | string | 是 | 对话ID（URL路径参数） | "conv_123" |
  | content | string | 是 | 消息内容，最大2000字符 | "你好，我想咨询一下" |
  | type | string | 否 | 消息类型，text/image/audio，默认text | "text" |
- **成功响应**: `{"code": 200, "status": 0, "message": "发送成功", "data": {"message": {}}}`
- **失败响应**: `{"code": 400, "status": -1, "message": "消息内容不能为空", "data": null}`

### 4.4 创建新对话接口
- **接口地址**: `POST /applet/v1/chat/conversations`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "title": "对话标题"  // 可选，对话标题，最大100字符，默认自动生成
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | title | string | 否 | 对话标题，最大100字符，默认自动生成 | "健康咨询" |
- **成功响应**: `{"code": 200, "status": 0, "message": "创建成功", "data": {"conversation": {}}}`
- **失败响应**: `{"code": 400, "status": -1, "message": "标题不能为空", "data": null}`

### 4.5 删除对话接口
- **接口地址**: `DELETE /applet/v1/chat/conversations/{conversationId}`
- **请求方式**: DELETE
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | conversationId | string | 是 | 对话ID（URL路径参数） | "conv_123" |
- **成功响应**: `{"code": 200, "status": 0, "message": "删除成功", "data": null}`
- **失败响应**: `{"code": 404, "status": -1, "message": "对话不存在", "data": null}`

### 4.6 更新对话标题接口
- **接口地址**: `PUT /applet/v1/chat/conversations/{conversationId}/title`
- **请求方式**: PUT
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "title": "新标题"  // 必填，新的对话标题，最大100字符
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | conversationId | string | 是 | 对话ID（URL路径参数） | "conv_123" |
  | title | string | 是 | 新的对话标题，最大100字符 | "健康咨询-更新" |
- **成功响应**: `{"code": 200, "status": 0, "message": "更新成功", "data": null}`
- **失败响应**: `{"code": 404, "status": -1, "message": "对话不存在", "data": null}`

### 4.7 删除所有对话接口
- **接口地址**: `DELETE /applet/v1/chat/conversations/all`
- **请求方式**: DELETE
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**: 无需请求参数，将删除当前用户的所有对话
- **成功响应**: `{"code": 200, "status": 0, "message": "删除成功", "data": null}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 4.8 上传聊天图片接口
- **接口地址**: `POST /applet/v1/chat/upload_image`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: multipart/form-data
- **请求参数**:
  ```
  image: File  // 必填，图片文件，支持jpg/jpeg/png格式，最大10MB
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | image | File | 是 | 图片文件，支持jpg/jpeg/png格式，最大10MB | 图片文件 |
- **成功响应**: `{"code": 200, "status": 0, "message": "上传成功", "data": {"image_url": "xxx"}}`
- **失败响应**: `{"code": 400, "status": -1, "message": "文件格式错误", "data": null}`

### 4.9 语音转文字接口
- **接口地址**: `POST /applet/v1/chat/speech_to_text`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: multipart/form-data
- **请求参数**:
  ```
  audio: File  // 必填，音频文件，支持mp3/wav/m4a格式，最大20MB，时长不超过60秒
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | audio | File | 是 | 音频文件，支持mp3/wav/m4a格式，最大20MB，时长不超过60秒 | 音频文件 |
- **成功响应**: `{"code": 200, "status": 0, "message": "转换成功", "data": {"text": "转换后的文字"}}`
- **失败响应**: `{"code": 400, "status": -1, "message": "音频格式错误", "data": null}`

### 4.10 文本转语音接口
- **接口地址**: `POST /applet/v1/trans/tts`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "text": "要转换的文字",  // 必填，要转换的文本，最大500字符
    "language": "zh-CN"   // 可选，语言代码，默认zh-CN
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | text | string | 是 | 要转换的文本，最大500字符 | "你好，欢迎使用" |
  | language | string | 否 | 语言代码，默认zh-CN | "zh-CN" |
- **成功响应**: `{"code": 200, "status": 0, "message": "转换成功", "data": {"audio_url": "xxx"}}`
- **失败响应**: `{"code": 400, "status": -1, "message": "文本不能为空", "data": null}`

### 4.11 获取TTS支持的语言列表接口
- **接口地址**: `GET /applet/v1/trans/tts/languages`
- **请求方式**: GET
- **是否需要认证**: 否
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**: 无需请求参数
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": [{"code": "zh-CN", "name": "中文"}]}`
- **失败响应**: `{"code": 500, "status": -1, "message": "服务器错误", "data": null}`

## 5. 医生列表页面 (DoctorList)

### 5.1 获取医生列表接口
- **接口地址**: `GET /applet/v1/doctors`
- **请求方式**: GET
- **是否需要认证**: 否
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "page": 1,              // 可选，页码，默认1
    "page_size": 20,        // 可选，每页数量，默认20，最大100
    "specialty": "内科",    // 可选，专科筛选
    "keyword": "张医生"     // 可选，关键词搜索（姓名）
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | page | integer | 否 | 页码，默认1 | 1 |
  | page_size | integer | 否 | 每页数量，默认20，最大100 | 20 |
  | specialty | string | 否 | 专科筛选 | "内科" |
  | keyword | string | 否 | 关键词搜索（姓名） | "张医生" |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"doctors": [], "total": 50}}`
- **失败响应**: `{"code": 500, "status": -1, "message": "服务器错误", "data": null}`

### 5.2 获取包含互动状态的医生列表接口
- **接口地址**: `GET /applet/v1/doctors/with-interaction`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "page": 1,        // 可选，页码，默认1
    "page_size": 20   // 可选，每页数量，默认20，最大100
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | page | integer | 否 | 页码，默认1 | 1 |
  | page_size | integer | 否 | 每页数量，默认20，最大100 | 20 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"doctors": [], "total": 50}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

## 6. 医生详情页面 (DoctorDetail)

### 6.1 获取单个医生详情接口
- **接口地址**: `GET /applet/v1/doctors/{doctorId}`
- **请求方式**: GET
- **是否需要认证**: 否
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | doctorId | integer | 是 | 医生ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"id": 1, "name": "张医生"}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "医生不存在", "data": null}`

### 6.2 点赞医生接口
- **接口地址**: `POST /applet/v1/doctors/{doctorId}/like`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | doctorId | integer | 是 | 医生ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "点赞成功", "data": null}`
- **失败响应**: `{"code": 400, "status": -1, "message": "已点赞", "data": null}`

### 6.3 取消点赞医生接口
- **接口地址**: `DELETE /applet/v1/doctors/{doctorId}/like`
- **请求方式**: DELETE
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | doctorId | integer | 是 | 医生ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "取消点赞成功", "data": null}`
- **失败响应**: `{"code": 400, "status": -1, "message": "未点赞", "data": null}`

### 6.4 收藏医生接口
- **接口地址**: `POST /applet/v1/doctors/{doctorId}/favorite`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | doctorId | integer | 是 | 医生ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "收藏成功", "data": null}`
- **失败响应**: `{"code": 400, "status": -1, "message": "已收藏", "data": null}`

### 6.5 取消收藏医生接口
- **接口地址**: `DELETE /applet/v1/doctors/{doctorId}/favorite`
- **请求方式**: DELETE
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | doctorId | integer | 是 | 医生ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "取消收藏成功", "data": null}`
- **失败响应**: `{"code": 400, "status": -1, "message": "未收藏", "data": null}`

### 6.6 获取医生互动状态接口
- **接口地址**: `GET /applet/v1/doctors/{doctorId}/status`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | doctorId | integer | 是 | 医生ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"is_liked": true, "is_favorited": false}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "医生不存在", "data": null}`

### 6.7 获取医生的产品接口（用户端）
- **接口地址**: `GET /applet/v1/products/doctor/{doctorId}`
- **请求方式**: GET
- **是否需要认证**: 否
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "page": 1,        // 可选，页码，默认1
    "page_size": 20   // 可选，每页数量，默认20，最大100
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | doctorId | integer | 是 | 医生ID（URL路径参数） | 123 |
  | page | integer | 否 | 页码，默认1 | 1 |
  | page_size | integer | 否 | 每页数量，默认20，最大100 | 20 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"products": [], "total": 20}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "医生不存在", "data": null}`

## 7. 我的收藏页面 (MyFavorites)

### 7.1 获取用户收藏的医生列表接口
- **接口地址**: `GET /applet/v1/doctors/favorites`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "page": 1,        // 可选，页码，默认1
    "page_size": 20   // 可选，每页数量，默认20，最大100
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | page | integer | 否 | 页码，默认1 | 1 |
  | page_size | integer | 否 | 每页数量，默认20，最大100 | 20 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"doctors": [], "total": 10}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 7.2 获取用户点赞的医生列表接口
- **接口地址**: `GET /applet/v1/doctors/likes`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "page": 1,        // 可选，页码，默认1
    "page_size": 20   // 可选，每页数量，默认20，最大100
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | page | integer | 否 | 页码，默认1 | 1 |
  | page_size | integer | 否 | 每页数量，默认20，最大100 | 20 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"doctors": [], "total": 15}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

## 8. 医生产品管理页面 (DoctorProductManagement)

### 8.1 创建产品接口
- **接口地址**: `POST /applet/v1/doctor/products`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "name": "产品名称",           // 必填，产品名称，最大100字符
    "price": "99.00",           // 必填，产品价格，格式：数字.数字
    "description": "产品描述",   // 可选，产品描述，最大1000字符
    "images": [],               // 可选，产品图片URL数组，最多10张
    "category": "药品",         // 可选，产品分类
    "stock": 100                // 可选，库存数量，默认0
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | name | string | 是 | 产品名称，最大100字符 | "感冒灵颗粒" |
  | price | string | 是 | 产品价格，格式：数字.数字 | "99.00" |
  | description | string | 否 | 产品描述，最大1000字符 | "用于治疗感冒症状" |
  | images | array | 否 | 产品图片URL数组，最多10张 | ["url1", "url2"] |
  | category | string | 否 | 产品分类 | "药品" |
  | stock | integer | 否 | 库存数量，默认0 | 100 |
- **成功响应**: `{"code": 200, "status": 0, "message": "创建成功", "data": {"product": {}}}`
- **失败响应**: `{"code": 400, "status": -1, "message": "产品名称不能为空", "data": null}`

### 8.2 获取产品列表接口
- **接口地址**: `GET /applet/v1/doctor/products`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "page": 1,           // 可选，页码，默认1
    "page_size": 20,     // 可选，每页数量，默认20，最大100
    "status": "active",  // 可选，状态筛选：active/pending/rejected
    "keyword": "感冒"    // 可选，关键词搜索
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | page | integer | 否 | 页码，默认1 | 1 |
  | page_size | integer | 否 | 每页数量，默认20，最大100 | 20 |
  | status | string | 否 | 状态筛选：active/pending/rejected | "active" |
  | keyword | string | 否 | 关键词搜索 | "感冒" |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"products": [], "total": 30}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 8.3 获取产品详情接口
- **接口地址**: `GET /applet/v1/doctor/products/{productId}`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | productId | integer | 是 | 产品ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"id": 1, "name": "产品名称"}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "产品不存在", "data": null}`

### 8.4 更新产品接口
- **接口地址**: `PUT /applet/v1/doctor/products/{productId}`
- **请求方式**: PUT
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "name": "新产品名称",       // 可选，产品名称，最大100字符
    "price": "199.00",         // 可选，产品价格
    "description": "新描述",   // 可选，产品描述，最大1000字符
    "images": [],             // 可选，产品图片URL数组
    "stock": 200              // 可选，库存数量
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | productId | integer | 是 | 产品ID（URL路径参数） | 123 |
  | name | string | 否 | 产品名称，最大100字符 | "新产品名称" |
  | price | string | 否 | 产品价格 | "199.00" |
  | description | string | 否 | 产品描述，最大1000字符 | "新的产品描述" |
  | images | array | 否 | 产品图片URL数组 | ["url1", "url2"] |
  | stock | integer | 否 | 库存数量 | 200 |
- **成功响应**: `{"code": 200, "status": 0, "message": "更新成功", "data": {"product": {}}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "产品不存在", "data": null}`

### 8.5 删除产品接口
- **接口地址**: `DELETE /applet/v1/doctor/products/{productId}`
- **请求方式**: DELETE
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | productId | integer | 是 | 产品ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "删除成功", "data": null}`
- **失败响应**: `{"code": 404, "status": -1, "message": "产品不存在", "data": null}`

### 8.6 获取产品统计接口
- **接口地址**: `GET /applet/v1/doctor/products/statistics/overview`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**: 无需请求参数，获取当前医生的产品统计信息
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"total_products": 10, "total_sales": "5000.00"}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 8.7 获取产品订单列表接口
- **接口地址**: `GET /applet/v1/doctor/products/orders/list`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "page": 1,           // 可选，页码，默认1
    "page_size": 20,     // 可选，每页数量，默认20，最大100
    "status": "paid",    // 可选，订单状态筛选：pending/paid/shipped/completed/cancelled
    "product_id": 123    // 可选，产品ID筛选
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | page | integer | 否 | 页码，默认1 | 1 |
  | page_size | integer | 否 | 每页数量，默认20，最大100 | 20 |
  | status | string | 否 | 订单状态：pending/paid/shipped/completed/cancelled | "paid" |
  | product_id | integer | 否 | 产品ID筛选 | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"orders": [], "total": 100}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 8.8 单张产品图片上传接口
- **接口地址**: `POST /applet/v1/doctor/products/upload-image`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: multipart/form-data
- **请求参数**:
  ```
  image: File  // 必填，图片文件，支持jpg/jpeg/png格式，最大5MB
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | image | File | 是 | 图片文件，支持jpg/jpeg/png格式，最大5MB | 图片文件 |
- **成功响应**: `{"code": 200, "status": 0, "message": "上传成功", "data": {"image_url": "xxx"}}`
- **失败响应**: `{"code": 400, "status": -1, "message": "文件格式错误", "data": null}`

### 8.9 批量产品图片上传接口
- **接口地址**: `POST /applet/v1/doctor/products/upload-images`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: multipart/form-data
- **请求参数**:
  ```
  images: [File1, File2, ...]  // 必填，图片文件数组，每个文件支持jpg/jpeg/png格式，最大5MB，最多10张
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | images | File[] | 是 | 图片文件数组，每个文件支持jpg/jpeg/png格式，最大5MB，最多10张 | [图片文件1, 图片文件2] |
- **成功响应**: `{"code": 200, "status": 0, "message": "上传成功", "data": {"image_urls": ["xxx", "yyy"]}}`
- **失败响应**: `{"code": 400, "status": -1, "message": "文件格式错误", "data": null}`

### 8.10 获待发货订单列表接口（医生端）
- **接口地址**: `GET /applet/v1/doctor/products/orders/pending-shipment`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "page": 1,        // 可选，页码，默认1
    "page_size": 20   // 可选，每页数量，默认20，最大100
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | page | integer | 否 | 页码，默认1 | 1 |
  | page_size | integer | 否 | 每页数量，默认20，最大100 | 20 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"orders": [], "total": 30}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 8.11 订单发货接口（医生端）
- **接口地址**: `POST /applet/v1/doctor/products/orders/{orderId}/ship`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:

  ```json
  {
    "tracking_number": "SF123456789",  // 必填，快递单号
    "logistics_company": "顺丰快递"    // 必填，物流公司名称
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | orderId | integer | 是 | 订单ID（URL路径参数） | 123 |
  | tracking_number | string | 是 | 快递单号 | "SF123456789" |
  | logistics_company | string | 是 | 物流公司名称 | "顺丰快递" |
- **成功响应**: `{"code": 200, "status": 0, "message": "发货成功", "data": null}`
- **失败响应**: `{"code": 400, "status": -1, "message": "订单状态错误", "data": null}`

### 8.12 获取订单物流状态接口（医生端）
- **接口地址**: `GET /applet/v1/doctor/products/orders/{orderId}/shipping-status`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | orderId | integer | 是 | 订单ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"status": "shipped", "tracking_info": []}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "订单不存在", "data": null}`

## 9. 产品商城页面 (ProductMall)

### 9.1 获取产品列表接口（用户端）
- **接口地址**: `GET /applet/v1/products`
- **请求方式**: GET
- **是否需要认证**: 否
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "page": 1,              // 可选，页码，默认1
    "page_size": 20,        // 可选，每页数量，默认20，最大100
    "category": "药品",     // 可选，产品分类筛选
    "keyword": "感冒药",    // 可选，关键词搜索
    "doctor_id": 123,       // 可选，医生ID筛选
    "min_price": "10.00",   // 可选，最低价格筛选
    "max_price": "500.00"   // 可选，最高价格筛选
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | page | integer | 否 | 页码，默认1 | 1 |
  | page_size | integer | 否 | 每页数量，默认20，最大100 | 20 |
  | category | string | 否 | 产品分类筛选 | "药品" |
  | keyword | string | 否 | 关键词搜索 | "感冒药" |
  | doctor_id | integer | 否 | 医生ID筛选 | 123 |
  | min_price | string | 否 | 最低价格筛选 | "10.00" |
  | max_price | string | 否 | 最高价格筛选 | "500.00" |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"products": [], "total": 100}}`
- **失败响应**: `{"code": 500, "status": -1, "message": "服务器错误", "data": null}`

### 9.2 获取产品分类列表接口（用户端）
- **接口地址**: `GET /applet/v1/products/categories`
- **请求方式**: GET
- **是否需要认证**: 否
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**: 无需请求参数，获取所有产品分类
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": [{"id": 1, "name": "药品"}]}`
- **失败响应**: `{"code": 500, "status": -1, "message": "服务器错误", "data": null}`

## 10. 产品详情页面 (ProductDetail)

### 10.1 获取产品详情接口（用户端）
- **接口地址**: `GET /applet/v1/products/{productId}`
- **请求方式**: GET
- **是否需要认证**: 否
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | productId | integer | 是 | 产品ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"id": 1, "name": "产品名称"}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "产品不存在", "data": null}`

### 10.2 加入购物车接口
- **接口地址**: `POST /applet/v1/cart/add`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "product_id": 1,                    // 必填，产品ID
    "quantity": 2                       // 必填，数量，必须大于0
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | product_id | integer | 是 | 产品ID | 1 |
  | quantity | integer | 是 | 数量，必须大于0 | 2 |
- **成功响应**: `{"code": 200, "status": 0, "message": "加入购物车成功", "data": {"cart_id": 1, "product_id": 1, "quantity": 2}}`
- **失败响应**: `{"code": 400, "status": -1, "message": "产品不存在", "data": null}`

### 10.3 创建订单接口（用户端）
- **接口地址**: `POST /applet/v1/products/orders`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "product_id": 1,      // 必填，产品ID
    "quantity": 2,        // 必填，购买数量，大于0
    "address_id": 1,      // 必填，收货地址ID
    "note": "备注信息",   // 可选，订单备注，最大200字符
    "coupon_id": 5        // 可选，优惠券ID
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | product_id | integer | 是 | 产品ID | 1 |
  | quantity | integer | 是 | 购买数量，大于0 | 2 |
  | address_id | integer | 是 | 收货地址ID | 1 |
  | note | string | 否 | 订单备注，最大200字符 | "备注信息" |
  | coupon_id | integer | 否 | 优惠券ID | 5 |
- **成功响应**: `{"code": 200, "status": 0, "message": "创建成功", "data": {"order": {}}}`
- **失败响应**: `{"code": 400, "status": -1, "message": "产品不存在", "data": null}`

## 11. 购物车页面 (ShoppingCart)

### 11.1 获取购物车列表接口
- **接口地址**: `GET /applet/v1/cart`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "lang": "zh"                        // 可选，语言代码 (zh/en/ug)，默认zh
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | lang | string | 否 | 语言代码 (zh/en/ug)，默认zh | "zh" |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取购物车列表成功", "data": {"items": [], "total_amount": "100.00", "total_count": 3}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 11.2 更新购物车商品接口
- **接口地址**: `PUT /applet/v1/cart/{cartId}`
- **请求方式**: PUT
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "quantity": 3                       // 必填，新的数量，必须大于0
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | cartId | integer | 是 | 购物车商品ID（URL路径参数） | 123 |
  | quantity | integer | 是 | 新的数量，必须大于0 | 3 |
  | lang | string | 否 | 语言代码 (zh/en/ug)，默认zh | "zh" |
- **成功响应**: `{"code": 200, "status": 0, "message": "购物车商品更新成功", "data": {"cart_id": 1, "quantity": 3}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "购物车商品不存在", "data": null}`

### 11.3 删除购物车商品接口
- **接口地址**: `DELETE /applet/v1/cart/{cartId}`
- **请求方式**: DELETE
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | cartId | integer | 是 | 购物车商品ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "购物车商品删除成功", "data": null}`
- **失败响应**: `{"code": 404, "status": -1, "message": "购物车商品不存在", "data": null}`

### 11.4 清空购物车接口
- **接口地址**: `DELETE /applet/v1/cart/clear`
- **请求方式**: DELETE
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**: 无需请求参数，清空当前用户的所有购物车商品
- **成功响应**: `{"code": 200, "status": 0, "message": "购物车清空成功", "data": null}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 11.5 批量删除购物车商品接口
- **接口地址**: `DELETE /applet/v1/cart/batch`
- **请求方式**: DELETE
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "cart_ids": [1, 2, 3]               // 必填，要删除的购物车商品ID数组
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | cart_ids | integer[] | 是 | 要删除的购物车商品ID数组 | [1, 2, 3] |
- **成功响应**: `{"code": 200, "status": 0, "message": "批量删除成功", "data": null}`
- **失败响应**: `{"code": 400, "status": -1, "message": "购物车商品ID不能为空", "data": null}`

## 12. 我的订单页面 (MyOrders)

### 12.1 获取我的订单接口（用户端）
- **接口地址**: `GET /applet/v1/products/orders/my`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "page": 1,           // 可选，页码，默认1
    "page_size": 20,     // 可选，每页数量，默认20，最大100
    "status": "paid"     // 可选，订单状态筛选：pending/paid/shipped/completed/cancelled
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | page | integer | 否 | 页码，默认1 | 1 |
  | page_size | integer | 否 | 每页数量，默认20，最大100 | 20 |
  | status | string | 否 | 订单状态：pending/paid/shipped/completed/cancelled | "paid" |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"orders": [], "total": 50}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 12.2 获取订单详情接口（用户端）
- **接口地址**: `GET /applet/v1/products/orders/{orderId}`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | orderId | integer | 是 | 订单ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"id": 1, "product_name": "产品名称"}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "订单不存在", "data": null}`

### 12.3 取消订单接口（用户端）
- **接口地址**: `POST /applet/v1/products/orders/{orderId}/cancel`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "reason": "取消原因"  // 必填，取消原因，最大200字符
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | orderId | integer | 是 | 订单ID（URL路径参数） | 123 |
  | reason | string | 是 | 取消原因，最大200字符 | "不需要了" |
- **成功响应**: `{"code": 200, "status": 0, "message": "取消成功", "data": null}`
- **失败响应**: `{"code": 400, "status": -1, "message": "订单无法取消", "data": null}`

### 12.4 获取我的已发货订单接口（用户端）
- **接口地址**: `GET /applet/v1/products/orders/my/shipped`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "page": 1,        // 可选，页码，默认1
    "page_size": 20   // 可选，每页数量，默认20，最大100
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | page | integer | 否 | 页码，默认1 | 1 |
  | page_size | integer | 否 | 每页数量，默认20，最大100 | 20 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"orders": [], "total": 25}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 12.5 获取订单物流状态接口（用户端）
- **接口地址**: `GET /applet/v1/products/orders/{orderId}/shipping-status`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | orderId | integer | 是 | 订单ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"status": "delivered", "tracking_info": []}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "订单不存在", "data": null}`

## 13. 订单支付页面 (OrderPayment)

### 13.1 创建订单支付接口
- **接口地址**: `POST /applet/v1/products/orders/{orderId}/payment`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "payment_method": "wechat",  // 必填，支付方式：wechat/alipay/balance
    "return_url": "xxx"          // 可选，支付成功后的回调URL
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | orderId | integer | 是 | 订单ID（URL路径参数） | 123 |
  | payment_method | string | 是 | 支付方式：wechat/alipay/balance | "wechat" |
  | return_url | string | 否 | 支付成功后的回调URL | "https://example.com/callback" |
- **成功响应**: `{"code": 200, "status": 0, "message": "创建成功", "data": {"payment_url": "xxx", "order_sn": "xxx"}}`
- **失败响应**: `{"code": 400, "status": -1, "message": "订单状态错误", "data": null}`

### 13.2 查询订单支付状态接口
- **接口地址**: `GET /applet/v1/products/orders/{orderId}/payment/status`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | orderId | integer | 是 | 订单ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "查询成功", "data": {"status": "paid", "paid_at": "2024-01-01 12:00:00"}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "订单不存在", "data": null}`

### 13.3 同步订单支付状态接口
- **接口地址**: `POST /applet/v1/products/orders/{orderId}/payment/sync`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | orderId | integer | 是 | 订单ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "同步成功", "data": {"status": "paid"}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "订单不存在", "data": null}`

## 14. 健康档案页面 (HealthProfile)

### 14.1 获取健康档案接口
- **接口地址**: `GET /applet/v1/health-profile`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**: 无需请求参数，通过Authorization头中的token识别用户身份
- **成功响应**: `{"code": 200, "status": 0, "message": "获取成功", "data": {"id": 1, "height": 170.0, "weight": 65.0, ...}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "健康档案不存在", "data": null}`

### 14.2 创建/完整更新健康档案接口
- **接口地址**: `POST /applet/v1/health-profile`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "height": 170.0,                    // 可选，身高(cm)，范围50-300
    "weight": 65.0,                     // 可选，体重(kg)，范围20-500
    "blood_type": "A",                  // 可选，血型：A/B/AB/O/unknown
    "address_province": "广东省",       // 可选，省份，最大50字符
    "address_city": "深圳市",           // 可选，城市，最大50字符
    "address_district": "南山区",       // 可选，区县，最大50字符
    "address_detail": "科技园南区",     // 可选，详细地址，最大255字符
    "has_allergies": true,              // 可选，是否有过敏史，默认false
    "allergy_drugs": ["青霉素类药物"],  // 可选，过敏药物列表
    "allergy_foods": ["海鲜", "坚果"],  // 可选，过敏食物列表
    "allergy_others": "花粉过敏",       // 可选，其他过敏，最大200字符
    "has_current_medication": false,    // 可选，是否正在用药，默认false
    "current_medications": "降压药",    // 可选，当前用药，最大500字符
    "has_chronic_diseases": true,       // 可选，是否有慢性病，默认false
    "chronic_diseases": ["高血压"],     // 可选，慢性病列表
    "blood_pressure_range": "140-160/90-100", // 可选，血压范围，最大50字符
    "blood_sugar_range": "6.0-8.0",    // 可选，血糖范围，最大50字符
    "chronic_diseases_other": "其他慢性病", // 可选，其他慢性病，最大200字符
    "has_surgery_history": false,       // 可选，是否有手术史，默认false
    "surgery_history": "阑尾炎手术",    // 可选，手术史，最大500字符
    "family_diseases": ["糖尿病"],      // 可选，家族病史列表
    "family_diseases_other": "其他家族病史", // 可选，其他家族病史，最大200字符
    "exercise_frequency": "regular",    // 可选，运动频率：sedentary/light/regular/frequent/intense
    "diet_preferences": ["清淡"],       // 可选，饮食偏好列表
    "smoking_status": "never",          // 可选，吸烟状态：never/current/quit
    "drinking_status": "never",         // 可选，饮酒状态：never/occasional/regular/heavy
    "sleep_duration": "7_8",            // 可选，睡眠时长：less_6/6_7/7_8/8_9/more_9
    "sleep_quality": "good",            // 可选，睡眠质量：poor/fair/good/excellent
    "stress_level": "moderate",         // 可选，压力水平：very_low/low/moderate/high/very_high
    "is_menopause": false,              // 可选，是否绝经，默认false
    "menstrual_regular": "yes",         // 可选，月经是否规律：yes/no/uncertain
    "has_pregnancy": false,             // 可选，是否曾怀孕，默认false
    "birth_count": 0                    // 可选，生育次数，默认0
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | height | decimal | 否 | 身高(cm)，范围50-300 | 170.0 |
  | weight | decimal | 否 | 体重(kg)，范围20-500 | 65.0 |
  | blood_type | string | 否 | 血型：A/B/AB/O/unknown | "A" |
  | has_allergies | boolean | 否 | 是否有过敏史 | true |
  | allergy_drugs | array | 否 | 过敏药物列表 | ["青霉素类药物"] |
  | chronic_diseases | array | 否 | 慢性病列表 | ["高血压"] |
  | exercise_frequency | string | 否 | 运动频率 | "regular" |
  | smoking_status | string | 否 | 吸烟状态 | "never" |
- **成功响应**: `{"code": 200, "status": 0, "message": "创建/更新成功", "data": {"id": 1, "height": 170.0, ...}}`
- **失败响应**: `{"code": 400, "status": -1, "message": "创建/更新健康档案失败", "data": null}`

### 14.3 部分更新健康档案接口
- **接口地址**: `PATCH /applet/v1/health-profile`
- **请求方式**: PATCH
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "height": 175.0,                    // 可选，只更新需要修改的字段
    "weight": 70.0,                     // 可选，只更新需要修改的字段
    "blood_type": "B"                   // 可选，只更新需要修改的字段
  }
  ```
- **参数说明**: 支持所有健康档案字段的部分更新，只需传入要修改的字段
- **成功响应**: `{"code": 200, "status": 0, "message": "更新成功", "data": {"id": 1, "height": 175.0, ...}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "健康档案不存在", "data": null}`

### 14.4 删除健康档案接口
- **接口地址**: `DELETE /applet/v1/health-profile`
- **请求方式**: DELETE
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**: 无需请求参数，通过Authorization头中的token识别用户身份
- **成功响应**: `{"code": 200, "status": 0, "message": "健康档案删除成功", "data": null}`
- **失败响应**: `{"code": 404, "status": -1, "message": "健康档案不存在", "data": null}`

## 15. 地址管理页面 (AddressManagement)

### 15.1 创建用户地址接口
- **接口地址**: `POST /applet/v1/addresses`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "receiver_name": "张三",            // 必填，收货人姓名，最大50字符
    "receiver_phone": "13800138000",    // 必填，收货人电话，7-20位数字
    "province": "广东省",               // 必填，省份，最大50字符
    "city": "深圳市",                   // 必填，城市，最大50字符
    "district": "南山区",               // 必填，区/县，最大50字符
    "detailed_address": "科技园南区深南大道10000号", // 必填，详细地址，最大200字符
    "postal_code": "518000",            // 可选，邮政编码，数字格式，最大10字符
    "address_label": "家",              // 可选，地址标签(如:家、公司、学校等)，最大20字符
    "is_default": true                  // 可选，是否设为默认地址，默认false
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | receiver_name | string | 是 | 收货人姓名，最大50字符 | "张三" |
  | receiver_phone | string | 是 | 收货人电话，7-20位数字 | "13800138000" |
  | province | string | 是 | 省份，最大50字符 | "广东省" |
  | city | string | 是 | 城市，最大50字符 | "深圳市" |
  | district | string | 是 | 区/县，最大50字符 | "南山区" |
  | detailed_address | string | 是 | 详细地址，最大200字符 | "科技园南区深南大道10000号" |
  | postal_code | string | 否 | 邮政编码，数字格式，最大10字符 | "518000" |
  | address_label | string | 否 | 地址标签，最大20字符 | "家" |
  | is_default | boolean | 否 | 是否设为默认地址，默认false | true |
- **成功响应**: `{"code": 200, "status": 0, "message": "地址创建成功", "data": {"id": 1, "receiver_name": "张三", ...}}`
- **失败响应**: `{"code": 400, "status": -1, "message": "收货人姓名不能为空", "data": null}`

### 15.2 获取用户地址列表接口
- **接口地址**: `GET /applet/v1/addresses`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**: 无需请求参数，通过Authorization头中的token识别用户身份
- **成功响应**: `{"code": 200, "status": 0, "message": "获取地址列表成功", "data": {"addresses": [], "total": 3, "default_address_id": 1}}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 15.3 获取简化地址列表接口
- **接口地址**: `GET /applet/v1/addresses/simple`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**: 无需请求参数，用于订单选择地址时的简化列表
- **成功响应**: `{"code": 200, "status": 0, "message": "获取地址列表成功", "data": [{"id": 1, "receiver_name": "张三", "full_address": "广东省深圳市南山区...", "is_default": true}]}`
- **失败响应**: `{"code": 401, "status": -1, "message": "未授权", "data": null}`

### 15.4 获取地址详情接口
- **接口地址**: `GET /applet/v1/addresses/{addressId}`
- **请求方式**: GET
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | addressId | integer | 是 | 地址ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "获取地址详情成功", "data": {"id": 1, "receiver_name": "张三", ...}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "地址不存在", "data": null}`

### 15.5 更新用户地址接口
- **接口地址**: `PUT /applet/v1/addresses/{addressId}`
- **请求方式**: PUT
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "receiver_name": "李四",            // 可选，收货人姓名，最大50字符
    "receiver_phone": "13900139000",    // 可选，收货人电话，7-20位数字
    "province": "北京市",               // 可选，省份，最大50字符
    "city": "北京市",                   // 可选，城市，最大50字符
    "district": "朝阳区",               // 可选，区/县，最大50字符
    "detailed_address": "建国路100号",  // 可选，详细地址，最大200字符
    "postal_code": "100000",            // 可选，邮政编码，数字格式
    "address_label": "公司",            // 可选，地址标签，最大20字符
    "is_default": false                 // 可选，是否设为默认地址
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | addressId | integer | 是 | 地址ID（URL路径参数） | 123 |
  | receiver_name | string | 否 | 收货人姓名，最大50字符 | "李四" |
  | receiver_phone | string | 否 | 收货人电话，7-20位数字 | "13900139000" |
  | province | string | 否 | 省份，最大50字符 | "北京市" |
  | city | string | 否 | 城市，最大50字符 | "北京市" |
  | district | string | 否 | 区/县，最大50字符 | "朝阳区" |
  | detailed_address | string | 否 | 详细地址，最大200字符 | "建国路100号" |
  | postal_code | string | 否 | 邮政编码，数字格式 | "100000" |
  | address_label | string | 否 | 地址标签，最大20字符 | "公司" |
  | is_default | boolean | 否 | 是否设为默认地址 | false |
- **成功响应**: `{"code": 200, "status": 0, "message": "地址更新成功", "data": {"id": 1, "receiver_name": "李四", ...}}`
- **失败响应**: `{"code": 404, "status": -1, "message": "地址不存在", "data": null}`

### 15.6 删除用户地址接口
- **接口地址**: `DELETE /applet/v1/addresses/{addressId}`
- **请求方式**: DELETE
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**: 无
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | addressId | integer | 是 | 地址ID（URL路径参数） | 123 |
- **成功响应**: `{"code": 200, "status": 0, "message": "地址删除成功", "data": null}`
- **失败响应**: `{"code": 404, "status": -1, "message": "地址不存在", "data": null}`

### 15.7 设置默认地址接口
- **接口地址**: `POST /applet/v1/addresses/set-default`
- **请求方式**: POST
- **是否需要认证**: 是
- **内容类型**: application/json
- **请求参数**:
  ```json
  {
    "address_id": 1                     // 必填，要设为默认的地址ID
  }
  ```
- **参数说明**:
  | 参数名 | 类型 | 必填 | 描述 | 示例 |
  |--------|------|------|------|------|
  | address_id | integer | 是 | 要设为默认的地址ID | 1 |
- **成功响应**: `{"code": 200, "status": 0, "message": "设置默认地址成功", "data": null}`
- **失败响应**: `{"code": 404, "status": -1, "message": "地址不存在", "data": null}`

---

## 页面接口分类总结

本文档已按照小程序页面对所有API接口进行了详细分类，共包含15个主要页面模块：

1. **用户个人中心页面** - 个人资料管理、头像上传
2. **VIP会员页面** - VIP价格列表查询
3. **分销员管理页面** - 分销员申请、收入管理、下级管理、海报管理
4. **AI健康咨询聊天页面** - 对话管理、消息发送、语音转换、文件上传
5. **医生列表页面** - 医生搜索、筛选、状态查询
6. **医生详情页面** - 医生信息、点赞收藏、产品查看
7. **我的收藏页面** - 收藏医生列表、点赞医生列表
8. **医生产品管理页面** - 产品CRUD、订单管理、发货管理
9. **产品商城页面** - 产品浏览、分类筛选
10. **产品详情页面** - 产品详情、购物车操作、订单创建
11. **购物车页面** - 购物车管理、批量操作
12. **我的订单页面** - 订单查询、取消、物流跟踪
13. **订单支付页面** - 支付创建、状态查询、同步
14. **健康档案页面** - 健康信息管理
15. **地址管理页面** - 收货地址CRUD

每个页面模块都包含了该页面所需的完整接口，便于前端开发时快速定位和使用相应的API接口。










## 接口使用说明

### 认证说明
需要认证的接口需要在请求头中添加：
```
Authorization: Bearer {token}
```

### 通用错误响应格式
```json
{
  "code": 400,
  "status": -1,
  "message": "错误信息",
  "message_uy": "维吾尔语错误信息",
  "data": null
}
```

### 常见错误码
| 错误码 | 描述 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

### 多语言支持
支持的语言代码：`zh-CN`（中文）、`uy-CN`（维吾尔语）、`en-US`（英语）

可通过以下方式指定语言：
1. 请求头：`Accept-Language: zh-CN`
2. URL参数：`?lang=zh-CN`
3. 请求体参数：`{"lang": "zh-CN"}`
