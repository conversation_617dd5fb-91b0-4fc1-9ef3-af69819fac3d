// 统一的布局变量系统
// 包含尺寸、边框、阴影、层级等布局相关变量

// ==================== 尺寸变量 ====================
// 容器最大宽度
$container-max-width: 1200rpx;
$container-padding: 32rpx;

// 导航栏高度
$navbar-height: 88rpx;
$navbar-height-with-status: 132rpx; // 包含状态栏

// 标签栏高度
$tabbar-height: 100rpx;
$tabbar-safe-area: 68rpx; // 安全区域高度

// 页面内容区域
$page-content-min-height: calc(100vh - #{$navbar-height} - #{$tabbar-height});

// ==================== 边框半径 ====================
$border-radius-none: 0;
$border-radius-sm: 4rpx;
$border-radius-base: 8rpx;
$border-radius-md: 12rpx;
$border-radius-lg: 16rpx;
$border-radius-xl: 20rpx;
$border-radius-xxl: 24rpx;
$border-radius-full: 50%;

// 组件边框半径
$card-border-radius: $border-radius-lg;
$button-border-radius: $border-radius-base;
$input-border-radius: $border-radius-base;
$modal-border-radius: $border-radius-xl;

// ==================== 边框宽度 ====================
$border-width-none: 0;
$border-width-thin: 1rpx;
$border-width-base: 2rpx;
$border-width-thick: 4rpx;

// ==================== 阴影定义 ====================
$box-shadow-none: none;
$box-shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
$box-shadow-base: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
$box-shadow-md: 0 6rpx 24rpx rgba(0, 0, 0, 0.12);
$box-shadow-lg: 0 8rpx 32rpx rgba(0, 0, 0, 0.16);
$box-shadow-xl: 0 12rpx 48rpx rgba(0, 0, 0, 0.20);

// 组件阴影
$card-shadow: $box-shadow-base;
$modal-shadow: $box-shadow-lg;
$dropdown-shadow: $box-shadow-md;

// ==================== 层级（z-index）====================
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
$z-index-toast: 1080;

// ==================== 过渡动画 ====================
$transition-duration-fast: 0.15s;
$transition-duration-base: 0.3s;
$transition-duration-slow: 0.5s;

$transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);

$transition-all: all $transition-duration-base $transition-timing-function;
$transition-color: color $transition-duration-fast $transition-timing-function;
$transition-background: background-color $transition-duration-fast $transition-timing-function;
$transition-transform: transform $transition-duration-base $transition-timing-function;

// ==================== 断点变量 ====================
$breakpoint-xs: 480rpx;
$breakpoint-sm: 640rpx;
$breakpoint-md: 768rpx;
$breakpoint-lg: 1024rpx;
$breakpoint-xl: 1280rpx;
$breakpoint-xxl: 1536rpx;

// ==================== CSS变量映射 ====================
:root {
  // 尺寸
  --container-max-width: #{$container-max-width};
  --container-padding: #{$container-padding};
  --navbar-height: #{$navbar-height};
  --tabbar-height: #{$tabbar-height};
  
  // 边框半径
  --border-radius-none: #{$border-radius-none};
  --border-radius-sm: #{$border-radius-sm};
  --border-radius-base: #{$border-radius-base};
  --border-radius-md: #{$border-radius-md};
  --border-radius-lg: #{$border-radius-lg};
  --border-radius-xl: #{$border-radius-xl};
  --border-radius-xxl: #{$border-radius-xxl};
  --border-radius-full: #{$border-radius-full};
  
  // 边框宽度
  --border-width-none: #{$border-width-none};
  --border-width-thin: #{$border-width-thin};
  --border-width-base: #{$border-width-base};
  --border-width-thick: #{$border-width-thick};
  
  // 阴影
  --box-shadow-none: #{$box-shadow-none};
  --box-shadow-sm: #{$box-shadow-sm};
  --box-shadow-base: #{$box-shadow-base};
  --box-shadow-md: #{$box-shadow-md};
  --box-shadow-lg: #{$box-shadow-lg};
  --box-shadow-xl: #{$box-shadow-xl};
  
  // 层级
  --z-index-dropdown: #{$z-index-dropdown};
  --z-index-modal: #{$z-index-modal};
  --z-index-toast: #{$z-index-toast};
  
  // 过渡
  --transition-duration-fast: #{$transition-duration-fast};
  --transition-duration-base: #{$transition-duration-base};
  --transition-duration-slow: #{$transition-duration-slow};
  --transition-all: #{$transition-all};
}

// ==================== 布局工具类 ====================
// 容器
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--container-padding);
}

// Flexbox工具类
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

// Flex对齐
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

.content-start { align-content: flex-start; }
.content-end { align-content: flex-end; }
.content-center { align-content: center; }
.content-between { align-content: space-between; }
.content-around { align-content: space-around; }

// Flex grow/shrink
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

.grow { flex-grow: 1; }
.grow-0 { flex-grow: 0; }
.shrink { flex-shrink: 1; }
.shrink-0 { flex-shrink: 0; }

// 定位
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

// 显示/隐藏
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }

// 溢出
.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }

// 边框半径工具类
.rounded-none { border-radius: var(--border-radius-none); }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded { border-radius: var(--border-radius-base); }
.rounded-md { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }
.rounded-xxl { border-radius: var(--border-radius-xxl); }
.rounded-full { border-radius: var(--border-radius-full); }

// 阴影工具类
.shadow-none { box-shadow: var(--box-shadow-none); }
.shadow-sm { box-shadow: var(--box-shadow-sm); }
.shadow { box-shadow: var(--box-shadow-base); }
.shadow-md { box-shadow: var(--box-shadow-md); }
.shadow-lg { box-shadow: var(--box-shadow-lg); }
.shadow-xl { box-shadow: var(--box-shadow-xl); }

// 宽度工具类
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }

// 高度工具类
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-screen { height: 100vh; }

// 最小/最大尺寸
.min-h-screen { min-height: 100vh; }
.min-w-0 { min-width: 0; }
.max-w-full { max-width: 100%; }
.max-h-full { max-height: 100%; }

// ==================== 兼容性映射 ====================
// 为了兼容现有代码，保留一些旧的变量名
$uni-radius-root: $border-radius-base;
