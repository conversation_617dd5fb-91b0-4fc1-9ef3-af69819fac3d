<template>
	<!--
		💬 帮助反馈页面模板
		这是用户提交帮助和反馈信息的页面
		包含：反馈说明、联系方式表单、问题描述、提交功能等
		用户可以在这里报告问题、提出建议或寻求帮助
	-->

	<!--
		📄 帮助反馈页面容器
		- class="help-feedback-container": 基础页面样式
		- :class: 动态样式类，包括字体大小和字体类型
		- :key: 强制重新渲染的键，当字体或语言变化时重新渲染
		  * fontSizeUpdateKey: 字体大小变化键
		  * i18nUpdateKey: 国际化变化键
	-->
	<view class="help-feedback-container" :class="[fontSizeClass, fontClass]" :key="fontSizeUpdateKey + i18nUpdateKey">
		<!-- 📄 页面内容容器 -->
		<view class="content-container">
			<!--
				📢 反馈说明卡片
				向用户说明反馈的目的和注意事项
			-->
			<view class="feedback-notice">
				<!-- 说明卡片头部 -->
				<view class="notice-header">
					<!--
						信息图标
						使用FirstUI的info图标，绿色表示友好提示
					-->
					<fui-icon name="info" :size="36" color="#4CAF50"></fui-icon>

					<!--
						说明标题
						$t('helpFeedback.feedbackNotice'): 从语言包获取"反馈说明"文字
					-->
					<text class="notice-title">{{ $t('helpFeedback.feedbackNotice') }}</text>
				</view>

				<!-- 说明内容 -->
				<view class="notice-content">
					<!--
						说明文字
						$t('helpFeedback.noticeContent'): 从语言包获取说明内容文字
						通常包含如何填写反馈、处理时间等信息
					-->
					<text class="notice-text">{{ $t('helpFeedback.noticeContent') }}</text>
				</view>
			</view>

			<!--
				📝 表单字段循环
				v-for: 遍历formFields数组，动态生成表单字段
				:key: 每个表单字段的唯一标识
				formFields通常包含：姓名、手机号、邮箱等联系方式字段
			-->
			<view
				v-for="field in formFields"
				:key="field.key"
				class="form-section"
			>
				<!-- 表单字段标题 -->
				<view class="section-title">
					<!--
						字段标题文字
						$t(field.titleKey): 从语言包获取字段标题，如"姓名"、"手机号"等
					-->
					<text class="title-text">{{ $t(field.titleKey) }}</text>
				</view>

				<!-- 输入框容器 -->
				<view class="input-container">
					<!-- 输入框图标 -->
					<view class="input-icon">
						<!--
							字段图标
							:name: 图标名称，来自字段配置
							:size: 图标大小40px
							color="#999999": 灰色图标
						-->
						<fui-icon :name="field.iconName" :size="40" color="#999999"></fui-icon>
					</view>

					<!--
						输入框
						:type: 输入类型，如text、number、email等
						:placeholder: 占位符文字，从语言包获取
						:value: 输入框的值，来自字段配置
						:maxlength: 最大输入长度限制
						@input: 输入时的回调函数，用于更新数据
					-->
					<input
						class="input-field"
						:type="field.inputType"
						:placeholder="$t(field.placeholderKey)"
						:value="field.value"
						:maxlength="field.maxlength"
						@input="field.onInput"
					/>
				</view>
			</view>

			<!--
				📝 问题描述区域
				用户可以在这里详细描述遇到的问题或建议
			-->
			<view class="form-section">
				<!-- 问题描述标题 -->
				<view class="section-title">
					<!--
						问题描述标题文字
						$t('helpFeedback.problemDescription'): 从语言包获取"问题描述"文字
					-->
					<text class="title-text">{{ $t('helpFeedback.problemDescription') }}</text>
				</view>

				<!-- 文本域容器 -->
				<view class="textarea-container">
					<!-- 文本域图标 -->
					<view class="textarea-icon">
						<!--
							编辑图标
							使用FirstUI的edit图标表示可编辑文本
						-->
						<fui-icon name="edit" :size="40" color="#999999"></fui-icon>
					</view>

					<!-- 文本域内容区域 -->
					<view class="textarea-content">
						<!--
							占位符内容
							v-if: 当用户还没有输入内容时显示占位符
							formData.content: 用户输入的问题描述内容
						-->
						<view v-if="!formData.content" class="placeholder-content">
							<text class="placeholder-subtitle">{{ $t('helpFeedback.includeLabel') }}</text>
							<view class="placeholder-list">
								<text
									v-for="item in placeholderItems"
									:key="item"
									class="placeholder-item"
								>{{ $t(item) }}</text>
							</view>
						</view>
						<textarea
							class="textarea-field"
							:class="{ 'has-content': formData.content }"
							placeholder=""
							v-model="formData.content"
							maxlength="1000"
						></textarea>
					</view>
				</view>
				<view class="char-count">
					<text class="count-text">{{ formData.content.length }}/1000</text>
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-section">
				<view class="submit-btn" @tap="submitFeedback">
					<text class="submit-text">{{ $t('helpFeedback.submitFeedback') }}</text>
				</view>
			</view>

			<!-- 底部提示 -->
			<view class="bottom-notice">
				<text class="notice-text">{{ $t('helpFeedback.bottomNotice') }}</text>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useFontSizePage } from '@/utils/fontSizeMixin.js'
import { useI18n } from '@/plugins/i18n.js'

// 使用国际化功能
const { fontClass, i18nUpdateKey, $t } = useI18n()

// 表单数据
const formData = ref({
	nickname: '',
	phone: '',
	content: ''
})

// 字体大小功能
const { fontSizeClass, fontSizeUpdateKey } = useFontSizePage()

// 设置导航栏标题
const setNavigationTitle = () => {
	uni.setNavigationBarTitle({
		title: $t('helpFeedback.title')
	})
}

// 页面加载时设置标题
onMounted(() => {
	setNavigationTitle()

	// 监听语言变化事件，更新标题
	uni.$on('languageChanged', () => {
		setNavigationTitle()
	})
})

// 页面卸载时移除监听
onUnmounted(() => {
	uni.$off('languageChanged')
})

// 表单字段配置数组
const formFields = computed(() => [
	{
		key: 'nickname',
		titleKey: 'helpFeedback.yourName',
		placeholderKey: 'helpFeedback.namePlaceholder',
		iconName: 'my',
		inputType: 'text',
		maxlength: 20,
		value: formData.value.nickname,
		onInput: (e) => { formData.value.nickname = e.detail.value }
	},
	{
		key: 'phone',
		titleKey: 'helpFeedback.yourPhone',
		placeholderKey: 'helpFeedback.phonePlaceholder',
		iconName: 'mobile',
		inputType: 'number',
		maxlength: 11,
		value: formData.value.phone,
		onInput: (e) => { formData.value.phone = e.detail.value }
	}
])

// placeholder项目配置数组
const placeholderItems = [
	'helpFeedback.includeItems.steps',
	'helpFeedback.includeItems.expected',
	'helpFeedback.includeItems.actual',
	'helpFeedback.includeItems.other'
]

// 提交反馈
const submitFeedback = () => {
	// 验证手机号
	if (formData.value.phone && !/^1[3-9]\d{9}$/.test(formData.value.phone)) {
		uni.showToast({
			title: $t('helpFeedback.validation.phoneError'),
			icon: 'none'
		})
		return
	}

	// 验证内容
	if (!formData.value.content.trim()) {
		uni.showToast({
			title: $t('helpFeedback.validation.contentRequired'),
			icon: 'none'
		})
		return
	}

	// 提交逻辑
	console.log('提交反馈:', formData.value)

	uni.showLoading({
		title: $t('helpFeedback.messages.submitting')
	})

	// 模拟提交
	setTimeout(() => {
		uni.hideLoading()
		uni.showToast({
			title: $t('helpFeedback.messages.submitSuccess'),
			icon: 'success'
		})

		// 清空表单
		formData.value = {
			nickname: '',
			phone: '',
			content: ''
		}

		// 延迟返回
		setTimeout(() => {
			uni.navigateBack()
		}, 1500)
	}, 2000)
}
</script>

<style lang="scss">
.help-feedback-container {
	min-height: 100vh;
	background: #f5f5f5;
}

/* 自定义导航栏 */
.custom-navbar {
	background: #ffffff;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	position: sticky;
	top: 0;
	z-index: 999;
}

.navbar-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 32rpx;
}

.navbar-left {
	width: 88rpx;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.navbar-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 44rpx;
	font-weight: 600;
	color: #333333;
}

.navbar-right {
	width: 88rpx;
}

/* 内容容器 */
.content-container {
	padding: 32rpx;
}

/* 反馈说明卡片 */
.feedback-notice {
	background: #e8f5e8;
	border-radius: 16rpx;
	padding: 32rpx;
	margin-bottom: 48rpx;
	border: 2rpx solid #c8e6c9;
}

.notice-header {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
}

.notice-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #2e7d32;
	margin-left: 16rpx;
}

.notice-content {
	padding-left: 52rpx;
}

.notice-text {
	font-size: 30rpx;
	color: #388e3c;
	line-height: 1.6;
}

/* 表单区域 */
.form-section {
	margin-bottom: 48rpx;
}

.section-title {
	margin-bottom: 24rpx;
}

.section-title .title-text {
	font-size: 36rpx;
	font-weight: 500;
	color: #333333;
}

/* 输入框容器 */
.input-container {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 24rpx 32rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	border: 2rpx solid #f0f0f0;
	transition: all 0.3s ease;
}

.input-container:focus-within {
	border-color: #4CAF50;
	box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.2);
}

.input-icon {
	margin-right: 24rpx;
	display: flex;
	align-items: center;
}

.input-field {
	flex: 1;
	font-size: 32rpx;
	color: #333333;
	border: none;
	outline: none;
}

.input-field::placeholder {
	color: #999999;
	font-size: 32rpx;
}

/* 文本域容器 */
.textarea-container {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	border: 2rpx solid #f0f0f0;
	transition: all 0.3s ease;
	position: relative;
}

.textarea-container:focus-within {
	border-color: #4CAF50;
	box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.2);
}

.textarea-icon {
	position: absolute;
	top: 32rpx;
	left: 32rpx;
	z-index: 2;
}

.textarea-content {
	position: relative;
	width: 550rpx;
	min-height: 240rpx;
	
}

/* 占位符内容 */
.placeholder-content {
	position: absolute;
	top: 0;
	left: 72rpx;
	right: 0;
	z-index: 1;
	pointer-events: none;
}

.placeholder-title {
	display: block;
	font-size: 32rpx;
	color: #999999;
	line-height: 1.6;
	margin-bottom: 16rpx;
}

.placeholder-subtitle {
	display: block;
	font-size: 30rpx;
	color: #999999;
	line-height: 1.6;
	margin-bottom: 12rpx;
}

.placeholder-list {
	display: flex;
	flex-direction: column;
}

.placeholder-item {
	display: block;
	font-size: 28rpx;
	color: #999999;
	line-height: 1.8;
	margin-bottom: 8rpx;
}

.textarea-field {
	width: 100%;
	min-height: 240rpx;
	font-size: 32rpx;
	color: #333333;
	border: none;
	outline: none;
	padding-left: 72rpx;
	line-height: 1.6;
	resize: none;
	background: transparent;
	position: relative;
	z-index: 3;
}

.textarea-field.has-content {
	background: #ffffff;
}

.textarea-field::placeholder {
	color: transparent;
}

/* 字符计数 */
.char-count {
	text-align: right;
	margin-top: 16rpx;
}

.count-text {
	font-size: 24rpx;
	color: #999999;
}

/* 字体大小主题样式 */
.font-size-small {
	.title-text {
		font-size: 36rpx !important;
	}

	.notice-title {
		font-size: 30rpx !important;
	}

	.notice-text {
		font-size: 26rpx !important;
	}

	.section-title .title-text {
		font-size: 30rpx !important;
	}

	.input-field,
	.textarea-field {
		font-size: 28rpx !important;
	}

	.input-field::placeholder,
	.textarea-field::placeholder {
		font-size: 28rpx !important;
	}

	.placeholder-title {
		font-size: 28rpx !important;
	}

	.placeholder-subtitle {
		font-size: 26rpx !important;
	}

	.placeholder-item {
		font-size: 24rpx !important;
	}

	.count-text {
		font-size: 20rpx !important;
	}

	.submit-text {
		font-size: 32rpx !important;
	}

	.bottom-notice .notice-text {
		font-size: 20rpx !important;
	}
}

.font-size-medium {
	.title-text {
		font-size: 44rpx !important;
	}

	.notice-title {
		font-size: 36rpx !important;
	}

	.notice-text {
		font-size: 30rpx !important;
	}

	.section-title .title-text {
		font-size: 36rpx !important;
	}

	.input-field,
	.textarea-field {
		font-size: 32rpx !important;
	}

	.input-field::placeholder,
	.textarea-field::placeholder {
		font-size: 32rpx !important;
	}

	.placeholder-title {
		font-size: 32rpx !important;
	}

	.placeholder-subtitle {
		font-size: 30rpx !important;
	}

	.placeholder-item {
		font-size: 28rpx !important;
	}

	.count-text {
		font-size: 24rpx !important;
	}

	.submit-text {
		font-size: 36rpx !important;
	}

	.bottom-notice .notice-text {
		font-size: 24rpx !important;
	}
}

.font-size-large {
	.title-text {
		font-size: 52rpx !important;
	}

	.notice-title {
		font-size: 44rpx !important;
	}

	.notice-text {
		font-size: 36rpx !important;
	}

	.section-title .title-text {
		font-size: 44rpx !important;
	}

	.input-field,
	.textarea-field {
		font-size: 40rpx !important;
	}

	.input-field::placeholder,
	.textarea-field::placeholder {
		font-size: 40rpx !important;
	}

	.placeholder-title {
		font-size: 40rpx !important;
	}

	.placeholder-subtitle {
		font-size: 36rpx !important;
	}

	.placeholder-item {
		font-size: 32rpx !important;
	}

	.count-text {
		font-size: 28rpx !important;
	}

	.submit-text {
		font-size: 40rpx !important;
	}

	.bottom-notice .notice-text {
		font-size: 28rpx !important;
	}
}

/* 提交按钮区域 */
.submit-section {
	margin: 64rpx 0 48rpx 0;
}

.submit-btn {
	background: linear-gradient(135deg, #109d58 0%, #109d58 100%);
	border-radius: 16rpx;
	height: 96rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);
	transition: all 0.3s ease;
}

.submit-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.submit-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 底部提示 */
.bottom-notice {
	padding: 32rpx 0;
	text-align: center;
}

.bottom-notice .notice-text {
	font-size: 24rpx;
	color: #999999;
	line-height: 1.6;
}
</style>
