// 全局国际化插件
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { t, getCurrentLanguage } from '@/locale/index.js'
import { useAppStore } from '@/store/app.js'

// 全局国际化管理器
class GlobalI18nManager {
  constructor() {
    this.currentLanguage = getCurrentLanguage()
    this.isInitialized = false
    this.pageInstances = new Set()
  }

  // 初始化国际化管理器
  init() {
    if (this.isInitialized) return
    
    try {
      const appStore = useAppStore()
      this.currentLanguage = appStore.lang
      
      // 监听语言变化事件
      uni.$on('languageChanged', this.handleLanguageChange.bind(this))
      uni.$on('languageFontChanged', this.handleLanguageChange.bind(this))
      
      this.isInitialized = true
      console.log('全局国际化管理器已初始化')
    } catch (error) {
      console.warn('初始化国际化管理器失败:', error)
    }
  }

  // 处理语言变化
  handleLanguageChange(data) {
    this.currentLanguage = data.language
    
    // 通知所有注册的页面实例更新
    this.notifyAllPages(data)
  }

  // 注册页面实例
  registerPage(pageInstance) {
    this.pageInstances.add(pageInstance)
  }

  // 注销页面实例
  unregisterPage(pageInstance) {
    this.pageInstances.delete(pageInstance)
  }

  // 通知所有页面更新
  notifyAllPages(data) {
    this.pageInstances.forEach(pageInstance => {
      try {
        if (pageInstance && pageInstance.updateLanguage) {
          pageInstance.updateLanguage(data)
        }
      } catch (error) {
        console.warn('通知页面更新语言失败:', error)
      }
    })
  }

  // 获取当前语言状态
  getCurrentLanguageState() {
    const appStore = useAppStore()
    return {
      language: appStore.lang,
      isUyghur: appStore.isUyghur,
      fontClass: {
        'ug': appStore.isUyghur,
        [`lang-${appStore.lang}`]: true
      }
    }
  }

  // 销毁管理器
  destroy() {
    try {
      uni.$off('languageChanged', this.handleLanguageChange)
      uni.$off('languageFontChanged', this.handleLanguageChange)
      this.pageInstances.clear()
      this.isInitialized = false
      console.log('全局国际化管理器已销毁')
    } catch (error) {
      console.warn('销毁国际化管理器失败:', error)
    }
  }
}

// 创建全局实例
export const globalI18nManager = new GlobalI18nManager()

// 初始化函数
export function initGlobalI18n() {
  globalI18nManager.init()
}

// 页面国际化混入
export function createI18nPageMixin() {
  return {
    data() {
      return {
        currentLanguage: getCurrentLanguage(),
        i18nUpdateKey: 0
      }
    },

    computed: {
      // 计算字体类
      fontClass() {
        const state = globalI18nManager.getCurrentLanguageState()
        return state.fontClass
      },

      // 计算语言状态
      languageState() {
        return globalI18nManager.getCurrentLanguageState()
      }
    },

    methods: {
      // 翻译方法
      $t(key) {
        return t(key)
      },

      // 更新语言
      updateLanguage(data) {
        this.currentLanguage = data.language
        this.i18nUpdateKey++
        this.$forceUpdate()
      },

      // 获取当前语言
      getCurrentLanguage() {
        return getCurrentLanguage()
      }
    },

    created() {
      // 注册到全局管理器
      globalI18nManager.registerPage(this)
    },

    beforeDestroy() {
      // 从全局管理器注销
      globalI18nManager.unregisterPage(this)
    },

    // Vue 3 兼容
    beforeUnmount() {
      // 从全局管理器注销
      globalI18nManager.unregisterPage(this)
    }
  }
}

// Vue 3 Composition API 版本
export function useI18n() {
  const appStore = useAppStore()
  
  const currentLanguage = ref(appStore.lang)
  const i18nUpdateKey = ref(0)

  // 计算字体类
  const fontClass = computed(() => ({
    'ug': appStore.isUyghur,
    [`lang-${appStore.lang}`]: true
  }))

  // 计算语言状态
  const languageState = computed(() => ({
    language: appStore.lang,
    isUyghur: appStore.isUyghur
  }))

  // 翻译方法
  const $t = (key) => t(key)

  // 更新语言
  const updateLanguage = (data) => {
    currentLanguage.value = data.language
    i18nUpdateKey.value++
  }

  // 页面实例对象
  const pageInstance = {
    updateLanguage
  }

  onMounted(() => {
    // 注册到全局管理器
    globalI18nManager.registerPage(pageInstance)
  })

  onUnmounted(() => {
    // 从全局管理器注销
    globalI18nManager.unregisterPage(pageInstance)
  })

  return {
    currentLanguage,
    i18nUpdateKey,
    fontClass,
    languageState,
    $t,
    updateLanguage
  }
}

// 自动应用国际化的指令
export const i18nDirective = {
  mounted(el, binding) {
    const key = binding.value
    if (key) {
      el.textContent = t(key)
    }
    
    // 监听语言变化
    const updateText = () => {
      if (key) {
        el.textContent = t(key)
      }
    }
    
    el._i18nUpdateHandler = updateText
    uni.$on('languageChanged', updateText)
    uni.$on('languageFontChanged', updateText)
  },
  
  updated(el, binding) {
    const key = binding.value
    if (key) {
      el.textContent = t(key)
    }
  },
  
  unmounted(el) {
    if (el._i18nUpdateHandler) {
      uni.$off('languageChanged', el._i18nUpdateHandler)
      uni.$off('languageFontChanged', el._i18nUpdateHandler)
      delete el._i18nUpdateHandler
    }
  }
}

// 工具函数：为现有页面快速添加国际化支持
export function addI18nToPage(pageOptions) {
  const originalData = pageOptions.data || (() => ({}))
  const originalMethods = pageOptions.methods || {}
  const originalCreated = pageOptions.created || (() => {})
  const originalBeforeDestroy = pageOptions.beforeDestroy || (() => {})

  return {
    ...pageOptions,
    
    data() {
      return {
        ...originalData.call(this),
        currentLanguage: getCurrentLanguage(),
        i18nUpdateKey: 0
      }
    },

    computed: {
      ...pageOptions.computed,
      
      fontClass() {
        const state = globalI18nManager.getCurrentLanguageState()
        return state.fontClass
      },

      languageState() {
        return globalI18nManager.getCurrentLanguageState()
      }
    },

    methods: {
      ...originalMethods,
      
      $t(key) {
        return t(key)
      },

      updateLanguage(data) {
        this.currentLanguage = data.language
        this.i18nUpdateKey++
        this.$forceUpdate()
      }
    },

    created() {
      originalCreated.call(this)
      globalI18nManager.registerPage(this)
    },

    beforeDestroy() {
      originalBeforeDestroy.call(this)
      globalI18nManager.unregisterPage(this)
    }
  }
}
