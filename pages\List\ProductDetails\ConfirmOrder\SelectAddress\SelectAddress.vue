<template>
	<view class="select-address-page" :class="fontSizeClass" :key="fontSizeUpdateKey">
		<!-- 空状态显示 -->
		<view v-if="addressList.length === 0" class="main-content">
			<view class="empty-state">
				<!-- 位置图标 -->
				<view class="location-icon">
					<text class="icon-text">📍</text>
					<view class="icon-slash"></view>
				</view>

				<!-- 提示文字 -->
				<text class="empty-title">暂无收货地址</text>
				<text class="empty-desc">点击右下角添加地址</text>
			</view>
		</view>

		<!-- 地址列表 -->
		<scroll-view v-else class="address-list" scroll-y="true">
			<fui-swipe-action
				v-for="(address, index) in addressList"
				:key="index"
				:options="swipeOptions"
				@click="handleSwipeClick($event, address, index)"
				class="custom-swipe-action"
			>
				<view class="address-item" @tap="selectAddress(address)">
					<view class="address-info">
						<view class="address-header">
							<text class="receiver-name">{{ address.name }}</text>
							<view class="phone-tags-container">
								<text class="receiver-phone">{{ address.phone }}</text>
								<view v-if="address.isDefault" class="default-tag">默认</view>
								<view v-if="address.tag" class="address-tag">{{ address.tag }}</view>
							</view>
						</view>
						<text class="address-detail">{{ address.region }} {{ address.address }}</text>
					</view>

					<!-- 编辑按钮 -->
					<view class="edit-button" @tap.stop="editAddress(address, index)">
						<text class="edit-icon">🖊️</text>
					</view>
				</view>
			</fui-swipe-action>
		</scroll-view>

		<!-- 右下角添加按钮 -->
		<view class="add-button" @tap="addAddress">
			<text class="add-icon">+</text>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onShow as onShowHook } from '@dcloudio/uni-app'
import { useFontSizePage } from '@/utils/fontSizeMixin.js'

// 地址列表
const addressList = ref([])

// 使用字体大小功能
const { fontSizeClass, fontSizeUpdateKey } = useFontSizePage()

// 滑动菜单配置
const swipeOptions = ref([
	{
		text: '删除',
		style: {
			backgroundColor: '#FF3B30',
			color: '#FFFFFF',
			fontSize: '28rpx'
		}
	}
])

// 页面挂载时加载地址列表
onMounted(() => {
	loadAddressList()
})

// uni-app 页面生命周期 - 页面显示时
onShowHook(() => {
	loadAddressList()
})

// 加载地址列表
const loadAddressList = () => {
	const list = uni.getStorageSync('addressList') || []
	addressList.value = list

	// 如果没有地址，添加一个测试地址用于样式预览（开发时使用）
	if (list.length === 0) {
		// 可以取消注释下面的代码来添加测试数据
		/*
		const testAddress = {
			id: Date.now(),
			name: 'qqqqq',
			phone: '17399652363',
			region: '新疆维吾尔自治区 乌鲁木齐市 天山区',
			address: '111111111',
			isDefault: true
		}
		addressList.value = [testAddress]
		uni.setStorageSync('addressList', [testAddress])
		*/
	}
}

// 添加地址
const addAddress = () => {
	uni.navigateTo({
		url: '/pages/List/ProductDetails/ConfirmOrder/AddAddress/AddAddress'
	})
}

// 编辑地址
const editAddress = (address, index) => {
	const addressData = encodeURIComponent(JSON.stringify(address))
	uni.navigateTo({
		url: `/pages/List/ProductDetails/ConfirmOrder/EditAddress/EditAddress?addressData=${addressData}&index=${index}`
	})
}

// 处理滑动菜单点击
const handleSwipeClick = (e, address, index) => {
	console.log('滑动菜单点击:', e, address, index)

	if (e.index === 0) { // 删除按钮
		deleteAddress(address, index)
	}
}

// 删除地址
const deleteAddress = (address, index) => {
	uni.showModal({
		title: '确认删除',
		content: `确定要删除地址"${address.name} ${address.phone}"吗？`,
		confirmColor: '#FF3B30',
		success: (res) => {
			if (res.confirm) {
				// 从本地存储中删除
				const existingAddresses = uni.getStorageSync('addressList') || []
				existingAddresses.splice(index, 1)
				uni.setStorageSync('addressList', existingAddresses)

				// 更新页面数据
				addressList.value.splice(index, 1)

				uni.showToast({
					title: '删除成功',
					icon: 'success'
				})

				console.log('地址删除成功，剩余地址数量:', addressList.value.length)
			}
		}
	})
}

// 选择地址（返回到确认订单页面）
const selectAddress = (address) => {
	console.log('选择地址:', address)

	// 方案1：保存到本地存储（最可靠）
	uni.setStorageSync('selectedAddressData', {
		name: address.name,
		phone: address.phone,
		region: address.region,
		address: address.address,
		fullAddress: `${address.region} ${address.address}`,
		timestamp: Date.now()
	})
	console.log('地址数据已保存到本地存储')

	// 方案2：页面实例直接设置数据
	const pages = getCurrentPages()
	const prevPage = pages[pages.length - 2]

	if (prevPage) {
		console.log('找到上一页:', prevPage.route)

		if (prevPage.$vm) {
			prevPage.$vm.selectedAddress = address.region
			prevPage.$vm.receiverName = address.name
			prevPage.$vm.receiverPhone = address.phone
			prevPage.$vm.receiverAddress = address.address
			console.log('通过页面实例设置数据完成')
		}

		// 方案3：事件总线传递数据
		uni.$emit('addressSelected', {
			name: address.name,
			phone: address.phone,
			region: address.region,
			address: address.address,
			fullAddress: `${address.region} ${address.address}`
		})
		console.log('通过事件总线发送数据完成')
	}

	// 返回上一页
	uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.select-address-page {
	width: 100%;
	height: 100vh;
	background-color: #f5f5f5;
	position: relative;
	display: flex;
	flex-direction: column;
}

/* 地址内容区域 */
.address-content {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
}

/* 主要内容区域 */
.main-content {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 60rpx;
}

/* 空状态样式 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

/* 位置图标 */
.location-icon {
	position: relative;
	width: 120rpx;
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;
}

.icon-text {
	font-size: 80rpx;
	color: #cccccc;
	opacity: 0.8;
}

/* 斜杠禁用效果 */
.icon-slash {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%) rotate(45deg);
	width: 100rpx;
	height: 4rpx;
	background-color: #cccccc;
	opacity: 0.8;
}

/* 提示文字 */
.empty-title {
	font-size: 36rpx;
	color: #666666;
	font-weight: 500;
	margin-bottom: 16rpx;
	text-align: center;
}

.empty-desc {
	font-size: 28rpx;
	color: #999999;
	text-align: center;
	line-height: 1.5;
}

/* 地址列表 */
.address-list {
	flex: 1;
	padding: 32rpx 24rpx;
	height: 100vh;
	
}

.address-item {
	background-color: #ffffff;
	border-radius: 32rpx;
	padding: 48rpx 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
	position: relative;
	border: 1rpx solid #f0f0f0;
	margin: 0;
}

.address-info {
	width: 100%;
	padding-right: 80rpx;
	
}

.address-header {
	display: flex;
	align-items: flex-start;
	margin-bottom: 16rpx;
	position: relative;
}

.receiver-name {
	font-size: 32rpx;
	color: #333333;
	font-weight: 600;
	margin-right: 24rpx;
	flex-shrink: 0;
}

.phone-tags-container {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	gap: 12rpx;
	flex: 1;
}

.receiver-phone {
	font-size: 32rpx;
	color: #333333;
	font-weight: 400;
}

.default-tag {
	background-color: #4CAF50;
	color: #ffffff;
	font-size: 20rpx;
	padding: 6rpx 12rpx;
	border-radius: 8rpx;
	font-weight: 500;
}

.address-tag {
	background-color: #e9f5f1;
	color: #0f9d58;
	font-size: 20rpx;
	padding: 6rpx 12rpx;
	border-radius: 8rpx;
	font-weight: 400;
}

.address-detail {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.5;
	margin-bottom: 0;
	padding-right: 100rpx;
}

/* 编辑按钮 */
.edit-button {
	position: absolute;
	top: 100rpx;
	right: 50rpx;
	width: 70rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 20rpx;
	background-color: rgba(76, 175, 80, 0.1);
	transition: all 0.3s ease;
}

.edit-button:active {
	transform: scale(0.9);
	background-color: rgba(76, 175, 80, 0.2);
}

.edit-icon {
	font-size: 28rpx;
	color: #4CAF50;
}


/* 滑动删除样式 */
.custom-swipe-action {
	margin-bottom: 32rpx;
}

/* 滑动删除按钮样式 */
::v-deep .fui-swipe__btn {
	height: 100% !important;
	min-height: 100% !important;
	border-radius: 0 !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
}

/* 更通用的选择器适配不同版本的组件 */
::v-deep [class*="swipe"] [class*="btn"] {
	height: 100% !important;
	min-height: 100% !important;
	border-radius: 0 !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
}



/* 右下角添加按钮 */
.add-button {
	position: fixed;
	bottom: 80rpx;
	right: 60rpx;
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
	border-radius: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.4);
	z-index: 100;
}

.add-button:active {
	transform: scale(0.95);
	box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.4);
}

.add-icon {
	font-size: 48rpx;
	color: #ffffff;
	font-weight: 300;
	line-height: 1;
}
</style>
