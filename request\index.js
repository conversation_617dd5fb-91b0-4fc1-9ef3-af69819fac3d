import { request } from './api.js';
import {BASE_URL} from '@/main.js'
const version='v1'

// 产品API
export const productApi = {
  // 获取产品列表
  getProductList: (params = {}) => {
    return request({
      url: `/${version}/products`,
      method: 'GET',
      data: params
    });
  },

  // 获取产品详情
  getProductDetail: (id) => {
    return request({
      url: `${version}/products/${id}`,
      method: 'GET'
    });
  },

  // 🆕 获取医生的产品接口（用户端）
  getDoctorProducts: (doctorId, params = {}) => {
    return request({
      url: `${version}/products/doctor/${doctorId}`,
      method: 'GET',
      data: params
    });
  },

  // 🆕 获取产品分类接口
  getProductCategories: () => {
    return request({
      url: `${version}/products/categories`,
      method: 'GET'
    });
  }
};

// 医生API
export const doctorApi = {
  // 获取医生列表
  getDoctorList: (params = {}) => {
    return request({
      url: `${version}/doctors`,
      method: 'GET',
      data: params
    });
  },

  // 获取医生详情
  getDoctorDetail: (id) => {
    return request({
      url: `${version}/doctors/${id}`,
      method: 'GET'
    });
  },

  // 🆕 点赞医生接口
  likeDoctorApi: (doctorId) => {
    return request({
      url: `${version}/doctors/${doctorId}/like`,
      method: 'POST'
    });
  },

  // 🆕 取消点赞医生接口
  unlikeDoctorApi: (doctorId) => {
    return request({
      url: `${version}/doctors/${doctorId}/like`,
      method: 'DELETE'
    });
  },

  // 🆕 获取用户点赞的医生列表接口
  getLikedDoctors: (params = {}) => {
    return request({
      url: `${version}/doctors/likes`,
      method: 'GET',
      data: params
    });
  },

  // 🆕 获取医生互动状态接口（包含点赞状态、收藏状态、点赞数、收藏数）
  getDoctorStatus: (doctorId) => {
    return request({
      url: `${version}/doctors/${doctorId}/status`,
      method: 'GET'
    });
  },

  // 🆕 收藏医生接口
  favoriteDoctorApi: (doctorId) => {
    return request({
      url: `${version}/doctors/${doctorId}/favorite`,
      method: 'POST'
    });
  },

  // 🆕 取消收藏医生接口
  unfavoriteDoctorApi: (doctorId) => {
    return request({
      url: `${version}/doctors/${doctorId}/favorite`,
      method: 'DELETE'
    });
  }
};

// 微信登录
export const wechat_Authenticator = (code, dis_code = '') => {
  const data = { code };
  if (dis_code) {
    data.dis_code = dis_code;
  }
  return request({
    url: 'user/login',
    method: 'GET',
    data: data
  });
};

// 获取用户信息
export const userinfo = () => {
	return request({
		url: `${version}/app/profile`,
		method: 'GET',
	});
};

// 更新用户信息
export const updateUserInfo = (data) => {
	return request({
		url: `${version}/app/update_profile`,
		method: 'POST',
		data
	});
};

// 获取token的函数（与api.js保持一致）
const getToken = () => {
	return uni.getStorageSync('auth_token') || uni.getStorageSync('session_key');
};

// 上传用户头像
export const uploadUserAvatar = (filePath) => {
	return new Promise((resolve, reject) => {
		const token = getToken();

		if (!token) {
			reject(new Error('未找到有效的登录token，请重新登录'));
			return;
		}

		console.log('📤 头像上传API调用:', `${BASE_URL}${version}/app/upload_avatar`);
		console.log('🔑 使用Token:', token.substring(0, 10) + '...');

		uni.uploadFile({
			url: `${BASE_URL}${version}/app/upload_avatar`,
			filePath: filePath,
			name: 'avatar',
			header: {
				'Authorization': `Bearer ${token}`
			},
			success: (uploadRes) => {
				try {
					const result = JSON.parse(uploadRes.data);
					if (result.code === 200) {
						resolve(result);
					} else {
						reject(new Error(result.message || '头像上传失败'));
					}
				} catch (e) {
					console.error('解析上传响应失败:', e);
					reject(new Error('响应解析失败'));
				}
			},
			fail: (error) => {
				console.error('头像上传请求失败:', error);
				reject(error);
			}
		});
	});
};

// 完善的用户资料API
export const profileApi = {
	// 更新基本资料
	updateBasicProfile: (data) => {
		return request({
			url: 'user/profile/basic',
			method: 'PUT',
			data: {
				nickname: data.nickname,
				gender: data.gender,
				birthday: data.birthday,
				height: data.height,
				weight: data.weight,
				location: data.location,
				phone: data.phone
			}
		});
	},

	// 更新健康档案
	updateHealthProfile: (data) => {
		return request({
			url: 'user/profile/health',
			method: 'PUT',
			data: {
				bloodType: data.bloodType,
				hasAllergy: data.hasAllergy,
				allergens: data.allergens,
				otherAllergies: data.otherAllergies,
				hasMedication: data.hasMedication,
				medicationList: data.medicationList,
				hasChronicDisease: data.hasChronicDisease,
				chronicDiseases: data.chronicDiseases,
				otherChronicDiseases: data.otherChronicDiseases,
				hypertensionRange: data.hypertensionRange,
				diabetesRange: data.diabetesRange,
				hasSurgery: data.hasSurgery,
				surgeryDetails: data.surgeryDetails,
				familyDiseases: data.familyDiseases
			}
		});
	},

	// 更新生活方式
	updateLifestyleProfile: (data) => {
		return request({
			url: 'user/profile/lifestyle',
			method: 'PUT',
			data: {
				exerciseFrequency: data.exerciseFrequency,
				smokingHistory: data.smokingHistory,
				drinkingHistory: data.drinkingHistory,
				sleepDuration: data.sleepDuration,
				sleepQuality: data.sleepQuality,
				stressLevel: data.stressLevel,
				dietPreferences: data.dietPreferences
			}
		});
	},

	// 更新女性健康信息
	updateFemaleHealthProfile: (data) => {
		return request({
			url: 'user/profile/female-health',
			method: 'PUT',
			data: {
				isMenopause: data.isMenopause,
				menstrualCycle: data.menstrualCycle,
				hasPregnancy: data.hasPregnancy,
				birthCount: data.birthCount
			}
		});
	},

	// 一次性更新完整资料
	updateCompleteProfile: (data) => {
		return request({
			url: 'user/profile/complete',
			method: 'PUT',
			data
		});
	}
};

// 传递用户头像和名称 - 使用 uploadFile 上传文件
export const useravatr = (filePath, nickname) => {
	return new Promise((resolve, reject) => {
		// 获取token
		const token = uni.getStorageSync('auth_token');

		uni.uploadFile({
			url: `${BASE_URL}user/authorization`,
			filePath: filePath,
			name: 'file',
			formData: {
				'nickname': nickname.trim()
			},
			header: {
				'Authorization': `Bearer ${token}`
			},
			success: (res) => {
				try {
					const result = JSON.parse(res.data);
					if (result.code === 200) {
						resolve(result);
					} else {
						reject(new Error(result.message || '授权失败'));
					}
				} catch (e) {
					reject(new Error('响应解析失败'));
				}
			},
			fail: (error) => {
				reject(error);
			}
		});
	});
};

// ==================== 健康档案相关API - 已移除 ====================

// 订单API
export const orderApi = {
  // 创建订单
  createOrder: (data) => {
    return request({
      url: '/v1/products/orders',
      method: 'POST',
      data
    });
  },

  // 获取我的订单
  getMyOrders: (params = {}) => {
    return request({
      url: '/v1/products/orders/my',
      method: 'GET',
      data: params
    });
  }
};

// 购物车API
export const cartApi = {
  // 获取购物车
  getCartList: () => {
    return request({
      url: '/v1/cart',
      method: 'GET'
    });
  },

  // 添加到购物车
  addToCart: (data) => {
    return request({
      url: '/v1/cart/add',
      method: 'POST',
      data
    });
  }
};

// 地址API
export const addressApi = {
  // 获取地址列表
  getAddressList: () => {
    return request({
      url: '/v1/user/addresses',
      method: 'GET'
    });
  },

  // 创建地址
  createAddress: (data) => {
    return request({
      url: '/v1/user/addresses',
      method: 'POST',
      data
    });
  }
};



// 用户API
export const userApi = {
  // 获取用户信息
  getUserInfo: () => {
    return request({
      url: '/v1/user/info',
      method: 'GET'
    });
  },

  // 更新用户信息
  updateUserInfo: (data) => {
    return request({
      url: '/v1/user/update',
      method: 'PUT',
      data
    });
  }
};



// 健康档案API - 已移除

// 分销功能API
export const distributionApi = {
  // 申请成为分销员
  applyDistributor: (data) => {
    return request({
      url: 'user/apply_ref',
      method: 'POST',
      data: {
        real_name: data.real_name,
        id_card: data.id_card,
        reason: data.reason
      }
    });
  },

  // 获取分销员收入信息
  getDistributorIncome: () => {
    return request({
      url: 'user/get_moneys',
      method: 'GET'
    });
  },

  // 获取余额变动记录
  getBalanceRecords: (params = {}) => {
    return request({
      url: 'user/get_balance_r',
      method: 'GET',
      data: {
        page: params.page || 1,
        limit: params.limit || 20,
        type: params.type // income, expense
      }
    });
  },

  // 获取下级用户列表
  getSubordinateUsers: (params = {}) => {
    return request({
      url: 'user/l_level_users',
      method: 'GET',
      data: {
        page: params.page || 1,
        limit: params.limit || 20,
        level: params.level || 1
      }
    });
  },

  // 修改下级分销等级
  updateSubordinateLevel: (userId, level) => {
    return request({
      url: 'user/up_dist_level',
      method: 'POST',
      data: {
        user_id: userId,
        level: level
      }
    });
  },

  // 获取分销海报列表
  getDistributionPosters: () => {
    return request({
      url: 'user/get_poster',
      method: 'GET'
    });
  },

  // 获取分销等级列表
  getDistributionLevels: () => {
    return request({
      url: 'index/dist_level',
      method: 'GET'
    });
  }
};

// 默认导出所有API
export default {
  productApi,
  doctorApi,
  orderApi,
  cartApi,
  addressApi,
  userApi,
  // healthProfileApi, // 已移除
  distributionApi,  // 分销API
  wechat_Authenticator,
  userinfo,
  updateUserInfo,
  uploadUserAvatar,
  useravatr
};