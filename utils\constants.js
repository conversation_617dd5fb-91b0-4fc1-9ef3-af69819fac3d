/**
 * 🔧 项目统一常量定义文件
 *
 * 这个文件定义了整个项目中使用的所有常量
 * 目的是避免在代码中使用魔法数字和重复的字符串
 * 便于维护和统一管理项目中的配置值
 *
 * 📋 包含的常量类型：
 * - UI相关常量（Toast、尺寸、按钮文本等）
 * - 业务相关常量（状态码、默认值等）
 * - 配置相关常量（API地址、超时时间等）
 */

// ==================== 🎨 UI相关常量 ====================
export const UI_CONSTANTS = {

  // 🍞 Toast提示相关配置
  TOAST_DURATION: 2000,  // Toast显示持续时间（毫秒）
  TOAST_ICONS: {
    SUCCESS: 'success',   // 成功图标标识
    ERROR: 'error',       // 错误图标标识
    LOADING: 'loading',   // 加载图标标识
    NONE: 'none'          // 无图标标识
  },

  // 📝 文本输入限制
  // 用于统一管理各种输入框的字符长度限制
  TEXT_LIMITS: {
    INPUT_MAX: 500,        // 普通输入框最大字符数
    TEXTAREA_MAX: 1000,    // 多行文本框最大字符数
    TITLE_MAX: 50,         // 标题最大字符数
    DESCRIPTION_MAX: 200   // 描述最大字符数
  },

  // 📏 尺寸常量
  // 统一管理图标和字体的标准尺寸（单位：rpx）
  SIZES: {
    // 图标尺寸
    ICON_SM: 24,    // 小图标：24rpx
    ICON_MD: 30,    // 中图标：30rpx
    ICON_LG: 40,    // 大图标：40rpx
    ICON_XL: 64,    // 超大图标：64rpx

    // 字体尺寸
    FONT_SM: 28,    // 小字体：28rpx
    FONT_MD: 32,    // 中字体：32rpx
    FONT_LG: 36,    // 大字体：36rpx
    FONT_XL: 40     // 超大字体：40rpx
  },

  // 🔘 按钮文本常量
  // 统一管理常用按钮的文字，便于国际化和统一修改
  BUTTON_TEXTS: {
    CONFIRM: '确定',   // 确定按钮
    CANCEL: '取消',    // 取消按钮
    SUBMIT: '提交',    // 提交按钮
    SAVE: '保存',      // 保存按钮
    DELETE: '删除',    // 删除按钮
    EDIT: '编辑',      // 编辑按钮
    ADD: '添加',       // 添加按钮
    CLOSE: '关闭'      // 关闭按钮
  },

  // 🔲 边框圆角常量
  // 统一管理UI组件的圆角大小
  BORDER_RADIUS: {
    SM: '8rpx',
    MD: '16rpx',
    LG: '24rpx',
    XL: '32rpx',
    CIRCLE: '50%'
  },

  // ⏱️ 时间常量
  // 统一管理各种时间相关的配置
  TIMING: {
    TOAST_SHORT: 1500,      // 短提示时间（毫秒）
    TOAST_NORMAL: 2000,     // 普通提示时间（毫秒）
    TOAST_LONG: 3000,       // 长提示时间（毫秒）
    RECORD_MAX: 60000,      // 录音最长时间（毫秒）
    ANIMATION_FAST: 200,    // 快速动画时间（毫秒）
    ANIMATION_NORMAL: 300,  // 普通动画时间（毫秒）
    ANIMATION_SLOW: 500     // 慢速动画时间（毫秒）
  },
  
  // 间距
  SPACING: {
    XS: '8rpx',
    SM: '16rpx',
    MD: '24rpx',
    LG: '32rpx',
    XL: '48rpx',
    XXL: '64rpx'
  },
  
  // 动画时长
  ANIMATION_DURATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500
  }
}

// 颜色常量
export const COLOR_CONSTANTS = {
  // 主色系
  PRIMARY: '#465CFF',
  PRIMARY_LIGHT: '#6B7FFF',
  PRIMARY_DARK: '#2D4BFF',
  
  // 功能色
  SUCCESS: '#09BE4F',
  WARNING: '#FFB703',
  DANGER: '#FF2B2B',
  INFO: '#8f939c',
  PURPLE: '#6831FF',
  
  // 文字颜色
  TEXT_PRIMARY: '#181818',
  TEXT_REGULAR: '#333333',
  TEXT_SECONDARY: '#7F7F7F',
  TEXT_PLACEHOLDER: '#B2B2B2',
  TEXT_DISABLED: '#CCCCCC',
  TEXT_WHITE: '#FFFFFF',
  
  // 背景颜色
  BG_WHITE: '#FFFFFF',
  BG_PAGE: '#F8F8F8',
  BG_CONTENT: '#F1F4FA',
  BG_HOVER: 'rgba(0, 0, 0, 0.1)',
  BG_MASK: 'rgba(0, 0, 0, 0.6)',
  
  // 边框颜色
  BORDER_BASE: '#EEEEEE',
  BORDER_LIGHT: '#F2F2F2',
  BORDER_DARK: '#CCCCCC',
  
  // 功能性颜色
  ICON_GRAY: '#999999',
  GREEN_ACCENT: '#109d58',
  SHADOW: 'rgba(2, 4, 38, 0.05)'
}

// 页面路径常量
export const PAGE_PATHS = {
  INDEX: '/pages/index/index',
  LIST: '/pages/List/List',
  MY: '/pages/My/My',
  CAMERA: '/pages/camera/camera',
  PHOTO_SELECT: '/pages/photo-select/photo-select',
  SETTINGS: '/pages/My/Settings/Settings',
  HELP_FEEDBACK: '/pages/My/Settings/HelpFeedback/HelpFeedback'
}

// 存储键名常量
export const STORAGE_KEYS = {
  FONT_SIZE: 'fontSize',
  LANGUAGE: 'language',
  THEME: 'theme',
  USER_INFO: 'userInfo',
  SELECTED_DOCTOR: 'selectedDoctor',
  CHAT_HISTORY: 'chatHistory'
}

// 语言代码常量
export const LANGUAGE_CODES = {
  CHINESE: 'zh-CN',
  UYGHUR: 'ug',
  ENGLISH: 'en'
}

// 字体大小常量
export const FONT_SIZES = {
  SMALL: 'small',
  MEDIUM: 'medium', 
  LARGE: 'large'
}

// API相关常量
export const API_CONSTANTS = {
  TIMEOUT: 10000,
  RETRY_COUNT: 3,
  SUCCESS_CODE: 200,
  ERROR_CODES: {
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    SERVER_ERROR: 500
  }
}

// 表单验证常量
export const VALIDATION = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^1[3-9]\d{9}$/,
  PASSWORD_MIN_LENGTH: 6,
  PASSWORD_MAX_LENGTH: 20
}

// 默认值常量
export const DEFAULTS = {
  AVATAR: '/static/icon/user.svg',
  PRODUCT_IMAGE: '/static/images/default-product.png',
  DOCTOR_AVATAR: '/static/icon/doctor-default.png'
}
