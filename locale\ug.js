// ئۇيغۇرچە تىل بوغچىسى (Uyghur language pack)
export default {
  // ئادەتتىكى
  common: {
    confirm: 'جەزملەشتۈرۈش',
    cancel: 'بىكار قىلىش',
    save: 'ساقلاش',
    themeSwitch: 'تېما ئالماشتۇرۇش',
    confirmThemeSwitch: 'تېما ھالىتىنى ئالماشتۇرۇش پروگراممىنى قايتا قوزغىتىشنى تەلەپ قىلىدۇ، دەرھال قايتا قوزغىتامسىز؟',
    delete: 'ئۆچۈرۈش',
    edit: 'تەھرىرلەش',
    add: 'قوشۇش',
    search: 'ئىزدەش',
    loading: 'يۈكلىنىۋاتىدۇ...',
    noData: 'سانلىق مەلۇمات يوق',
    retry: 'قايتا سىناش',
    back: 'قايتىش',
    next: 'كېيىنكى',
    previous: 'ئالدىنقى',
    submit: 'تاپشۇرۇش',
    reset: 'ئەسلىگە كەلتۈرۈش',
    close: 'يېپىش',
    open: 'ئېچىش',
    select: 'تاللاش',
    selectAll: 'ھەممىنى تاللاش',
    clear: 'تازىلاش',
    refresh: 'يېڭىلاش',
    more: 'تېخىمۇ كۆپ',
    less: 'ئاز',
    expand: 'كېڭەيتىش',
    collapse: 'قاتلاش',
    workingHours: 'دۈشەنبىدىن جۈمىگىچە، چۈشتىن بۇرۇن 9:00-12:00',
    productSafetyNote: 'بۇ مەھسۇلات قاتتىق سۈپەت تەكشۈرۈشتىن ئۆتكەن، بىخەتەر ۋە ئۈنۈملۈك. قوللانما بويىچە ئىشلىتىڭ، ئەگەر ياخشى ھېس قىلمىسىڭىز دوختۇرغا ۋاقتىدا مەسلىھەت سوراڭ.',
    // يېڭى قوشۇلغان ئادەتتىكى سۆزلەر
    copy: 'كۆچۈرۈش',
    share: 'ھەمبەھىرلەش',
    play: 'قويۇش',
    pause: 'توختىتىش',
    error: 'خاتالىق',
    networkError: 'تور ئۇلىنىشى مەغلۇپ بولدى، تور تەڭشىكىنى تەكشۈرۈڭ',
    permissionDenied: 'ھوقۇق رەت قىلىندى',
    cameraPermissionRequired: 'كامېرا ھوقۇقى كېرەك',
    microphonePermissionRequired: 'مىكروفون ھوقۇقى كېرەك',
    processing: 'بىر تەرەپ قىلىۋاتىدۇ...',
    listening: 'ئاڭلاۋاتىدۇ...',
    clearHistory: 'تارىخنى تازىلاش',
    restart: 'قايتا قوزغىتىش'
  },

  // يولباشچى ۋە بەلگە
  nav: {
    home: 'ساغلاملىق ياردەمچىسى',
    list: 'تارىخ',
    my: 'مېنىڭ',
    settings: 'تەڭشەكلەر',
    profile: 'شەخسىي مەلۇمات',
    about: 'ھەققىدە',
    help: 'ياردەم',
    feedback: 'پىكىر بىلدۈرۈش'
  },

  // باش بەت
  home: {
    title: 'ساغلاملىق ياردەمچىسى',
    welcome: 'ساغلاملىق ياردەمچىسىگە خوش كەلدىڭىز',
    welcomeTo: 'سىزنى قارشى ئالىمىز',
    smartGuide: 'ئەقلىي يېتەكچى',
    searchPlaceholder: 'ساغلاملىق مەسىلىڭىزنى كىرگۈزۈڭ...',
    inputPlaceholder: 'سوئالىڭىزنى كىرگۈزۈڭ...',
    voiceInput: 'ئاۋاز كىرگۈزۈش',
    textInput: 'تېكىست كىرگۈزۈش',
    send: 'ئەۋەتىش',
    sendMessage: 'ئۇچۇر ئەۋەتىش',
    aiConsultation: 'AI مەسلىھەت',
    doctorList: 'دوختۇر تىزىملىكى',
    healthRecords: 'ساغلاملىق ھۆججىتى',
    quickConsult: 'تېز مەسلىھەت',
    emergencyCall: 'جىددىي چاقىرىق',
    healthTips: 'ساغلاملىق ئەسكەرتىشى',
    newChat: 'يېڭى سۆھبەت',
    holdToSpeak: 'بېسىپ سۆزلەڭ',
    releaseToSend: 'قويۇپ بېرىڭ',
    recording: 'ئۈن ئېلىۋاتىدۇ',
    releaseToCancel: 'قويۇپ بىكار قىلىڭ',
    slideUpToCancel: 'يۇقىرىغا سۈرۈپ بىكار قىلىڭ',
    continueSlideToCancel: 'ئۈن ئېلىشنى بىكار قىلىش ئۈچۈن يۇقىرىغا سۈرۈشنى داۋاملاشتۇرۇڭ',
    recordingCancelled: 'ئۈن ئېلىش بىكار قىلىندى',
    recognizingVoice: 'ئاۋازنى تونۇۋاتىدۇ...',
    recognitionSuccess: 'تونۇش مۇۋەپپەقىيەتلىك، ئەۋەتىۋاتىدۇ...',
    recordingFailed: 'ئۈن ئېلىش مەغلۇپ بولدى',
    unknownError: 'نامەلۇم خاتالىق',
    permissionRequest: 'ھوقۇق تەلىپى',
    recordPermissionNeeded: 'ئاۋاز كىرگۈزۈش ئىقتىدارىنى ئىشلىتىش ئۈچۈن ئۈن ئېلىش ھوقۇقى كېرەك',
    switchedToDoctor: '{name}غا ئالماشتۇرۇلدى',
    addedDoctor: '{name} قوشۇلدى',
    newChatStarted: 'يېڭى سۆھبەت باشلاندى',
    loadedChatWith: '{name} بىلەن بولغان سۆھبەت يۈكلەندى',
    editFeatureInDevelopment: 'تەھرىرلەش ئىقتىدارى ئىشلەپ چىقىلىۋاتىدۇ'
  },

  // دوختۇر تىزىملىكى
  doctorList: {
    title: 'دوختۇر تىزىملىكى',
    searchDoctor: 'دوختۇر ئىزدەش',
    specialty: 'مۇتەخەسسىسلىك',
    experience: 'تەجرىبە',
    rating: 'باھالاش',
    consultation: 'مەسلىھەت',
    appointment: 'ۋاقىت بېكىتىش',
    online: 'توردا',
    offline: 'تورسىز',
    busy: 'ئالدىراش',
    cardiology: 'يۈرەك قان تومۇر ئىچكى دوختۇرلۇقى',
    neurology: 'نېرۋا ئىچكى دوختۇرلۇقى',
    gastroenterology: 'ھەزىم ئىچكى دوختۇرلۇقى',
    endocrinology: 'ئىچكى ئاجرىتىش دوختۇرلۇقى',
    orthopedics: 'سۆڭەك دوختۇرلۇقى',
    dermatology: 'تېرە دوختۇرلۇقى',
    pediatrics: 'بالىلار دوختۇرلۇقى',
    gynecology: 'ئايال دوختۇرلۇقى',
    respiratoryMedicine: 'نەپەس ئالىش ئىچكى دوختۇرلۇقى',
    nephrology: 'بۆرەك ئىچكى دوختۇرلۇقى',
    obstetrics: 'تۇغۇت ۋە ئايال دوختۇرلۇقى',
    defaultDoctor: 'لى نا دوختۇر',
    defaultTitle: 'دوختۇر',
    defaultDepartment: 'ھەزىم ئىچكى دوختۇرلۇقى'
  },

  // دوختۇر ئايلاندۇرۇش بۆلىكى
  doctorSwiper: {
    introduction: 'كەسپىي تونۇشتۇرۇش',
    specialties: 'ماھارەت رايونى',
    experience: 'خىزمەت يىلى',
    rating: 'باھالاش'
  },

  // مەھسۇلات تىزىملىكى
  productList: {
    title: 'مەھسۇلات تىزىملىكى',
    medicine: 'دورا',
    healthProducts: 'ساغلاملىق مەھسۇلاتلىرى',
    medicalDevices: 'تىببىي ئۈسكۈنىلەر',
    price: 'باھا',
    addToCart: 'سېتىۋېلىش سەۋىتىگە قوشۇش',
    buyNow: 'دەرھال سېتىۋېلىش',
    outOfStock: 'تۈگەپ كەتتى',
    inStock: 'بار',
    prescription: 'رېتسېپت دورىسى',
    otc: 'رېتسېپتسىز دورا',
    searchProduct: 'مەھسۇلات ئىزدەش',
    productsCount: 'مەھسۇلات',
    manufacturer: 'دوختۇر',
    originalPrice: 'ئەسلى باھا'
  },

  // تىزىملىك بېتى
  list: {
    // ئىزدەش
    searchDoctor: 'دوختۇر ئىزدەش',
    searchProduct: 'مەھسۇلات ئىزدەش',

    // دوختۇر مۇناسىۋەتلىك
    doctor: 'دوختۇر',
    years: 'يىل',
    consult: 'مەسلىھەت',
    appointment: 'ۋاقىت بېكىتىش',
    doctorInfo: 'دوختۇر ئۇچۇرى',
    doctorName: 'دوختۇر ئىسمى',
    contactPhone: 'ئالاقە تېلېفونى',
    workAddress: 'خىزمەت ئادرېسى',
    callPhone: 'تېلېفون قىلىش',
    close: 'تاقاش',
    callFailed: 'تېلېفون قىلىش مەغلۇپ بولدى',

    // مەھسۇلات تۈرى
    medicineCategory: 'دورا',
    healthCategory: 'ساغلاملىق مەھسۇلاتلىرى',

    // قاڭقىش كۆزنىكى
    popupClose: '✕'
  },

  // مەھسۇلات تەپسىلاتى بېتى
  productDetails: {
    // ئاساسىي ئۇچۇر
    manufacturer: 'ئىشلەپچىقارغۇچى: ',
    doctor: 'دوختۇر',
    price: 'باھا: ',
    detailDescription: 'تەپسىلىي چۈشەندۈرۈش',

    // ئاستى مەشغۇلات
    buyNow: 'دەرھال سېتىۋېلىش',

    // سېتىۋېلىش جەزملەش كۆزنىكى
    buyConfirm: 'سېتىۋېلىش جەزملەش',
    productName: 'مەھسۇلات نامى: ',
    unitPrice: 'بىر دانە باھا: ',
    quantity: 'سانى: ',
    total: 'جەمئىي: ',
    confirmTip: 'سېتىۋېلىش ئۇچۇرىڭىزنى جەزملەڭ، جەزملەش كۇنۇپكىسىنى چېكىپ بۇيرۇتما جەزملەش بېتىگە ئۆتىدۇ.',
    cancel: 'بىكار قىلىش',
    confirmBuy: 'سېتىۋېلىشنى جەزملەش',

    // سېتىۋېلىش سەۋىتى كۆزنىكى
    selectQuantity: 'سان تاللاش',
    stock: 'ئامبار: ',
    confirm: 'جەزملەش',
    addToCartSuccess: 'سېتىۋېلىش سەۋىتىگە {count} دانە قوشۇلدى',

    // مەھسۇلات ئۇچۇرى
    products: {
      // دورا
      kidneyTonic: {
        name: 'بۆرەك قۇۋۋەتلەندۈرگۈچ كاپسۇلا',
        description: 'بۆرەك جەۋھىرىنى تولدۇرىدۇ، بەدەننى كۈچەيتىدۇ',
        detailDescription: 'ئادەم شەم، بۇغا مۈڭگۈزى، قاشقار قۇيرۇقى قاتارلىق قىممەتلىك خىتاي دورا ماتېرىياللىرى بىلەن ياسالغان. بۆرەك ياڭىنى تولدۇرۇش، جەۋھەر تولدۇرۇش ئۈنۈمىگە ئىگە. بۆرەك كەمچىلىكى سەۋەبىدىن كېلىپ چىققان بەل تىز ئاغرىقى، روھىي چارچاش، جىنسىي ئىقتىدار تۆۋەنلىشىش قاتارلىق ئالامەتلەرگە ماس كېلىدۇ. بۆرەك ئىقتىدارىنى ياخشىلاپ، بەدەن سۈپىتىنى ئاشۇرالايدۇ. كۈندە 2 قېتىم، ھەر قېتىم 3 دانە، ئىلىق سۇ بىلەن ئىچىدۇ.'
      },
      ibuprofen: {
        name: 'ئىبۇپروفېن ئاستا بوشىتىش كاپسۇلىسى',
        description: 'يېنىك تا ئوتتۇراھال ئاغرىقنى يېنىكلىتىشكە ئىشلىتىلىدۇ',
        detailDescription: 'ئىبۇپروفېن ئاستا بوشىتىش كاپسۇلىسى بىر خىل ستېروئىد ئەمەس ياللۇغ قارشى دورا، ئاساسەن يېنىك تا ئوتتۇراھال ئاغرىقنى يېنىكلىتىشكە ئىشلىتىلىدۇ، مەسىلەن باش ئاغرىقى، بوغۇم ئاغرىقى، يېرىم باش ئاغرىقى، چىش ئاغرىقى، مۇسكۇل ئاغرىقى، نېرۋا ئاغرىقى، ئايال كىشىلەرنىڭ ئاغرىقى. شۇنداقلا ئادەتتىكى شاملاش ياكى ئېپىدېمىيەلىك شاملاش سەۋەبىدىن كېلىپ چىققان قىزىتىشقا ئىشلىتىلىدۇ.'
      },
      licorice: {
        name: 'مۇرەككەب شىرىن يوتال تاختىسى',
        description: 'يۆتەل توسۇش ۋە بالغام چىقىرىشقا ئىشلىتىلىدۇ',
        detailDescription: 'مۇرەككەب شىرىن يوتال تاختىسى يۆتەل توسۇش بالغام چىقىرىش تۈرىدىكى رېتسېپتسىز دورا مەھسۇلاتى. يۆتەل توسۇش بالغام چىقىرىشقا ئىشلىتىلىدۇ. يۇقىرى نەپەس يولى يۇقۇملىنىشى، بىرونخىت ۋە شاملاش ۋاقتىدا كېلىپ چىققان يۆتەل ۋە بالغام چىقىرىش قىيىنلىشىشقا ماس كېلىدۇ.'
      },
      vitaminC: {
        name: 'ۋىتامىن C تاختىسى',
        description: 'قان كېسىلى كېسەللىكىنى ئالدىنى ئېلىشقا ئىشلىتىلىدۇ',
        detailDescription: 'ۋىتامىن C تاختىسى قان كېسىلى كېسەللىكىنى ئالدىنى ئېلىشقا ئىشلىتىلىدۇ، شۇنداقلا ھەر خىل ئۆتكۈر ۋە سۈرۈلمە يۇقۇملۇق كېسەللىكلەر ۋە قان چېچەك قاتارلىقلارنىڭ ياردەمچى داۋالىشىغا ئىشلىتىلىدۇ. ۋىتامىن C ئامىنو كىسلاتا مېتابولىزمى، نېرۋا يەتكۈزگۈچى سىنتېزى، كوللاگېن ۋە توقۇما ھۈجەيرە ئارىلىق ماددىسىنىڭ سىنتېزىغا قاتنىشىدۇ.'
      },
      // ساغلاملىق مەھسۇلاتلىرى
      calcium: {
        name: 'كالتسىي تاختىسى',
        description: 'كالتسىي تولدۇرۇش، سۆڭەكنى مۇستەھكەملەش',
        detailDescription: 'كالتسىي ئادەم بەدىنىنىڭ سۆڭەك ۋە چىشلىرىنىڭ ئاساسىي تەركىبىي قىسمى، نۇرغۇن فىزىئولوگىيەلىك ئىقتىدارلارمۇ كالتسىينىڭ قاتنىشىشىنى تەلەپ قىلىدۇ. بۇ مەھسۇلاتنىڭ ھەر بىر تاختىسىدا 600 مىللىگرام كالتسىي بار، كالتسىي كاربونات كالتسىي مەنبەسى سۈپىتىدە ئىشلىتىلگەن، سىڭىش نىسبىتى يۇقىرى، ھەر خىل ياش گۇرۇپپىسىدىكى كىشىلەرنىڭ كالتسىي تولدۇرۇش ئېھتىياجىغا ماس كېلىدۇ.'
      },
      protein: {
        name: 'ئاق بەلۇق كۇكۇمى',
        description: 'يۇقىرى سۈپەتلىك ئاق بەلۇق تولدۇرۇش',
        detailDescription: 'يۇقىرى سۈپەتلىك داڭ ئاق بەلۇقى ۋە سۈت ئاق بەلۇقى بىلەن ياسالغان، ئاق بەلۇق مىقدارى 80% غا يەتكەن، ئادەم بەدىنى ئۈچۈن زۆرۈر بولغان 9 خىل ئامىنو كىسلاتانى ئۆز ئىچىگە ئالغان، ھەزىم قىلىش ۋە سىڭىش ئاسان. ياشلار، قېرىلار، بالىلار قاتارلىق ئاق بەلۇق تولدۇرۇشقا ئېھتىياجلىق گۇرۇپپىلارغا ماس كېلىدۇ.'
      },
      fishOil: {
        name: 'بېلىق مېيى كاپسۇلىسى',
        description: 'Omega-3 يېغ كىسلاتىسىغا باي',
        detailDescription: 'چوڭقۇر دېڭىز بېلىق مېيى كاپسۇلىسى EPA ۋە DHA غا باي، يۈرەك قان تومۇر ساغلاملىقىنى ساقلاشقا، مېڭە تەرەققىياتىنى ئىلگىرى سۈرۈشكە، ئەسلەش كۈچىنى ياخشىلاشقا ياردەم بېرىدۇ. نورۋېگىيە چوڭقۇر دېڭىز بېلىقلىرىدىن ئېلىنغان، ساپلىقى يۇقىرى، ئېغىر مېتال بۇلغىنىشى يوق.'
      }
    }
  },

  // بۇيرۇتما جەزملەش بېتى
  confirmOrder: {
    // بەت ماۋزۇسى
    title: 'بۇيرۇتما جەزملەش',

    // مەھسۇلات ئۇچۇرى
    productInfo: 'مەھسۇلات ئۇچۇرى',
    doctorName: 'دوختۇر نامى: ',
    quantity: 'سانى: ',
    subtotal: 'كىچىك جەمئىي: ',

    // يوللاش ئۇچۇرى
    shippingInfo: 'يوللاش ئۇچۇرى',
    addressManage: 'ئادرېس باشقۇرۇش',
    receiverName: 'تاپشۇرۇۋالغۇچى نامى',
    receiverPhone: 'تاپشۇرۇۋالغۇچى تېلېفونى',
    region: 'رايون',
    address: 'يەتكۈزۈش ئادرېسى',
    receiverNamePlaceholder: 'تاپشۇرۇۋالغۇچى نامىنى كىرگۈزۈڭ',
    receiverPhonePlaceholder: 'تاپشۇرۇۋالغۇچى تېلېفونىنى كىرگۈزۈڭ',
    addressPlaceholder: 'يەتكۈزۈش ئادرېسىنى كىرگۈزۈڭ',
    defaultRegion: 'شىنجاڭ ئۇيغۇر ئاپتونوم رايونى ئۈرۈمچى شەھىرى تەڭرىتاغ رايونى',

    // بۇيرۇتما پۇلى
    orderAmount: 'بۇيرۇتما پۇلى',
    productAmount: 'مەھسۇلات پۇلى',
    shippingFee: 'يوللاش ھەققى',
    freeShipping: 'ھەقسىز يوللاش',
    totalAmount: 'ئومۇمىي پۇل',
    total: 'جەمئىي: ',

    // مەشغۇلات كۇنۇپكىلىرى
    submitOrder: 'بۇيرۇتما تاپشۇرۇش',

    // دەلىللەش ئۇچۇرلىرى
    nameRequired: 'تاپشۇرۇۋالغۇچى نامىنى كىرگۈزۈڭ',
    phoneRequired: 'تاپشۇرۇۋالغۇچى تېلېفونىنى كىرگۈزۈڭ',
    addressRequired: 'يەتكۈزۈش ئادرېسىنى كىرگۈزۈڭ',
    orderSubmitSuccess: 'بۇيرۇتما مۇۋەپپەقىيەتلىك تاپشۇرۇلدى'
  },

  // ئادرېس قوشۇش بېتى
  addAddress: {
    // بەت ماۋزۇسى
    title: 'ئادرېس قوشۇش',

    // فورما بەلگىلىرى
    receiverName: 'تاپشۇرۇۋالغۇچى نامى',
    contactPhone: 'ئالاقە تېلېفونى',
    region: 'رايون',
    detailAddress: 'تەپسىلىي ئادرېس',
    zipCode: 'پوچتا نومۇرى (تاللاشچان)',
    addressTag: 'ئادرېس بەلگىسى (تاللاشچان)',
    setDefault: 'سۈكۈتتىكى ئادرېس قىلىپ بەلگىلەش',

    // ئورۇن ئىگىلىگۈچىلەر
    namePlaceholder: 'تاپشۇرۇۋالغۇچى نامىنى كىرگۈزۈڭ',
    phonePlaceholder: 'ئالاقە تېلېفونىنى كىرگۈزۈڭ',
    regionPlaceholder: 'رايون تاللاڭ',
    addressPlaceholder: 'تەپسىلىي ئادرېسنى كىرگۈزۈڭ (كوچا، ئۆي نومۇرى قاتارلىق)',
    zipCodePlaceholder: 'پوچتا نومۇرىنى كىرگۈزۈڭ',
    tagPlaceholder: 'مەسىلەن: ئۆي، ئىشخانا، مەكتەپ قاتارلىق',

    // مەشغۇلات كۇنۇپكىلىرى
    saveAddress: 'ئادرېس ساقلاش',

    // دەلىللەش ئۇچۇرلىرى
    nameRequired: 'تاپشۇرۇۋالغۇچى نامىنى كىرگۈزۈڭ',
    phoneRequired: 'ئالاقە تېلېفونىنى كىرگۈزۈڭ',
    regionRequired: 'رايون تاللاڭ',
    addressRequired: 'تەپسىلىي ئادرېسنى كىرگۈزۈڭ',
    saveSuccess: 'مۇۋەپپەقىيەتلىك ساقلاندى'
  },

  // ئادرېس تەھرىرلەش بېتى
  editAddress: {
    // بەت ماۋزۇسى
    title: 'ئادرېس تەھرىرلەش',

    // فورما بەلگىلىرى (addAddress دىن كۆپىنچە قايتا ئىشلىتىش)
    receiverName: 'تاپشۇرۇۋالغۇچى نامى',
    contactPhone: 'ئالاقە تېلېفونى',
    region: 'رايون',
    detailAddress: 'تەپسىلىي ئادرېس',
    zipCode: 'پوچتا نومۇرى (تاللاشچان)',
    addressTag: 'ئادرېس بەلگىسى (تاللاشچان)',
    setDefault: 'سۈكۈتتىكى ئادرېس قىلىپ بەلگىلەش',

    // ئورۇن ئىگىلىگۈچىلەر
    namePlaceholder: 'تاپشۇرۇۋالغۇچى نامىنى كىرگۈزۈڭ',
    phonePlaceholder: 'ئالاقە تېلېفونىنى كىرگۈزۈڭ',
    regionPlaceholder: 'رايون تاللاڭ',
    addressPlaceholder: 'تەپسىلىي ئادرېسنى كىرگۈزۈڭ (كوچا، ئۆي نومۇرى قاتارلىق)',
    zipCodePlaceholder: 'پوچتا نومۇرىنى كىرگۈزۈڭ',
    tagPlaceholder: 'مەسىلەن: ئۆي، ئىشخانا، مەكتەپ قاتارلىق',

    // مەشغۇلات كۇنۇپكىلىرى
    saveChanges: 'ئۆزگەرتىشنى ساقلاش',

    // دەلىللەش ئۇچۇرلىرى
    nameRequired: 'تاپشۇرۇۋالغۇچى نامىنى كىرگۈزۈڭ',
    phoneRequired: 'ئالاقە تېلېفونىنى كىرگۈزۈڭ',
    regionRequired: 'رايون تاللاڭ',
    addressRequired: 'تەپسىلىي ئادرېسنى كىرگۈزۈڭ',
    saveSuccess: 'مۇۋەپپەقىيەتلىك ساقلاندى'
  },

  // ئادرېس تاللاش بېتى
  selectAddress: {
    // بەت ماۋزۇسى
    title: 'ئادرېس تاللاش',

    // قۇرۇق ھالەت
    noAddress: 'يەتكۈزۈش ئادرېسى يوق',
    addAddressHint: 'ئوڭ ئاستىدىكى كۇنۇپكىنى چېكىپ ئادرېس قوشۇڭ',

    // ئادرېس بەلگىلىرى
    defaultTag: 'سۈكۈتتىكى',

    // مەشغۇلات كۇنۇپكىلىرى
    edit: 'تەھرىرلەش',
    delete: 'ئۆچۈرۈش',

    // ئۆچۈرۈشنى جەزملەش
    deleteConfirm: 'بۇ ئادرېسنى ئۆچۈرۈشنى جەزملەمسىز؟',
    deleteSuccess: 'مۇۋەپپەقىيەتلىك ئۆچۈرۈلدى'
  },

  // دوختۇر تەپسىلاتى بېتى
  doctorDetails: {
    // بەت ماۋزۇسى
    title: 'دوختۇر تەپسىلاتى',

    // دوختۇر ئۇچۇرى
    doctorBadge: 'دوختۇر',
    workTime: 'دۈشەنبىدىن جۈمىگىچە 9:00-17:00',
    consultDoctor: 'دوختۇرغا مەسلىھەت سوراش',

    // تەپسىلات بۆلۈملىرى
    doctorDetail: 'دوختۇر تەپسىلاتى',
    contactPhone: 'ئالاقە تېلېفونى',
    workAddress: 'خىزمەت ئادرېسى',
    specialtyField: 'ماھىر بولغان سوھە',
    doctorRecommend: 'دوختۇر تەۋسىيەسى',

    // مەشغۇلات كۇنۇپكىلىرى
    call: 'تېلېفون قىلىش',
    copy: 'كۆچۈرۈش',
    viewDetail: 'تەپسىلاتىنى كۆرۈش',

    // ئۇقتۇرۇش ئۇچۇرلىرى
    liked: 'ياقتۇردۇم',
    unliked: 'ياقتۇرۇشنى بىكار قىلدىم',
    favorited: 'يىغىۋالدىم',
    unfavorited: 'يىغىۋېلىشنى بىكار قىلدىم',
    callFailed: 'تېلېفون قىلىش مەغلۇپ بولدى',
    addressCopied: 'ئادرېس كۆچۈرۈلدى',

    // ئالاھىدە كېسەللەر
    diabetes: 'شېكەر كېسىلى',
    thyroidDisease: 'قالقانسىمان بېزى كېسىلى',
    endocrineDisorder: 'ئىچكى ئاجرىتىش بۇزۇلۇشى',

    // تەۋسىيە قىلىنغان مەھسۇلاتلار
    heartProtectionCapsule: 'يۈرەك قوغداش كاپسۇلىسى',
    heartProtectionDesc: 'تەبىئىي ئۆسۈملۈك ئېكستراكتى، يۈرەك قان تومۇر ساغلاملىقىنى ئۈنۈملۈك قوغدايدۇ',
    antihypertensiveTablet: 'قان بېسىمىنى چۈشۈرۈش تاۋلېتكىسى',
    antihypertensiveDesc: 'خىتاي دورىسى مۇرەككەپ تەييارلىغى، مۇلايىم قان بېسىمىنى چۈشۈرىدۇ، يان تەسىرى يوق'
  },

  // مېنىڭ بېتىم
  my: {
    title: 'مېنىڭ',
    profile: 'شەخسىي مەلۇمات',
    healthRecords: 'ساغلاملىق ھۆججىتى',
    chatHistory: 'پاراڭ تارىخى',
    shoppingCart: 'مال ھارۋىسى',
    orders: "زاكاسلىرىم",
    addresses: 'تاپشۇرۇپ بېرىش ئادرېسى',
    settings: 'تەڭشەك',
    help: 'ياردەم ۋە پىكىر بىلدۈرۈش',
    about: 'بىز ھەققىدە',
    logout: 'چىقىش',
    login: 'كىرىش',
    register: 'تىزىملىتىش',
    // ئىقتىدار كارتىسى
    myLikes: 'ياقتۇرغانلىرىم',
    myFavorites: 'يىغقانلىرىم',
    // ساغلاملىق سانلىق مەلۇماتلىرى
    weight: 'ئېغىرلىق',
    height: 'ئېگىزلىك',
    bloodType: 'قان تىپى',
    // ئىقتىدار تىزىملىكى
    shareApp: 'ئەپنى ھەمبەھىرلەش',
    distributionManagement: 'تارقىتىش باشقۇرۇش',
    // كىرىش مۇناسىۋەتلىك
    loginInProgress: 'كىرىۋاتىدۇ...',
    loginFailed: 'كىرىش مەغلۇپ بولدى، قايتا سىناڭ',
    inputNickname: 'تەخەللۇس كىرگۈزۈڭ',
    pleaseInputNickname: 'تەخەللۇسىڭىزنى كىرگۈزۈڭ',
    nicknameRequired: 'تەخەللۇس كىرگۈزۈڭ',
    completingLogin: 'كىرىشنى تاماملاۋاتىدۇ...',
    loginSuccess: 'كىرىش مۇۋەپپەقىيەتلىك'
  },

  // پاراڭ تارىخى بېتى
  chatHistory: {
    title: 'پاراڭ تارىخى',
    selectYearMonth: 'يىل ئاي تاللاش',
    year: 'يىل',
    month: 'ئاي',
    slideToSelect: 'سىيرىپ تاللاڭ',
    noChatData: 'بۇ چېسلادا پاراڭ خاتىرىسى يوق',
    noHistory: 'پاراڭ تارىخى يوق',
    clearHistory: 'تارىخنى تازىلاش',
    refreshHistory: 'تارىخنى يېڭىلاش',
    confirmClear: 'تازىلاشنى جەزملەش',
    clearWarning: 'بارلىق پاراڭ تارىخىنى تازىلامسىز؟ بۇ مەشغۇلاتنى ئەسلىگە كەلتۈرگىلى بولمايدۇ.',
    historyCleared: 'تارىخ خاتىرىسى تازىلاندى',
    deleteHistory: 'تارىخنى ئۆچۈرۈش',
    exportHistory: 'تارىخنى چىقىرىش',
    searchHistory: 'تارىخ ئىزدەش',
    today: 'بۈگۈن',
    yesterday: 'تۈنۈگۈن',
    thisWeek: 'بۇ ھەپتە',
    thisMonth: 'بۇ ئاي',
    day: 'كۈن',
    selectYear: 'يىل تاللاش',
    selectMonth: 'ئاي تاللاش',
    selectDate: 'چېسلا تاللاش',
    // ھەپتە
    weekdays: {
      sunday: 'يەكشەنبە',
      monday: 'دۈشەنبە',
      tuesday: 'سەيشەنبە',
      wednesday: 'چارشەنبە',
      thursday: 'پەيشەنبە',
      friday: 'جۈمە',
      saturday: 'شەنبە'
    }
  },

  // مېنىڭ بۇيۇرتما بېتىم
  myOrder: {
    title: 'مېنىڭ بۇيۇرتمىلىرىم',
    tabs: {
      all: 'ھەممىسى',
      pendingPayment: 'تۆلەش كۈتۈۋاتقان',
      pendingShipment: 'يوللاش كۈتۈۋاتقان',
      shipped: 'يوللانغان',
      completed: 'تاماملانغان'
    },
    empty: {
      noOrders: 'بۇيۇرتما يوق',
      noPendingPayment: 'تۆلەش كۈتۈۋاتقان بۇيۇرتما يوق',
      noPendingShipment: 'يوللاش كۈتۈۋاتقان بۇيۇرتما يوق',
      noShipped: 'يوللانغان بۇيۇرتما يوق',
      noCompleted: 'تاماملانغان بۇيۇرتما يوق'
    }
  },

  // ساغلاملىق ھۆججىتى بېتى
  healthRecords: {
    title: 'ساغلاملىق ھۆججىتى',
    noHealthData: 'ساغلاملىق ھۆججىتى يوق',
    createHealthRecord: 'ساغلاملىق ھۆججىتى قۇرۇش',
    basicInfo: 'ئاساسىي مەلۇمات',
    allergyHistory: 'ئاللېرگىيە تارىخى',
    chronicDiseaseHistory: 'سۈرەكلىك كېسەللىك تارىخى',
    currentMedication: 'نۆۋەتتىكى دورا ئىشلىتىش',
    lifestyle: 'تۇرمۇش ئۇسۇلى',
    // ساغلاملىق ھالىتى كۆرسىتىش
    hasAllergyDesc: 'دورا، يېمەكلىك ياكى باشقا ماددىلارغا ئاللېرگىيەڭىز بارمۇ',
    hasChronicDiseaseDesc: 'سۈرەكلىك كېسەللىكىڭىز بارمۇ',
    hasMedicationDesc: 'دورا ئىشلىتەمسىز',
    exerciseFrequencyLabel: 'ماشىق چاستوتىسى',
    smokingStatusLabel: 'تاماكا چېكىش ئەھۋالى',
    drinkingStatusLabel: 'ھاراق ئىچىش ئەھۋالى',
    sleepDurationLabel: 'ھەر كېچە ئوتتۇراچە ئۇخلاش ۋاقتى',
    sleepQualityLabel: 'ئۇخلاش سۈپىتى',
    stressLevelLabel: 'يېقىنقى بېسىم دەرىجىسى'
  },

  // سېتىۋېلىش سەۋىتى بېتى
  shoppingCart: {
    title: 'سېتىۋېلىش سەۋىتى',
    empty: 'سېتىۋېلىش سەۋىتى قۇرۇق',
    emptySubtitle: 'ياخشى كۆرگەن تاۋارلارنى تاللاڭ',
    goShopping: 'سېتىۋېلىشقا بېرىش',
    selectAll: 'ھەممىنى تاللاش',
    totalItems: 'جەمئىي {count} دانە تاۋار',
    subtotal: 'كىچىك جەم',
    selectedItems: 'تاللانغان {count} دانە تاۋار',
    total: 'جەمئىي',
    checkout: 'ھېساب ({count})',
    deleteAll: 'ھەممىنى ئۆچۈرۈش',
    deleteConfirm: 'ئۆچۈرۈشنى جەزملەش',
    deleteItemConfirm: '"{name}" تاۋارنى ئۆچۈرەمسىز؟',
    deleteAllConfirm: 'تاللانغان {count} دانە تاۋارنى ئۆچۈرەمسىز؟',
    deleteSuccess: 'ئۆچۈرۈش مۇۋەپپەقىيەتلىك',
    deleteAllSuccess: '{count} دانە تاۋار مۇۋەپپەقىيەتلىك ئۆچۈرۈلدى',
    selectItemsToDelete: 'ئۆچۈرىدىغان تاۋارنى تاللاڭ',
    selectItemsToCheckout: 'ھېساب قىلىدىغان تاۋارنى تاللاڭ',
    checkoutItems: '{count} دانە تاۋار ھېساب قىلىش',
    delete: 'ئۆچۈرۈش'
  },

  // ئىشلەتكۈچى مۇناسىۋەتلىك
  user: {
    profile: 'شەخسىي مەلۇمات',
    nickname: 'تەخەللۇس',
    avatar: 'باش سۈرەت',
    phone: 'تېلېفون نومۇرى',
    email: 'ئېلېكترونلۇق خەت',
    gender: 'جىنسى',
    birthday: 'تۇغۇلغان كۈنى',
    address: 'ئادرېس',
    male: 'ئەر',
    female: 'ئايال',
    unknown: 'نامەلۇم',
    clickToLogin: 'كىرىش ئۈچۈن چېكىڭ',
    loginForMore: 'تېخىمۇ كۆپ ئىقتىدارلارنى ئىشلىتىش ئۈچۈن كىرىڭ',
    // مەلۇمات تەھرىرلەش بېتى
    changeAvatar: 'باش سۈرەت ئالماشتۇرۇش',
    clickToChangePhoto: 'باش سۈرەتنى چېكىپ سۈرەت ئالماشتۇرۇڭ',
    basicInfo: 'ئاساسىي مەلۇمات',
    height: 'ئېگىزلىك',
    weight: 'ئېغىرلىق',
    bloodType: 'قان تىپى',
    location: 'تۇرۇقلۇق ئادرېس',
    locating: 'ئورۇن بېكىتىۋاتىدۇ',
    locate: 'ئورۇن بېكىتىش',
    healthInfo: 'ساغلاملىق ئۇچۇرى',
    allergyHistory: 'ئاللېرگىيە تارىخى',
    allergyDesc: 'دورا، يېمەكلىك ياكى باشقا ماددىلارغا ئاللېرگىيەڭىز بارمۇ',
    yes: 'ھەئە',
    no: 'ياق',
    commonAllergens: 'كۆپ ئۇچرايدىغان ئاللېرگىيە مەنبەسى',
    // ئاللېرگىيە مەنبەسى
    penicillin: 'پېنىسىللىن تىپىدىكى دورىلار',
    cephalosporin: 'سېفالوسپورىن تىپىدىكى دورىلار',
    aspirin: 'ئاسپىرىن',
    peanuts: 'يەر يېڭاق',
    seafood: 'دېڭىز مەھسۇلاتلىرى',
    milk: 'سۈت',
    eggs: 'تۇخۇم',
    pollenMites: 'گۈل چاڭى/چاڭ كېنىسى',
    // كىرگۈزۈش ئەسكەرتىشى
    enterHeight: 'ئېگىزلىكنى كىرگۈزۈڭ (cm)',
    enterWeight: 'ئېغىرلىقنى كىرگۈزۈڭ (kg)',
    // كۆڭۈلدىكى ئادرېس
    defaultLocation: 'شىنجاڭ ئۇيغۇر ئاپتونوم رايونى ئۈرۈمچى شەھىرى تەڭرىتاغ رايونى',
    // جىنس تاللانمىسى
    notSet: 'بېكىتىلمىگەن',
    // قان تىپى تاللانمىسى
    unknownBloodType: 'نامەلۇم',
    typeA: 'A تىپ',
    typeB: 'B تىپ',
    typeAB: 'AB تىپ',
    typeO: 'O تىپ',
    // مەلۇمات تەھرىرلەش بېتى كېڭەيتىش
    otherAllergies: 'باشقا ئاللېرگىيە ماددىلىرى',
    otherAllergiesPlaceholder: 'باشقا ئاللېرگىيە مەنبەسىنى تولۇقلاڭ',
    currentMedication: 'نۆۋەتتىكى دورا ئىشلىتىش',
    currentMedicationDesc: 'ھازىر ھەرقانداق دورا ئىچىۋاتامسىز',
    medicationList: 'دورا تىزىملىكى',
    medicationListPlaceholder: 'ئىچىۋاتقان دورىلارنىڭ نامى، مىقدارى ۋە چاستوتىسىنى تىزىڭ',
    chronicDiseaseHistory: 'سۈرەكلىك كېسەللىك تارىخى',
    chronicDiseaseDesc: 'سۈرەكلىك كېسەللىكىڭىز بارمۇ',
    specificSymptoms: 'ئېنىق كېسەللىك',
    hypertension: 'يۇقىرى قان بېسىمى',
    diabetes: 'شېكەر كېسىلى',
    hypertensionRange: 'ئادەتتىكى قان بېسىمى دائىرىسى',
    hypertensionPlaceholder: 'مەسىلەن 130/85 mmHg',
    diabetesRange: 'ئادەتتىكى ئاچ قورساق قان شېكىرى دائىرىسى',
    diabetesPlaceholder: 'مەسىلەن 5.8 mmol/L',
    otherChronicDiseases: 'باشقا سۈرەكلىك كېسەللىكلەر',
    otherChronicDiseasesPlaceholder: 'باشقا سۈرەكلىك كېسەللىكلەرنى تولۇقلاڭ',
    // دوختۇرلۇق تارىخ
    medicalHistory: 'دوختۇرلۇق تارىخ',
    surgeryHistory: 'ئوپېراتسىيە ۋە دوختۇرخانىدا ياتقان تارىخ',
    surgeryHistoryDesc: 'ئىلگىرى ئوپېراتسىيە ياكى دوختۇرخانىدا ياتقان تەجرىبىڭىز بارمۇ',
    surgeryDetails: 'تەپسىلاتى',
    surgeryDetailsPlaceholder: 'ئوپېراتسىيە ياكى دوختۇرخانىدا ياتقان تەپسىلاتىنى تەسۋىرلەڭ',
    familyHistory: 'ئائىلە كېسەللىك تارىخى',
    familyHistoryDesc: 'يېقىن تۇغقانلار (ئاتا-ئانا، قېرىنداش-سىڭىل، بالىلار) تۆۋەندىكى كېسەللىكلەرگە گىرىپتارمۇ',
    heartDisease: 'يۈرەك كېسىلى',
    stroke: 'مىڭە ئۇرۇلۇش',
    cancer: 'سەرەتان',
    mentalHealth: 'روھىي ساغلاملىق كېسەللىكى',
    otherFamilyHistory: 'باشقا ئائىلە كېسەللىك تارىخى تولۇقلاش',
    otherFamilyHistoryPlaceholder: 'باشقا ئائىلە كېسەللىك تارىخىنى تولۇقلاڭ',
    // تۇرمۇش ئۇسۇلى
    lifestyle: 'تۇرمۇش ئۇسۇلى',
    exerciseFrequency: 'ماشىق چاستوتىسى',
    dietPreferences: 'كۈندىلىك يېمەكلىك ئامراق',
    spicy: 'ئاچچىق يېمەكنى ياخشى كۆرىدۇ',
    sweet: 'تاتلىق يېمەكنى ياخشى كۆرىدۇ',
    salty: 'شورلۇق يېمەكنى ياخشى كۆرىدۇ',
    light: 'يېنىك يېمەكلىكنى ياخشى كۆرىدۇ',
    oily: 'ماي يېمەكلىكنى ياخشى كۆرىدۇ',
    vegetarian: 'كۆكتاتنى ياخشى كۆرىدۇ',
    smokingStatus: 'تاماكا چېكىش ئەھۋالى',
    drinkingStatus: 'ھاراق ئىچىش ئەھۋالى',
    sleepDuration: 'ھەر كېچە ئوتتۇراچە ئۇخلاش ۋاقتى',
    sleepQuality: 'ئۇخلاش سۈپىتى',
    stressLevel: 'يېقىنقى بېسىم دەرىجىسى',
    // ئايال ساغلاملىقى
    womenHealth: 'ئايال ساغلاملىقى',
    menopause: 'ئاي كۆرۈش توختىدىمۇ',
    menstrualCycle: 'ئاي كۆرۈش دەۋرىسى قانۇنلۇقمۇ',
    pregnancy: 'ھامىلىدار بولغان تەجرىبىڭىز بارمۇ',
    birthCount: 'تۇغۇش قېتىم سانى',
    // چېسلا تاللاش
    selectDate: 'چېسلا تاللاش',
    save: 'ساقلاش',
    // تۇرمۇش ئۇسۇلى تاللانمىلىرى
    exerciseOptions: {
      sedentary: 'ئۇزۇن ئولتۇرۇش(ئاساسەن ماشىق قىلماسلىق)',
      light: 'يېنىك ماشىق',
      moderate: 'ئوتتۇراھال ماشىق',
      intense: 'يۇقىرى كۈچلۈك ماشىق'
    },
    smokingOptions: {
      never: 'ھەرگىز تاماكا چەكمەيمەن',
      occasionally: 'ئارا-ئارىدا تاماكا چېكىمەن',
      frequently: 'دائىم تاماكا چېكىمەن',
      quit: 'تاماكا چېكىشنى توختاتتىم'
    },
    drinkingOptions: {
      never: 'ھەرگىز ھاراق ئىچمەيمەن',
      occasionally: 'ئارا-ئارىدا ھاراق ئىچىمەن',
      frequently: 'دائىم ھاراق ئىچىمەن',
      quit: 'ھاراق ئىچىشنى توختاتتىم'
    },
    sleepOptions: {
      normal: '7-8 سائەت',
      short: '6-7 سائەت',
      long: '8-9 سائەت',
      veryShort: '6 سائەتتىن ئاز',
      veryLong: '9 سائەتتىن كۆپ'
    },
    sleepQualityOptions: {
      veryGood: 'بەك ياخشى(ئۇخلاشقا قىيىنچىلىق يوق)',
      good: 'ئادەتتىكى',
      poor: 'ناچار',
      veryPoor: 'بەك ناچار'
    },
    stressOptions: {
      veryLow: 'بەك كىچىك',
      low: 'ئادەتتىكى',
      high: 'چوڭراق',
      veryHigh: 'بەك چوڭ'
    },
    // ئايال ساغلاملىقى تاللانمىلىرى
    menstrualCycleOptions: {
      regular: 'قانۇنلۇق',
      irregular: 'قانۇنسىز',
      sometimes: 'ئارا-ئارىدا قانۇنسىز'
    },
    birthCountOptions: {
      zero: '0 قېتىم',
      one: '1 قېتىم',
      two: '2 قېتىم',
      three: '3 قېتىم',
      fourPlus: '4 قېتىم ۋە ئۇنىڭدىن كۆپ'
    },
    // ئۇقتۇرۇش ئۇچۇرلىرى
    messages: {
      regionSelectSuccess: 'رايون تاللاش مۇۋەپپەقىيەتلىك',
      locationPermissionTitle: 'ئورۇن بېكىتىش ھوقۇقى ئىلتىماسى',
      locationPermissionContent: 'ئېنىق ئادرېس ئورۇن بېكىتىش مۇلازىمىتىنى تەمىنلەش ئۈچۈن سىزنىڭ ئورۇن ئۇچۇرىڭىزنى ئېلىش كېرەك، تەڭشەكتە ئورۇن بېكىتىش ھوقۇقىنى ئېچىڭ',
      locationPermissionRequest: 'ئورۇن ھوقۇقى ئىلتىماسى',
      locationPermissionDesc: 'بىز ئادرېسنى ئاپتوماتىك تولدۇرۇش ئۈچۈن سىزنىڭ ئورۇن ئۇچۇرىڭىزنى ئېلىش كېرەك، بۇ سىزنىڭ شەخسىي مەلۇمات تەڭشىكىنى تېز تاماملىشىڭىزغا ياردەم بېرىدۇ. سىزنىڭ ئورۇن ئۇچۇرىڭىز پەقەت ئادرېس ئايلاندۇرۇش ئۈچۈن ئىشلىتىلىدۇ، ساقلانمايدۇ ياكى يۈكلەنمەيدۇ.',
      locationPermissionDenied: 'ئورۇن بېكىتىش ھوقۇقى رەت قىلىندى',
      manualAddressInput: 'سىز قولدا ئادرېس كىرگۈزەلەيسىز',
      locating: 'ئورۇن بېكىتىۋاتىدۇ...',
      locationFailed: 'ئورۇن بېكىتىش مەغلۇپ بولدى',
      locationSuccess: 'ئورۇن بېكىتىش مۇۋەپپەقىيەتلىك',
      saving: 'ساقلاۋاتىدۇ...',
      saveSuccess: 'ساقلاش مۇۋەپپەقىيەتلىك',
      saveFailed: 'ساقلاش مەغلۇپ بولدى',
      goToSettings: 'تەڭشەككە بېرىش',
      agree: 'قوشۇلىمەن',
      refuse: 'رەت قىلىمەن'
    }
  },

  // تەڭشەك بېتى
  settings: {
    title: 'تەڭشەكلەر',
    language: 'تىل تەڭشىكى',
    fontSize: 'خەت چوڭلۇقى',
    notifications: 'ئۇقتۇرۇش تەڭشىكى',
    privacy: 'مەخپىيەتلىك تەڭشىكى',
    security: 'بىخەتەرلىك تەڭشىكى',
    theme: 'تېما تەڭشىكى',
    cache: 'غەملەك باشقۇرۇش',
    version: 'نەشر ئۇچۇرى',
    checkUpdate: 'يېڭىلانمىنى تەكشۈرۈش',
    clearCache: 'غەملەكنى تازىلاش',
    // كۆرسىتىش تەڭشىكى
    display: 'كۆرسىتىش تەڭشىكى',
    darkMode: 'قاراڭغۇ ھالەت',
    darkModeDesc: 'ئوچۇق ۋە قاراڭغۇ تېما ئارىسىدا ئالماشتۇرۇش',
    followSystem: 'سىستېما تېمىسىغا ئەگىشىش',
    followSystemDesc: 'سىستېمىنىڭ ئوچۇق/قاراڭغۇ تەڭشىكىگە ئاپتوماتىك ماسلىشىش',
    // ئىشلەتكۈچى ئۇچۇرى مۇناسىۋەتلىك
    notSetNickname: 'ئۇچۇر تەھرىرلەش',
    notBoundPhone: 'تېلېفون نومۇرى باغلانمىغان',
    editProfile: 'ئۇچۇر تەھرىرلەش',
    normalUser: 'ئادەتتىكى ئىشلەتكۈچى',
    expiryTime: 'ۋاقتى ئۆتىدىغان ۋاقىت',
    notSet: '--',
    // تەڭشەك تۈرى چۈشەندۈرۈشى
    fontSizeDesc: 'ئەپ ئىچىدىكى خەت چوڭلۇقىنى تەڭشەش',
    languageDesc: 'ئەپ كۆرسىتىش تىلىنى تاللاش',
    darkModeDesc: 'ئوچۇق ۋە قاراڭغۇ تېما ئارىسىدا ئالماشتۇرۇش',
    followSystemDesc: 'سىستېمىنىڭ ئوچۇق/قاراڭغۇ تەڭشىكىگە ئاپتوماتىك ماسلىشىش',
    helpFeedback: 'ياردەم ۋە پىكىر',
    helpFeedbackDesc: 'دائىملىق سوئاللار ۋە پىكىر قايتۇرۇش',
    aboutUsDesc: 'نەشر ئۇچۇرى ۋە شىركەت تونۇشتۇرۇشى',
    selectLanguage: 'تىل تاللاڭ',
    selectSourceLanguage: 'مەنبە تىلىنى تاللاڭ',
    selectTargetLanguage: 'نىشان تىلىنى تاللاڭ',
    myLanguage: 'مېنىڭ تىلىم',
    theirLanguage: 'ئۇلارنىڭ تىلى',
    sourceLanguage: 'مەنبە',
    targetLanguage: 'نىشان',
    chinese: 'خەنزۇچە',
    english: 'ئىنگلىزچە',
    uyghur: 'ئۇيغۇرچە',
    // باشقا تەڭشەكلەر
    other: 'باشقا',
    logout: 'چىقىش',
    logoutDesc: 'كىرىش ھالىتىنى تازىلاپ كىرىش بېتىگە قايتىش',
    shareApp: 'ئەپنى ھەمبەھىرلەش',
    distributionManagement: 'تارقىتىش باشقۇرۇش',
    aboutDialogTitle: 'بىز ھەققىدە',
    aboutDialogContent: 'ساغلاملىق ياردەمچىسى v1.0.0\n\nكەسپىي AI ساغلاملىق يېتەكچىسى ئەپى',
    // قاڭقىش رامكا مۇناسىۋەتلىك
    adjustFontSize: 'خەت چوڭلۇقىنى تەڭشەش',
    fontPreview: 'خەت ئالدىن كۆرۈش Font Preview',
    previewText: 'بۇ ئالدىن كۆرۈش تېكىستىنىڭ ئۈنۈمى',
    currentSize: 'نۆۋەتتىكى چوڭلۇقى',
    aboutUs: 'بىز ھەققىدە',
    appName: 'ساغلاملىق ياردەمچىسى v1.0.0',
    appDesc: 'بىر كەسپىي AI ساغلاملىق يېتەكچى ئەپى',
    fontSizes: {
      small: 'كىچىك',
      medium: 'ئوتتۇراھال',
      large: 'چوڭ',
      extraLarge: 'ئىنتايىن چوڭ'
    },
    languages: {
      'zh-CN': '简体中文',
      'en': 'English',
      'ug': 'ئۇيغۇرچە'
    }
  },

  // ياردەم ۋە ئىنكاس بېتى
  helpFeedback: {
    title: 'ياردەم ۋە ئىنكاس',
    feedbackNotice: 'ئىنكاس چۈشەندۈرۈشى',
    noticeContent: 'بىز سىزنىڭ مەسىلە چۈشەندۈرۈشىڭىز ۋە مۇناسىۋەتلىك ئەپ سانلىق مەلۇماتلىرىنى (سەزگۈر ئۇچۇرلارنى ئۆز ئىچىگە ئالمايدۇ) توپلايمىز، سىزنىڭ مەسىلىڭىزنى تېخىمۇ ياخشى ھەل قىلىش ئۈچۈن. تاپشۇرغاندىن كېيىن بىز سىز تەمىنلىگەن تېلېفون نومۇرى ئارقىلىق سىز بىلەن ئالاقىلىشىمىز.',
    yourName: 'سىزنىڭ ئىسمىڭىز (ئىختىيارى)',
    namePlaceholder: 'ئىسمىڭىزنى كىرگۈزۈڭ',
    yourPhone: 'سىزنىڭ تېلېفون نومۇرىڭىز (قايتا زىيارەت ئۈچۈن)',
    phonePlaceholder: '11 خانىلىق تېلېفون نومۇرىنى كىرگۈزۈڭ',
    problemDescription: 'مەسىلە ياكى تەكلىپىڭىزنى تەپسىلىي چۈشەندۈرۈڭ',
    descriptionPlaceholder: 'ئۇچراشقان مەسىلە ياكى تەكلىپىڭىزنى تەپسىلىي چۈشەندۈرۈڭ',
    includeLabel: 'ئۆز ئىچىگە ئالىدۇ:',
    includeItems: {
      steps: '• ئېنىق مەشغۇلات باسقۇچلىرى',
      expected: '• كۈتۈلگەن نەتىجىلەر',
      actual: '• ئەمەلىيەتتە يۈز بەرگەن ئەھۋال',
      other: '• باشقا مۇناسىۋەتلىك ئۇچۇرلار'
    },
    submitFeedback: 'ئىنكاس تاپشۇرۇش',
    bottomNotice: 'ئەسكەرتىش: سىزنىڭ مەخپىيەتلىكىڭىز بىزگە ئىنتايىن مۇھىم، بىز سىزنىڭ سەزگۈر ئۇچۇرلىرىڭىزنى توپلىمايمىز، پەقەت مەسىلىنى ھەل قىلىشقا ياردەم بېرىش ئۈچۈن زۆرۈر ئەپ سانلىق مەلۇماتلىرىنى توپلايمىز.',
    // دەلىللەش ۋە ئەسكەرتىش ئۇچۇرلىرى
    validation: {
      phoneError: 'توغرا تېلېفون نومۇرىنى كىرگۈزۈڭ',
      contentRequired: 'مەسىلە ياكى تەكلىپىڭىزنى چۈشەندۈرۈڭ'
    },
    messages: {
      submitting: 'تاپشۇرۇۋاتىدۇ...',
      submitSuccess: 'مۇۋەپپەقىيەتلىك تاپشۇرۇلدى',
      submitFailed: 'تاپشۇرۇش مەغلۇپ بولدى'
    }
  },





  // سېتىۋېلىش سەۋىتى
  shoppingCart: {
    title: 'سېتىۋېلىش سەۋىتى',
    empty: 'سېتىۋېلىش سەۋىتى قۇرۇق',
    selectAll: 'ھەممىنى تاللاش',
    total: 'جەمئىي',
    checkout: 'ھېساب-كىتاب',
    quantity: 'سانى',
    remove: 'چىقىرىۋېتىش',
    addMore: 'سېتىۋېلىشنى داۋاملاشتۇرۇش',
    selectedItems: 'تاللانغان مەھسۇلاتلار',
    deleteSelected: 'تاللانغاننى ئۆچۈرۈش',
    confirmDelete: 'ئۆچۈرۈشنى جەزملەشتۈرۈش',
    deleteAllConfirm: 'تاللانغان بارلىق مەھسۇلاتلارنى ئۆچۈرەمسىز؟'
  },

  // ئادرېس باشقۇرۇش
  address: {
    title: 'تاپشۇرۇپ بېرىش ئادرېسى',
    addAddress: 'ئادرېس قوشۇش',
    editAddress: 'ئادرېس تەھرىرلەش',
    selectAddress: 'ئادرېس تاللاش',
    defaultAddress: 'كۆڭۈلدىكى ئادرېس',
    setDefault: 'كۆڭۈلدىكى قىلىپ بېكىتىش',
    name: 'تاپشۇرۇپ ئالغۇچى',
    phone: 'تېلېفون نومۇرى',
    region: 'تۇرغان رايون',
    detailAddress: 'تەپسىلىي ئادرېس',
    postalCode: 'پوچتا نومۇرى',
    saveAddress: 'ئادرېس ساقلاش',
    deleteAddress: 'ئادرېس ئۆچۈرۈش',
    confirmDeleteAddress: 'بۇ ئادرېسنى ئۆچۈرەمسىز؟'
  },

  // كامېرا ئىقتىدارى
  camera: {
    title: 'سۈرەت تارتىش',
    takePhoto: 'سۈرەت تارتىش',
    selectFromAlbum: 'سۈرەت ئالبومىدىن تاللاش',
    retake: 'قايتا تارتىش',
    confirm: 'جەزملەشتۈرۈش',
    flashOn: 'چاقماق چىرىغىنى ئېچىش',
    flashOff: 'چاقماق چىرىغىنى ئېتىش',
    switchCamera: 'كامېرا ئالماشتۇرۇش',
    help: 'ياردەم',
    cameraHelp: 'كامېرا ياردىمى',
    helpContent: {
      takePhoto: 'سۈرەت تارتىش كۇنۇپكىسىنى چېكىپ سۈرەت تارتىڭ',
      selectAlbum: 'ئالبوم كۇنۇپكىسىنى چېكىپ مەۋجۇت سۈرەتلەرنى تاللاڭ',
      switchCamera: 'ئالماشتۇرۇش كۇنۇپكىسىنى چېكىپ ئالدى/كەينى كامېرا ئالماشتۇرۇڭ',
      flash: 'چاقماق چىرىغى كۇنۇپكىسىنى چېكىپ چاقماق چىرىغىنى كونترول قىلىڭ'
    }
  },

  // خاتالىق ئۇچۇرى
  error: {
    networkError: 'تور ئۇلىنىشى مەغلۇپ بولدى',
    serverError: 'مۇلازىمېتىر خاتالىقى',
    unknownError: 'نامەلۇم خاتالىق',
    permissionDenied: 'ھوقۇق رەت قىلىندى',
    cameraPermissionDenied: 'كامېرا ھوقۇقى رەت قىلىندى',
    locationPermissionDenied: 'ئورۇن بېكىتىش ھوقۇقى رەت قىلىندى',
    storagePermissionDenied: 'ساقلاش ھوقۇقى رەت قىلىندى',
    operationFailed: 'مەشغۇلات مەغلۇپ بولدى',
    dataLoadFailed: 'سانلىق مەلۇمات يۈكلەش مەغلۇپ بولدى',
    uploadFailed: 'يۈكلەش مەغلۇپ بولدى',
    downloadFailed: 'چۈشۈرۈش مەغلۇپ بولدى'
  },

  // مۇۋەپپەقىيەت ئۇچۇرى
  success: {
    operationSuccess: 'مەشغۇلات مۇۋەپپەقىيەتلىك',
    saveSuccess: 'ساقلاش مۇۋەپپەقىيەتلىك',
    deleteSuccess: 'ئۆچۈرۈش مۇۋەپپەقىيەتلىك',
    uploadSuccess: 'يۈكلەش مۇۋەپپەقىيەتلىك',
    downloadSuccess: 'چۈشۈرۈش مۇۋەپپەقىيەتلىك',
    updateSuccess: 'يېڭىلاش مۇۋەپپەقىيەتلىك',
    loginSuccess: 'كىرىش مۇۋەپپەقىيەتلىك',
    logoutSuccess: 'چىقىش مۇۋەپپەقىيەتلىك',
    registerSuccess: 'تىزىملىتىش مۇۋەپپەقىيەتلىك'
  },

  // جەدۋەل دەلىللەش
  validation: {
    required: 'بۇ تۈر تولدۇرۇش زۆرۈر',
    invalidEmail: 'ئېلېكترونلۇق خەت فورماتى توغرا ئەمەس',
    invalidPhone: 'تېلېفون نومۇر فورماتى توغرا ئەمەس',
    passwordTooShort: 'پارول ئۇزۇنلۇقى 6 خانىدىن كەم بولمىسۇن',
    passwordMismatch: 'ئىككى قېتىم كىرگۈزگەن پارول ماس كەلمىدى',
    invalidFormat: 'فورمات توغرا ئەمەس',
    tooLong: 'مەزمۇن بەك ئۇزۇن',
    tooShort: 'مەزمۇن بەك قىسقا'
  },

  // كامېرا ئىقتىدارى
  camera: {
    title: 'رەسىم تارتىپ ئەۋەتىش',
    takePhoto: 'رەسىم تارتىش',
    retake: 'قايتا رەسىم تارتىش',
    send: 'ئەۋەتىش',
    selectArea: 'رايون تاللاش'
  },

  // كىرىش مۇناسىۋەتلىك
  login: {
    changeAvatar: 'باش سۈرەت ئۆزگەرتىش',
    selectAvatar: 'باش سۈرەت تاللاش',
    nicknamePlaceholder: 'تەخەللۇسىڭىزنى كىرگۈزۈڭ',
    avatarTip: 'ۋېچات باش سۈرىتىنى تاللاش ئۈچۈن يۇقىرىقى كۇنۇپكىنى چېكىڭ',
    experienceTip: 'تېخىمۇ ياخشى تەجرىبە ئۈچۈن، باش سۈرەت سىنبەلگىسىنى چېكىپ باش سۈرەت ئېلىڭ',
    devTip: '(ئىجادىيەت مۇھىتى: ئەگەر نورمالسىزلىق كۆرۈلسە، قايتا تاللاڭ ياكى قورالنى قايتا قوزغىتىڭ)',
    loginNow: 'دەرھال كىرىش'
  }
}
