/* 全局字体大小样式 */

/* 基础字体大小类 */
.font-xs { font-size: var(--font-size-xs, 24rpx) !important; }
.font-sm { font-size: var(--font-size-sm, 28rpx) !important; }
.font-base { font-size: var(--font-size-base, 30rpx) !important; }
.font-md { font-size: var(--font-size-md, 32rpx) !important; }
.font-lg { font-size: var(--font-size-lg, 36rpx) !important; }
.font-xl { font-size: var(--font-size-xl, 40rpx) !important; }
.font-2xl { font-size: var(--font-size-2xl, 44rpx) !important; }
.font-3xl { font-size: var(--font-size-3xl, 48rpx) !important; }
.font-4xl { font-size: var(--font-size-4xl, 56rpx) !important; }

/* 常用组件字体大小 */
.title { font-size: var(--font-size-2xl, 44rpx) !important; }
.subtitle { font-size: var(--font-size-lg, 36rpx) !important; }
.body-text { font-size: var(--font-size-md, 32rpx) !important; }
.caption { font-size: var(--font-size-sm, 28rpx) !important; }
.small-text { font-size: var(--font-size-xs, 24rpx) !important; }

/* 按钮字体大小 */
.btn-text { font-size: var(--font-size-md, 32rpx) !important; }
.btn-text-lg { font-size: var(--font-size-lg, 36rpx) !important; }
.btn-text-sm { font-size: var(--font-size-sm, 28rpx) !important; }

/* 表单元素字体大小 */
.input-text { font-size: var(--font-size-md, 32rpx) !important; }
.label-text { font-size: var(--font-size-base, 30rpx) !important; }
.placeholder-text { font-size: var(--font-size-sm, 28rpx) !important; }

/* 导航栏字体大小 */
.nav-title { font-size: var(--font-size-2xl, 44rpx) !important; }
.nav-subtitle { font-size: var(--font-size-base, 30rpx) !important; }

/* 列表项字体大小 */
.list-title { font-size: var(--font-size-lg, 36rpx) !important; }
.list-subtitle { font-size: var(--font-size-base, 30rpx) !important; }
.list-caption { font-size: var(--font-size-sm, 28rpx) !important; }

/* 卡片字体大小 */
.card-title { font-size: var(--font-size-xl, 40rpx) !important; }
.card-subtitle { font-size: var(--font-size-lg, 36rpx) !important; }
.card-content { font-size: var(--font-size-md, 32rpx) !important; }

/* 弹窗字体大小 */
.popup-title { font-size: var(--font-size-xl, 40rpx) !important; }
.popup-content { font-size: var(--font-size-md, 32rpx) !important; }
.popup-button { font-size: var(--font-size-md, 32rpx) !important; }

/* 字体大小主题类 */
.font-size-small {
  --font-size-xs: 20rpx;      /* 10px */
  --font-size-sm: 24rpx;      /* 12px */
  --font-size-base: 26rpx;    /* 13px */
  --font-size-md: 28rpx;      /* 14px */
  --font-size-lg: 30rpx;      /* 15px */
  --font-size-xl: 32rpx;      /* 16px */
  --font-size-2xl: 36rpx;     /* 18px */
  --font-size-3xl: 40rpx;     /* 20px */
  --font-size-4xl: 48rpx;     /* 24px */
}

.font-size-medium {
  --font-size-xs: 24rpx;      /* 12px */
  --font-size-sm: 28rpx;      /* 14px */
  --font-size-base: 30rpx;    /* 15px */
  --font-size-md: 32rpx;      /* 16px */
  --font-size-lg: 36rpx;      /* 18px */
  --font-size-xl: 40rpx;      /* 20px */
  --font-size-2xl: 44rpx;     /* 22px */
  --font-size-3xl: 48rpx;     /* 24px */
  --font-size-4xl: 56rpx;     /* 28px */
}

.font-size-large {
  --font-size-xs: 28rpx;      /* 14px */
  --font-size-sm: 32rpx;      /* 16px */
  --font-size-base: 36rpx;    /* 18px */
  --font-size-md: 40rpx;      /* 20px */
  --font-size-lg: 44rpx;      /* 22px */
  --font-size-xl: 48rpx;      /* 24px */
  --font-size-2xl: 52rpx;     /* 26px */
  --font-size-3xl: 56rpx;     /* 28px */
  --font-size-4xl: 64rpx;     /* 32px */
}

/* 响应式字体大小 */
@media screen and (max-width: 750rpx) {
  .font-size-small {
    --font-size-xs: 18rpx;
    --font-size-sm: 22rpx;
    --font-size-base: 24rpx;
    --font-size-md: 26rpx;
    --font-size-lg: 28rpx;
    --font-size-xl: 30rpx;
    --font-size-2xl: 34rpx;
    --font-size-3xl: 38rpx;
    --font-size-4xl: 46rpx;
  }
  
  .font-size-medium {
    --font-size-xs: 22rpx;
    --font-size-sm: 26rpx;
    --font-size-base: 28rpx;
    --font-size-md: 30rpx;
    --font-size-lg: 34rpx;
    --font-size-xl: 38rpx;
    --font-size-2xl: 42rpx;
    --font-size-3xl: 46rpx;
    --font-size-4xl: 54rpx;
  }
  
  .font-size-large {
    --font-size-xs: 26rpx;
    --font-size-sm: 30rpx;
    --font-size-base: 34rpx;
    --font-size-md: 38rpx;
    --font-size-lg: 42rpx;
    --font-size-xl: 46rpx;
    --font-size-2xl: 50rpx;
    --font-size-3xl: 54rpx;
    --font-size-4xl: 62rpx;
  }
}
