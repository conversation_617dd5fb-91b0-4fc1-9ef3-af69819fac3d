<template>
	<!--
		💬 聊天历史页面模板
		这是用户查看历史聊天记录的页面
		包含：月份选择、日期滑动选择、聊天记录显示等功能
		用户可以按日期查看与AI医生的历史对话
	-->

	<!--
		📄 聊天历史页面容器
		- class="chat-history-container": 基础页面样式
		- :class="fontClass": 动态字体样式类，支持多语言字体
	-->
	<view class="chat-history-container" :class="fontClass">
		<!--
			📅 月份选择器
			用户可以在这里选择要查看的月份
		-->
		<view class="month-selector">
			<!--
				⬅️ 上一月按钮
				@click: 点击时切换到上一个月
			-->
			<view class="month-nav-btn" @click="previousMonth">
				<!-- 左箭头图标 -->
				<text class="nav-icon">‹</text>
			</view>

			<!--
				📅 月份显示区域
				@click: 点击时显示年月选择器
			-->
			<view class="month-display" @click="showYearMonthPicker">
				<!--
					当前年月显示
					currentYearMonth: 当前选中的年月，格式如"2024年1月"
				-->
				<text class="month-text">{{ currentYearMonth }}</text>
				<!-- 下拉箭头图标 -->
				<text class="dropdown-icon">▼</text>
			</view>

			<!--
				➡️ 下一月按钮
				@click: 点击时切换到下一个月
			-->
			<view class="month-nav-btn" @click="nextMonth">
				<!-- 右箭头图标 -->
				<text class="nav-icon">›</text>
			</view>
		</view>

		<!--
			📅 日期滑动选择器
			用户可以左右滑动选择具体的日期
		-->
		<view class="date-slider-container" :key="refreshKey">
			<!--
				日期轮播组件
				- :current: 当前选中的日期索引
				- @change: 滑动切换时的回调函数
				- :circular="false": 不启用循环滑动
				- :display-multiple-items="5": 同时显示5个日期项
				- :previous-margin: 左侧边距，用于显示部分前一项
				- :next-margin: 右侧边距，用于显示部分后一项
			-->
			<swiper
				class="date-swiper"
				:current="currentDateIndex"
				@change="onDateChange"
				:circular="false"
				:display-multiple-items="5"
				:previous-margin="'60rpx'"
				:next-margin="'60rpx'"
			>
				<!--
					🔄 日期项循环
					v-for: 遍历dateList数组，为每个日期创建滑动项
					:key: 每个日期项的唯一标识（使用索引）
				-->
				<swiper-item
					v-for="(date, index) in dateList"
					:key="index"
					class="date-item"
				>
					<!--
						日期卡片
						:class: 当前选中的日期添加active样式
						@click: 点击时选择该日期
					-->
					<view
						class="date-card"
						:class="{ 'active': index === currentDateIndex }"
						@click="selectDate(index)"
					>
						<!--
							日期数字
							date.day: 日期的天数，如"15"
						-->
						<text class="date-number">{{ date.day }}</text>

						<!--
							星期显示 - 直接使用翻译函数
						-->
						<text class="date-weekday">{{ getWeekdayText(date.date.getDay()) }}</text>
					</view>
				</swiper-item>
			</swiper>
		</view>

		<!--
			💬 聊天记录内容区域
			显示选中日期的聊天记录
		-->
		<view class="chat-content">
			<!--
				🈳 空状态显示
				v-if="!hasChatData": 当没有聊天数据时显示
			-->
			<view v-if="!hasChatData" class="empty-state">
				<!-- 空状态图标 -->
				<view class="empty-icon">💬</view>

				<!--
					空状态文字
					$t('chatHistory.noChatData'): 从语言包获取"暂无聊天记录"文字
				-->
				<text class="empty-text">{{ $t('chatHistory.noChatData') }}</text>
			</view>
			<!-- 这里可以添加实际的聊天记录列表 -->
		</view>

		<!-- 年月选择弹窗 -->
		<view v-if="showPicker" class="picker-overlay" @click="hidePicker">
			<view class="picker-modal" :class="{ 'picker-modal-show': showPicker }" @click.stop>
				<view class="picker-header">
					<text class="picker-title">{{ $t('chatHistory.selectYearMonth') }}</text>
				</view>

				<view class="picker-content">
					<!-- 年份选择 -->
					<view class="picker-section picker-section-year" :class="{ 'picker-section-show': showPicker }">
						<view class="picker-label">
							<view class="label-with-icon">
								<text class="label-text">{{ $t('chatHistory.year') }}：</text>
								<image class="slide-icon" src="/static/icon/slideIcon.svg"></image>
								<text class="slide-text">{{ $t('chatHistory.slideToSelect') }}</text>
							</view>
						</view>
						<scroll-view
							class="picker-scroll-view"
							:class="{ 'picker-scroll-show': showPicker }"
							scroll-x="true"
							:show-scrollbar="false"
							:scroll-with-animation="true"
						>
							<view class="picker-scroll-container">
								<view
									v-for="(year, index) in yearOptions"
									:key="year"
									class="picker-option-scroll"
									:class="{
										'active': index === selectedYearIndex,
										'picker-option-show': showPicker
									}"
									:style="{ 'transition-delay': (index * 0.03) + 's' }"
									@click="selectYear(year, index)"
								>
									<text class="option-text">{{ year }}{{ $t('chatHistory.year') }}</text>
								</view>
							</view>
						</scroll-view>
					</view>

					<!-- 月份选择 -->
					<view class="picker-section picker-section-month" :class="{ 'picker-section-show': showPicker }">
						<view class="picker-label">
							<view class="label-with-icon">
								<text class="label-text">{{ $t('chatHistory.month') }}：</text>
								<image class="slide-icon" src="/static/icon/slideIcon.svg"></image>
								<text class="slide-text">{{ $t('chatHistory.slideToSelect') }}</text>
							</view>
						</view>
						<scroll-view
							class="picker-scroll-view"
							:class="{ 'picker-scroll-show': showPicker }"
							scroll-x="true"
							:show-scrollbar="false"
							:scroll-with-animation="true"
						>
							<view class="picker-scroll-container">
								<view
									v-for="(month, index) in monthOptions"
									:key="month"
									class="picker-option-scroll"
									:class="{
										'active': index === selectedMonthIndex,
										'picker-option-show': showPicker
									}"
									:style="{ 'transition-delay': (index * 0.05) + 's' }"
									@click="selectMonth(month, index)"
								>
									<text class="option-text">{{ month }}{{ $t('chatHistory.month') }}</text>
								</view>
							</view>
						</scroll-view>
					</view>
				</view>

				<view class="picker-actions" :class="{ 'picker-actions-show': showPicker }">
					<view class="picker-btn cancel-btn" @click="hidePicker">
						<text class="btn-text">{{ $t('common.cancel') }}</text>
					</view>
					<view class="picker-btn confirm-btn" @click="confirmYearMonth">
						<text class="btn-text">{{ $t('common.confirm') }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useAppStore } from '@/store/app.js'
import { t } from '@/locale/index.js'

// 应用状态管理
const appStore = useAppStore()

// 计算字体类
const fontClass = computed(() => ({
	'ug': appStore.isUyghur,
	[`lang-${appStore.lang}`]: true
}))

// 翻译方法
const $t = (key) => {
	const currentLang = appStore.lang
	return t(key, currentLang)
}

// 当前日期相关
const currentDate = new Date()
const currentYear = ref(currentDate.getFullYear())
const currentMonth = ref(currentDate.getMonth() + 1)
const currentDateIndex = ref(0)

// 弹窗相关
const showPicker = ref(false)
const selectedYear = ref(currentYear.value)
const selectedMonth = ref(currentMonth.value)
const selectedYearIndex = ref(0)
const selectedMonthIndex = ref(0)

// 是否有聊天数据
const hasChatData = ref(false)

// 强制刷新键
const refreshKey = ref(0)



// 计算当前年月显示
const currentYearMonth = computed(() => {
	const currentLang = appStore.lang
	return `${currentYear.value}${t('chatHistory.year', currentLang)}${currentMonth.value}${t('chatHistory.month', currentLang)}`
})

// 生成年份选项（2018年到2032年）
const yearOptions = computed(() => {
	const years = []
	for (let i = 2018; i <= 2032; i++) {
		years.push(i)
	}
	return years
})

// 生成月份选项
const monthOptions = computed(() => {
	return Array.from({ length: 12 }, (_, i) => i + 1)
})

// 生成当前月份的日期列表
const dateList = computed(() => {
	const year = currentYear.value
	const month = currentMonth.value
	const daysInMonth = new Date(year, month, 0).getDate()
	const dates = []

	for (let day = 1; day <= daysInMonth; day++) {
		const date = new Date(year, month - 1, day)
		dates.push({
			day,
			date: date
		})
	}

	return dates
})

// 初始化当前日期索引
const initCurrentDateIndex = () => {
	const today = new Date()
	if (today.getFullYear() === currentYear.value &&
		today.getMonth() + 1 === currentMonth.value) {
		currentDateIndex.value = today.getDate() - 1
	} else {
		currentDateIndex.value = 0
	}
}

// 上一个月
const previousMonth = () => {
	if (currentMonth.value === 1) {
		currentYear.value--
		currentMonth.value = 12
	} else {
		currentMonth.value--
	}
	initCurrentDateIndex()
}

// 下一个月
const nextMonth = () => {
	if (currentMonth.value === 12) {
		currentYear.value++
		currentMonth.value = 1
	} else {
		currentMonth.value++
	}
	initCurrentDateIndex()
}

// 显示年月选择器
const showYearMonthPicker = () => {
	selectedYear.value = currentYear.value
	selectedMonth.value = currentMonth.value

	// 计算当前年份和月份的索引
	selectedYearIndex.value = yearOptions.value.findIndex(year => year === currentYear.value)
	selectedMonthIndex.value = currentMonth.value - 1

	showPicker.value = true
}

// 隐藏年月选择器
const hidePicker = () => {
	showPicker.value = false
}

// 选择年份
const selectYear = (year, index) => {
	selectedYear.value = year
	selectedYearIndex.value = index
}

// 选择月份
const selectMonth = (month, index) => {
	selectedMonth.value = month
	selectedMonthIndex.value = index
}



// 确认年月选择
const confirmYearMonth = () => {
	currentYear.value = selectedYear.value
	currentMonth.value = selectedMonth.value
	initCurrentDateIndex()
	showPicker.value = false
}

// 日期滑动变化
const onDateChange = (e) => {
	currentDateIndex.value = e.detail.current
}

// 选择日期
const selectDate = (index) => {
	currentDateIndex.value = index
}

// 获取星期文本 - 直接翻译函数
const getWeekdayText = (dayIndex) => {
	const weekdayKeys = [
		'chatHistory.weekdays.sunday',
		'chatHistory.weekdays.monday',
		'chatHistory.weekdays.tuesday',
		'chatHistory.weekdays.wednesday',
		'chatHistory.weekdays.thursday',
		'chatHistory.weekdays.friday',
		'chatHistory.weekdays.saturday'
	]
	const currentLang = appStore.lang
	const key = weekdayKeys[dayIndex]

	// 简化调试
	console.log(`getWeekdayText(${dayIndex}) - Key: ${key}, Lang: ${currentLang}`)

	// 直接测试翻译
	const result = t(key, currentLang)
	console.log(`Translation result:`, result)

	// 如果翻译失败，返回默认值
	if (result === key) {
		const fallbackWeekdays = {
			'ug': ['يەكشەنبە', 'دۈشەنبە', 'سەيشەنبە', 'چارشەنبە', 'پەيشەنبە', 'جۈمە', 'شەنبە'],
			'zh-CN': ['日', '一', '二', '三', '四', '五', '六'],
			'en': ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
		}
		const fallback = fallbackWeekdays[currentLang]?.[dayIndex] || `Day${dayIndex}`
		console.log(`Using fallback:`, fallback)
		return fallback
	}

	return result
}

// 监听语言变化，强制更新日期列表
watch(() => appStore.lang, (newLang) => {
	console.log('ChatHistory - Language changed to:', newLang)
	// 强制重新计算日期列表
	refreshKey.value++
	initCurrentDateIndex()
}, { immediate: false })

// 页面加载时初始化
onMounted(() => {
	initCurrentDateIndex()
})
</script>

<style lang="scss">
.chat-history-container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding: 20rpx;
}

/* 月份选择器 */
.month-selector {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;
	padding: 0 40rpx;
}

.month-nav-btn {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.nav-icon {
	font-size: 48rpx;
	color: #666;
	font-weight: bold;
}

.month-display {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: rgba(76, 175, 80, 0.1);
	border-radius: 50rpx;
	padding: 20rpx 40rpx;
	margin: 0 20rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.month-display:active {
	background-color: rgba(76, 175, 80, 0.2);
}

.month-text {
	font-size: 32rpx;
	color: #4CAF50;
	font-weight: 600;
	margin-right: 10rpx;
}

.dropdown-icon {
	font-size: 24rpx;
	color: #4CAF50;
}

/* 日期滑动选择器 */
.date-slider-container {
	margin-bottom: 40rpx;
}

.date-swiper {
	height: 160rpx;
}

.date-item {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 10rpx;
}

.date-card {
	width: 120rpx;
	height: 120rpx;
	background-color: white;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	cursor: pointer;
	transition: all 0.3s ease;
}

.date-card.active {
	background-color: #4CAF50;
	transform: scale(1.1);
}

.date-number {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}

.date-card.active .date-number {
	color: white;
}

.date-weekday {
	font-size: 24rpx;
	color: #666;
}

/* 维吾尔文字体支持 */
.ug .date-weekday,
.lang-ug .date-weekday {
	font-family: 'UKIJ Tuz Tom', 'UKIJ Tuz', 'Alkatip Basma Tom', 'Alkatip Basma', sans-serif;
	font-size: 20rpx; /* 维吾尔文字体稍小一些以适应显示 */
}

.date-card.active .date-weekday {
	color: white;
}

/* 聊天内容区域 */
.chat-content {
	flex: 1;
	min-height: 600rpx;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 40rpx;
	opacity: 0.3;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

/* 年月选择弹窗 */
.picker-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.picker-modal {
	background-color: white;
	border-radius: 24rpx;
	width: 640rpx;
	max-height: 80vh;
	overflow: hidden;
	margin: 40rpx;
	transform: scale(0.8) translateY(50rpx);
	opacity: 0;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.picker-modal-show {
	transform: scale(1) translateY(0);
	opacity: 1;
}

.picker-header {
	padding: 40rpx;
	text-align: center;
	border-bottom: 1rpx solid #f0f0f0;
}

.picker-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.picker-content {
	padding: 40rpx;
	max-height: 60vh;
	overflow-y: auto;
}

.picker-section {
	margin-bottom: 40rpx;
	transform: translateX(-100%);
	opacity: 0;
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.picker-section-year {
	transition-delay: 0.1s;
}

.picker-section-month {
	transition-delay: 0.2s;
}

.picker-section-show {
	transform: translateX(0);
	opacity: 1;
}

.picker-section:last-child {
	margin-bottom: 0;
}

.picker-label {
	display: flex;
	align-items: center;
	margin-bottom: 24rpx;
}

.label-with-icon {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.label-text {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.slide-hint {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.slide-icon {
	width: 32rpx;
	height: 32rpx;
	opacity: 0.6;
}

.slide-text {
	font-size: 24rpx;
	color: #999;
	
}

/* 滑动选择器 */
.picker-scroll-view {
	height: 120rpx;
	margin: 20rpx 0;
	width: 100%;
	transform: translateY(30rpx);
	opacity: 0;
	transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.picker-scroll-show {
	transform: translateY(0);
	opacity: 1;
}

.picker-scroll-container {
	display: flex;
	align-items: center;
	height: 100%;
	padding: 0 60rpx 0 60rpx;
	white-space: nowrap;
}

.picker-swiper-item {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0 10rpx;
	
}

.picker-option-scroll {
	width: 160rpx;
	height: 80rpx;
	background-color: #f8f8f8;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	border: 2rpx solid #e8e8e8;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
	margin-right: 20rpx;
	flex-shrink: 0;
	transform: translateY(20rpx) scale(0.9);
	opacity: 0;
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.picker-option-show {
	transform: translateY(0) scale(1);
	opacity: 1;
}

.picker-option-scroll:last-child {
	margin-right: 60rpx;
}

.picker-option-scroll.active {
	background-color: #4CAF50;
	border-color: #4CAF50;
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.25);
	transform: scale(1.05);

}

.option-text {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.picker-option-scroll.active .option-text {
	color: white;
	font-weight: 600;
}

.picker-actions {
	display: flex;
	border-top: 1rpx solid #f0f0f0;
	transform: translateY(50rpx);
	opacity: 0;
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	transition-delay: 0.3s;
}

.picker-actions-show {
	transform: translateY(0);
	opacity: 1;
}

.picker-btn {
	flex: 1;
	height: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.cancel-btn {
	border-right: 1rpx solid #f0f0f0;
}

.cancel-btn:active {
	background-color: #f8f8f8;
}

.confirm-btn {
	background-color: #4CAF50;
}

.confirm-btn:active {
	background-color: #45a049;
}

.btn-text {
	font-size: 32rpx;
	color: #333;
}

.confirm-btn .btn-text {
	color: white;
	font-weight: 600;
}

/* ==================== RTL布局支持 ==================== */
/* 维吾尔文RTL模式下的样式调整 */
.rtl, .lang-ug, .ug {

	/* 月份选择器RTL调整 */
	.month-selector {
		flex-direction: row-reverse;
	}

	.month-text {
		margin-right: 0;
		margin-left: 10rpx;
	}

	/* 弹窗内容RTL调整 */
	.picker-header {
		text-align: right;
	}

	.picker-title {
		text-align: right;
	}

	.label-text {
		text-align: right;
	}

	.slide-text {
		text-align: right;
	}

	/* 选项文本右对齐 */
	.option-text {
		text-align: center; /* 保持居中对齐 */
	}

	/* 按钮文本RTL调整 */
	.btn-text {
		text-align: center; /* 保持居中对齐 */
	}

	/* 空状态文本RTL调整 */
	.empty-text {
		text-align: right;
	}

	/* 日期卡片文本保持居中 */
	.date-number,
	.date-weekday {
		text-align: center;
	}
}

</style>
