<template>
	<!--
		🤖 AI聊天界面组件模板
		这是AI咨询的核心组件，处理用户与AI医生的对话交互
		包含：消息列表、用户消息、AI回复、语音播放、打字效果等功能
	-->

	<!--
		💬 聊天界面容器
		整个AI聊天功能的主容器
	-->
	<view class="chat-interface">
		<!--
			📜 聊天消息列表 - 可滚动区域
			这是显示所有聊天消息的滚动容器
			- scroll-y="true": 启用垂直滚动
			- :scroll-top: 控制滚动位置，用于自动滚动到最新消息
			- :show-scrollbar="false": 隐藏滚动条
			- scroll-with-animation="true": 滚动时启用动画效果
			- :enable-back-to-top="false": 禁用回到顶部功能
			- :scroll-anchoring="true": 启用滚动锚定，保持滚动位置稳定
		-->
		<scroll-view
			class="chat-messages"
			scroll-y="true"
			:scroll-top="scrollTop"
			:show-scrollbar="false"
			scroll-with-animation="true"
			:enable-back-to-top="false"
			:scroll-anchoring="true"
		>
			<!-- 消息列表容器 -->
			<view class="message-list">
				<!--
					🔄 循环显示所有聊天消息
					v-for: 遍历messages数组中的每条消息
					:key: 每条消息的唯一标识（使用索引）
					:class: 根据消息类型添加不同的样式类
				-->
				<view
					v-for="(message, index) in messages"
					:key="index"
					class="message-item"
					:class="message.type"
				>
					<!--
						👤 用户消息（显示在右侧）
						v-if: 只有当消息类型为'user'时才显示
						用户发送的消息会显示在聊天界面的右侧
					-->
					<view v-if="message.type === 'user'" class="message-wrapper user-message">
						<!-- 用户消息内容区域 -->
						<view class="message-content">
							<!--
								✏️ 编辑按钮
								用户可以点击编辑自己发送的消息
								@tap: 点击时触发编辑消息功能
							-->
							<view class="edit-button" @tap="editMessage(message.content)">
								<!-- 编辑图标 -->
								<image src="/static/icon/editIcon.png"></image>
							</view>

							<!--
								💭 用户消息气泡
								显示用户发送的消息内容
							-->
							<view class="message-bubble user-bubble">
								<!--
									消息文字
									message.content: 消息的具体内容
									class="ug": 支持维吾尔文字体显示
								-->
								<text class="message-text ug">{{ message.content }}</text>
							</view>
						</view>

						<!--
							👤 用户头像
							显示用户的头像图标
						-->
						<view class="avatar user-avatar">
							<!-- 用户图标，使用FirstUI的person图标 -->
							<fui-icon name="person" :size="36" color="#ffffff"></fui-icon>
						</view>
					</view>

					<!--
						🤖 AI消息（显示在左侧）
						v-if: 只有当消息类型为'ai'时才显示
						AI回复的消息会显示在聊天界面的左侧
					-->
					<view v-if="message.type === 'ai'" class="message-wrapper ai-message">
						<!--
							🤖 AI头像
							显示AI医生的头像
						-->
						<view class="avatar ai-avatar">
							<!--
								AI头像图片
								:src: AI头像图片路径，来自aiAvatar变量
							-->
							<image :src="aiAvatar" class="avatar-img"></image>
						</view>

						<!-- AI消息内容区域 -->
						<view class="message-content">
							<!--
								💭 AI消息气泡
								显示AI回复的消息内容
							-->
							<view class="message-bubble ai-bubble">
								<!--
									AI回复文字
									message.content: AI回复的具体内容
									class="ug": 支持维吾尔文字体显示
								-->
								<text class="message-text ug">{{ message.content }}</text>
							</view>
							<!-- 🔊 语音播放按钮 - 位置在气泡右下角外侧 -->
							<view class="voice-button-container">
								<view class="voice-button" @tap="playVoice(message.content)" :class="{ 'playing': isPlaying }">
									<image src="/static/icon/wifiIcon.png" class="voice-icon-img" mode="aspectFit"></image>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 正在输入提示 -->
				<view v-if="isTyping" class="message-item ai">
					<view class="message-wrapper ai-message">
						<view class="avatar ai-avatar">
							<image :src="aiAvatar" class="avatar-img"></image>
						</view>
						<view class="message-content">
							<view class="ai-message-container">
								<view class="message-bubble ai-bubble typing-bubble">
									<view class="typing-indicator">
										<view class="dot"></view>
										<view class="dot"></view>
										<view class="dot"></view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script setup>
	import { ref, nextTick, watch, onMounted } from 'vue';
	import fuiIcon from '@/components/firstui/fui-icon/fui-icon.vue';

	// 定义props
	const props = defineProps({
		aiAvatar: {
			type: String,
			default: '/static/doctor1.jpg'
		},
		userAvatar: {
			type: String,
			default: '/static/user-avatar.jpg'
		},
		// 接收来自父组件的消息
		inputMessage: {
			type: String,
			default: ''
		},
		// 接收来自父组件的历史消息
		historyMessages: {
			type: Array,
			default: () => []
		}
	});

	// 定义emits
	const emit = defineEmits(['messageProcessed', 'editMessage']);

	// 聊天数据
	const messages = ref([]);
	const isTyping = ref(false);
	const scrollTop = ref(0);

	// 初始化聊天
	onMounted(() => {
		// 不再自动添加欢迎消息，保持页面干净
		console.log('AiConsultation组件已挂载，等待用户消息');
	});

	// 编辑消息功能
	const editMessage = (content) => {
		// 触发事件，将消息内容传递给父组件的输入框
		emit('editMessage', content);
	};

	// 语音播放功能
	const playVoice = (content) => {
		// 使用浏览器的语音合成API
		if ('speechSynthesis' in window) {
			// 停止当前播放
			speechSynthesis.cancel();

			const utterance = new SpeechSynthesisUtterance(content);
			utterance.lang = 'zh-CN'; // 设置中文
			utterance.rate = 0.8; // 语速
			utterance.pitch = 1; // 音调

			speechSynthesis.speak(utterance);
		} else {
			uni.showToast({
				title: '您的设备不支持语音播放',
				icon: 'none'
			});
		}
	};

	// 监听来自父组件的消息
	watch(() => props.inputMessage, (newMessage) => {
		console.log('AiConsultation收到消息:', newMessage);
		if (newMessage && newMessage.trim()) {
			// 检查是否是历史对话标识
			if (newMessage.startsWith('[HISTORY:')) {
				console.log('加载历史消息');
				loadHistoryMessages();
			} else {
				// 立即发送用户消息并开始AI搜索
				console.log('开始自动搜索:', newMessage);
				sendUserMessage(newMessage);
			}

			// 通知父组件消息已处理
			emit('messageProcessed');
		}
	}, { immediate: true });

	// 监听历史消息的变化
	watch(() => props.historyMessages, (newHistoryMessages) => {
		console.log('历史消息发生变化:', newHistoryMessages);
		if (newHistoryMessages && newHistoryMessages.length > 0) {
			console.log('自动加载历史消息');
			loadHistoryMessages();
		}
	}, { immediate: true, deep: true });

	// 添加消息
	const addMessage = (type, content) => {
		const message = {
			type,
			content,
			time: formatTime(new Date())
		};
		messages.value.push(message);
		scrollToBottom();
	};

	// 发送用户消息
	const sendUserMessage = (content) => {
		console.log('开始处理用户消息:', content);

		// 立即添加用户消息到对话中
		addMessage('user', content);

		// 立即开始AI搜索状态
		isTyping.value = true;
		console.log('AI开始搜索和分析...');

		// 模拟AI搜索和分析过程
		setTimeout(() => {
			isTyping.value = false;
			const aiResponse = getAiResponse(content);
			console.log('AI搜索完成，返回结果:', aiResponse);
			addMessage('ai', aiResponse);
		}, 1500);
	};

	// 加载历史消息
	const loadHistoryMessages = () => {
		console.log('开始加载历史消息:', props.historyMessages);

		// 清空当前消息
		messages.value = [];

		// 加载历史消息到当前对话中
		if (props.historyMessages && props.historyMessages.length > 0) {
			// 复制历史消息到当前消息列表
			messages.value = [...props.historyMessages];
			console.log('历史消息加载完成，消息数量:', messages.value.length);

			// 滚动到底部显示最新消息
			nextTick(() => {
				scrollToBottom();
			});
		} else {
			console.log('没有历史消息需要加载');
		}
	};

	// 智能AI搜索回复
	const getAiResponse = (userMessage) => {
		console.log('AI正在分析用户问题:', userMessage);

		// 根据用户输入的关键词给出相应的搜索结果
		const message = userMessage.toLowerCase();

		// 头痛相关搜索
		if (message.includes('头痛') || message.includes('头疼') || message.includes('头晕')) {
			return '🔍 搜索结果：头痛症状分析\n\n根据您的症状，头痛可能由以下原因引起：\n\n1️⃣ **紧张性头痛**：最常见，通常由压力、疲劳引起\n2️⃣ **偏头痛**：一侧头部剧烈疼痛，可能伴有恶心\n3️⃣ **颈椎问题**：长期低头工作导致\n4️⃣ **睡眠不足**：休息不够引起\n\n💡 **建议处理方式**：\n• 充足休息，保持规律作息\n• 适当按摩太阳穴\n• 避免长时间用眼\n• 如持续或加重，请及时就医';
		}
		// 发烧相关搜索
		else if (message.includes('发烧') || message.includes('发热') || message.includes('体温')) {
			return '🔍 搜索结果：发热症状处理\n\n发热是身体抵抗感染的自然反应：\n\n🌡️ **体温分级**：\n• 37.3-38°C：低热\n• 38.1-39°C：中等热\n• 39.1-41°C：高热\n\n💡 **处理建议**：\n• 多喝温开水，补充水分\n• 物理降温：温水擦拭身体\n• 充分休息，避免剧烈活动\n• 体温超过38.5°C或持续不退，请及时就医\n• 观察是否伴有其他症状';
		}
		// 咳嗽相关搜索
		else if (message.includes('咳嗽') || message.includes('咳痰') || message.includes('喉咙')) {
			return '🔍 搜索结果：咳嗽症状分析\n\n咳嗽类型及可能原因：\n\n🔸 **干咳**：可能是感冒初期、过敏\n🔸 **有痰咳嗽**：可能是呼吸道感染\n🔸 **夜间咳嗽**：可能是哮喘、胃食管反流\n\n💡 **缓解方法**：\n• 多喝温水，保持喉咙湿润\n• 蜂蜜水有助缓解咳嗽\n• 保持室内空气湿润\n• 避免烟雾、粉尘等刺激\n• 咳嗽超过一周或伴有血痰，请就医';
		}
		// 胃痛相关搜索
		else if (message.includes('胃痛') || message.includes('肚子疼') || message.includes('腹痛')) {
			return '🔍 搜索结果：腹痛症状分析\n\n腹痛可能的原因：\n\n🔸 **胃炎/胃溃疡**：上腹部疼痛\n🔸 **消化不良**：饭后腹胀、疼痛\n🔸 **肠胃炎**：腹痛伴腹泻\n🔸 **饮食不当**：暴饮暴食、生冷食物\n\n💡 **缓解建议**：\n• 暂时禁食，让胃部休息\n• 喝温开水或淡盐水\n• 避免辛辣、油腻食物\n• 少食多餐，细嚼慢咽\n• 疼痛剧烈或持续，请及时就医';
		}
		// 失眠相关搜索
		else if (message.includes('失眠') || message.includes('睡不着') || message.includes('睡眠')) {
			return '🔍 搜索结果：睡眠问题解决方案\n\n失眠常见原因：\n\n🔸 **心理因素**：压力、焦虑、抑郁\n🔸 **生活习惯**：晚睡、饮食不当\n🔸 **环境因素**：噪音、光线、温度\n🔸 **身体疾病**：疼痛、呼吸问题\n\n💡 **改善方法**：\n• 建立规律的睡眠时间\n• 睡前1小时避免电子设备\n• 保持卧室安静、黑暗、凉爽\n• 避免睡前饮用咖啡、茶\n• 尝试放松技巧：深呼吸、冥想\n• 长期失眠请咨询医生';
		}
		// 感冒相关搜索
		else if (message.includes('感冒') || message.includes('流鼻涕') || message.includes('鼻塞')) {
			return '🔍 搜索结果：感冒症状处理\n\n感冒常见症状及处理：\n\n🔸 **鼻塞流涕**：病毒感染引起\n🔸 **喉咙痛**：炎症反应\n🔸 **轻微发热**：身体抵抗病毒\n🔸 **全身乏力**：免疫系统工作\n\n💡 **康复建议**：\n• 充分休息，增强免疫力\n• 多喝温水，保持水分\n• 盐水漱口缓解喉咙痛\n• 蒸汽吸入缓解鼻塞\n• 避免去人群密集场所\n• 症状严重或持续超过一周请就医';
		}
		else {
			// 通用健康搜索结果
			const responses = [
				'🔍 搜索结果：健康咨询建议\n\n感谢您的咨询，根据您的描述：\n\n💡 **一般建议**：\n• 保持良好的生活习惯\n• 规律作息，充足睡眠\n• 均衡饮食，适量运动\n• 定期体检，预防为主\n• 如有不适症状持续，请及时就医\n\n如需更详细的建议，请描述具体症状。',
				'🔍 搜索结果：健康管理指导\n\n根据您的健康咨询：\n\n💡 **健康维护**：\n• 保持积极乐观的心态\n• 适当进行体育锻炼\n• 注意饮食营养搭配\n• 避免过度疲劳和压力\n• 有症状变化及时关注\n\n建议您详细描述症状以获得更精准的建议。',
				'🔍 搜索结果：专业健康建议\n\n感谢您信任我们的健康咨询：\n\n💡 **预防保健**：\n• 建立健康的生活方式\n• 定期进行健康检查\n• 关注身体信号变化\n• 及时处理健康问题\n• 保持良好的心理状态\n\n如有具体症状，请提供更多详细信息。'
			];
			return responses[Math.floor(Math.random() * responses.length)];
		}
	};

	// 滚动到底部
	const scrollToBottom = () => {
		nextTick(() => {
			const query = uni.createSelectorQuery();
			query.select('.message-list').boundingClientRect();
			query.select('.chat-messages').boundingClientRect();
			query.exec((res) => {
				if (res[0] && res[1]) {
					const messageListHeight = res[0].height;
					const scrollViewHeight = res[1].height;
					if (messageListHeight > scrollViewHeight) {
						scrollTop.value = messageListHeight - scrollViewHeight + 50;
					}
				}
			});
		});
	};

	// 格式化时间
	const formatTime = (timestamp) => {
		const date = new Date(timestamp);
		const hours = date.getHours().toString().padStart(2, '0');
		const minutes = date.getMinutes().toString().padStart(2, '0');
		return `${hours}:${minutes}`;
	};

	// 监听消息变化，自动滚动到底部
	watch(() => messages.value, () => {
		scrollToBottom();
	}, { deep: true });

	// 监听正在输入状态
	watch(() => isTyping.value, () => {
		if (isTyping.value) {
			scrollToBottom();
		}
	});

	// 暴露方法和数据给父组件
	defineExpose({
		sendUserMessage,
		addMessage,
		scrollToBottom,
		messages  // 暴露messages数据供父组件访问
	});
</script>

<style lang="scss" scoped>
	.chat-interface {
		height: 100%;
		display: flex;
		flex-direction: column;
		background-color: #f8f9fa; /* 调整为图片中的浅灰色背景 */
	}

	/* 聊天消息区域 */
	.chat-messages {
		flex: 1;
		height: 100%;
		padding: 0; /* 移除额外的内边距 */
		position: relative;
		overflow: hidden;
	}

	.message-list {
		padding: 32rpx 30rpx; /* 调整内边距，符合图片设计 */
		padding-bottom: 60rpx; /* 增加底部间距 */
		min-height: 100%;
		box-sizing: border-box;
	}

	.message-item {
		margin-bottom: 32rpx; /* 增加消息间距 */
	}

	.message-wrapper {
		display: flex;
		align-items: flex-start;
		gap: 24rpx; /* 调整头像和消息的间距 */
		margin-bottom: 0; /* 移除额外的底部间距 */
		position: relative;
	}

	/* 用户消息样式（右侧） */
	.user-message {
		justify-content: flex-end;
		align-items: flex-start;
		flex-direction: row;
		position: relative; /* 为编辑按钮提供定位参考 */
	}

	.user-message .message-content {
		max-width: calc(100% - 140rpx); /* 调整最大宽度 */
		position: relative;
		margin-left: 60rpx; /* 为编辑按钮留出空间 */
	}

	.user-bubble {
		background-color:#109d58; /* 蓝色背景，符合图片设计 */
		border-radius: 80rpx 50rpx 10rpx 80rpx; /* 调整圆角，更符合图片中的样式 */
		position: relative;
		padding: 20rpx 24rpx; /* 调整内边距 */
		color: white;
		font-size: 30rpx; /* 调整字体大小 */
		line-height: 1.4; /* 调整行高 */
		max-width: 480rpx; /* 限制最大宽度，符合图片比例 */
	}

	/* 编辑按钮 - 调试版本，先让它显示出来 */
	.edit-button {
		position: absolute;
		top: -1rpx;
		left: -80rpx;
		width: 40rpx;
		height: 40rpx;
		
		
		// background-color: #ff6b6b; /* 使用红色便于调试 */
		// border-radius: 50%;
		// display: flex;
		// align-items: center;
		// justify-content: center;
		// z-index: 999;
		// box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4);
		// border: 2rpx solid #fff;
		image{
			width: 100%;
			height: 100%;
		}
	}

	/* AI消息样式（左侧） */
	.ai-message {
		justify-content: flex-start;
		align-items: flex-start;
		flex-direction: row;
	}

	.ai-message .message-content {
		max-width: calc(100% - 140rpx); /* 调整最大宽度 */
		position: relative;
		margin-right: 80rpx; /* 为语音按钮留出更多空间 */
	}



	.ai-bubble {
		background-color: #ffffff; /* 白色背景，符合图片设计 */
		border-radius: 20rpx; /* 调整圆角，更符合图片中的样式 */
		position: relative;
		padding: 20rpx 24rpx; /* 调整内边距 */
		flex: 1;
		font-size: 30rpx; /* 调整字体大小 */
		line-height: 1.4; /* 调整行高 */
		color: #333333; /* 深灰色文字 */
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06); /* 添加轻微阴影 */
		border: 1rpx solid #f0f0f0; /* 添加边框 */
		max-width: 520rpx; /* AI消息可以稍微宽一些 */
	}

	/* 语音播放按钮容器 - 位于消息气泡右下角外侧 */
	.voice-button-container {
		position: absolute;
		bottom: 530rpx; /* 改为底部对齐 */
		right: -90rpx; /* 调整位置 */
		z-index: 10;
	}

	/* 语音播放按钮 - 按照图片样式调整 */
	.voice-button {
		width: 60rpx; /* 稍微增大按钮 */
		height: 60rpx;
		background-color: #ffffff; /* 白色背景 */
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 2rpx solid #e8e8e8; /* 浅灰色边框 */
		transition: all 0.2s ease;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08); /* 调整阴影 */
	}

	/* 播放状态 */
	.voice-button.playing {
		background-color: #ff6b6b;
		border-color: #ff5252;
		animation: pulse 1.5s infinite;
	}

	@keyframes pulse {
		0% { transform: scale(1); }
		50% { transform: scale(1.1); }
		100% { transform: scale(1); }
	}

	/* 语音图标图片 */
	.voice-icon-img {
		width: 32rpx; /* 调整图标大小 */
		height: 32rpx;
		opacity: 0.7; /* 调整透明度 */
		transition: all 0.3s ease;
		filter: brightness(0.8) contrast(1.1); /* 调整滤镜效果 */
	}

	/* 播放状态 - 图标高亮 */
	.voice-button.playing .voice-icon-img {
		opacity: 1;
		filter: brightness(1.2) contrast(1.3) saturate(1.2);
		transform: scale(1.05);
	}

	/* 头像样式 */
	.avatar {
		width: 80rpx; /* 调整头像大小，更符合图片比例 */
		height: 80rpx;
		border-radius: 50%;
		overflow: hidden;
		flex-shrink: 0;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.ai-avatar {
		background-color: #ffffff; /* AI头像背景为白色 */
		border: 2rpx solid #e8e8e8; /* 浅灰色边框 */
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08); /* 添加阴影 */
	}

	.user-avatar {
		background-color: #10a35c; /* 用户头像绿色背景 */
		// border: 2rpx solid #4cd964; /* 绿色边框 */
	}

	.avatar-img {
		width: 100%;
		height: 100%;
		border-radius: 50%;
	}

	/* 消息文本样式 */
	.message-text {
		font-size: 30rpx; /* 调整字体大小 */
		line-height: 1.5; /* 调整行高 */
		color: #333333;
		word-wrap: break-word;
		word-break: break-all;
		font-weight: 400; /* 正常字重 */
	}

	.user-bubble .message-text {
		color: #ffffff; /* 用户消息白色文字 */
	}

	.ai-bubble .message-text {
		color: #333333; /* AI消息深灰色文字 */
	}

	/* 正在输入动画 */
	.typing-bubble {
		background-color: #ffffff; /* 白色背景，与AI消息一致 */
		border-radius: 20rpx; /* 与AI消息圆角一致 */
		border: 1rpx solid #f0f0f0; /* 与AI消息边框一致 */
		padding: 20rpx 24rpx; /* 与AI消息内边距一致 */
		min-height: 60rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06); /* 添加阴影 */
	}

	.typing-indicator {
		display: flex;
		gap: 8rpx;
		align-items: center;
		justify-content: center;
		width: 100%;
	}

	.dot {
		width: 10rpx; /* 稍微调小 */
		height: 10rpx;
		border-radius: 50%;
		background-color: #4cd964; /* 使用绿色，与主题色一致 */
		animation: typing 1.4s infinite ease-in-out;
	}

	.dot:nth-child(1) {
		animation-delay: -0.32s;
	}

	.dot:nth-child(2) {
		animation-delay: -0.16s;
	}

	@keyframes typing {
		0%, 80%, 100% {
			transform: scale(0.8);
			opacity: 0.5;
		}
		40% {
			transform: scale(1);
			opacity: 1;
		}
	}
</style>