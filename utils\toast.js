/**
 * 🍞 统一的Toast工具函数库
 *
 * 这个文件提供了统一的Toast消息显示功能
 * 目的是：
 * 1. 避免在项目中重复编写uni.showToast调用代码
 * 2. 统一管理Toast的样式和行为
 * 3. 提供语义化的函数名，提高代码可读性
 * 4. 便于统一修改Toast的默认配置
 *
 * 📋 提供的功能：
 * - 基础Toast显示
 * - 成功Toast（绿色勾号）
 * - 错误Toast（红色叉号）
 * - 加载Toast（转圈动画）
 * - 纯文本Toast（无图标）
 * - 加载状态管理
 */

// 导入UI常量配置
import { UI_CONSTANTS } from './constants.js'

/**
 * 🔧 基础Toast显示函数
 *
 * 这是所有Toast函数的基础，其他Toast函数都会调用这个函数
 *
 * @param {string} title - 要显示的消息内容
 * @param {string} icon - 图标类型（success/error/loading/none）
 * @param {number} duration - 显示持续时间（毫秒）
 * @param {boolean} mask - 是否显示透明蒙层，防止用户操作
 *
 * 💡 使用示例：
 * showToast('操作成功', 'success', 2000, false)
 */
export const showToast = (
  title,                                      // 消息文字
  icon = UI_CONSTANTS.TOAST_ICONS.NONE,      // 默认无图标
  duration = UI_CONSTANTS.TOAST_DURATION,    // 默认显示2秒
  mask = false                               // 默认不显示蒙层
) => {
  // 🛡️ 安全检查：如果没有标题内容，直接返回不显示
  if (!title) return

  // 📱 调用uni-app的原生Toast API
  uni.showToast({
    title,      // 消息内容
    icon,       // 图标类型
    duration,   // 显示时长
    mask        // 是否显示蒙层
  })
}

/**
 * ✅ 显示成功Toast
 *
 * 用于显示操作成功的消息，会显示绿色的勾号图标
 *
 * @param {string} title - 成功消息内容
 * @param {number} duration - 显示时长（可选，默认2秒）
 *
 * 💡 使用示例：
 * showSuccessToast('保存成功')
 * showSuccessToast('登录成功', 3000)  // 显示3秒
 */
export const showSuccessToast = (title, duration = UI_CONSTANTS.TOAST_DURATION) => {
  showToast(title, UI_CONSTANTS.TOAST_ICONS.SUCCESS, duration)
}

/**
 * ❌ 显示错误Toast
 *
 * 用于显示操作失败或错误的消息，会显示红色的叉号图标
 *
 * @param {string} title - 错误消息内容
 * @param {number} duration - 显示时长（可选，默认2秒）
 *
 * 💡 使用示例：
 * showErrorToast('网络连接失败')
 * showErrorToast('密码错误', 3000)  // 显示3秒
 */
export const showErrorToast = (title, duration = UI_CONSTANTS.TOAST_DURATION) => {
  showToast(title, UI_CONSTANTS.TOAST_ICONS.ERROR, duration)
}

/**
 * ⏳ 显示加载Toast
 * @param {string} title - 加载消息
 * @param {number} duration - 显示时长
 */
export const showLoadingToast = (title, duration = UI_CONSTANTS.TOAST_DURATION) => {
  showToast(title, UI_CONSTANTS.TOAST_ICONS.LOADING, duration, true)
}

/**
 * 显示普通Toast（无图标）
 * @param {string} title - 消息内容
 * @param {number} duration - 显示时长
 */
export const showPlainToast = (title, duration = UI_CONSTANTS.TOAST_DURATION) => {
  showToast(title, UI_CONSTANTS.TOAST_ICONS.NONE, duration)
}

/**
 * 隐藏Toast
 */
export const hideToast = () => {
  uni.hideToast()
}

/**
 * 显示Loading（无文字）
 */
export const showLoading = (title = '加载中...', mask = true) => {
  uni.showLoading({
    title,
    mask
  })
}

/**
 * 隐藏Loading
 */
export const hideLoading = () => {
  uni.hideLoading()
}

/**
 * 显示模态对话框
 * @param {string} title - 标题
 * @param {string} content - 内容
 * @param {boolean} showCancel - 是否显示取消按钮
 * @param {string} confirmText - 确认按钮文字
 * @param {string} cancelText - 取消按钮文字
 * @param {string} confirmColor - 确认按钮颜色
 * @returns {Promise<boolean>} - 用户选择结果
 */
export const showModal = (
  title = '提示',
  content = '',
  showCancel = true,
  confirmText = UI_CONSTANTS.BUTTON_TEXTS.CONFIRM,
  cancelText = UI_CONSTANTS.BUTTON_TEXTS.CANCEL,
  confirmColor = '#465CFF'
) => {
  return new Promise((resolve) => {
    uni.showModal({
      title,
      content,
      showCancel,
      confirmText,
      cancelText,
      confirmColor,
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

/**
 * 显示确认对话框
 * @param {string} content - 确认内容
 * @param {string} title - 标题
 * @returns {Promise<boolean>} - 用户选择结果
 */
export const showConfirm = (content, title = '确认') => {
  return showModal(title, content, true)
}

/**
 * 显示警告对话框
 * @param {string} content - 警告内容
 * @param {string} title - 标题
 * @returns {Promise<boolean>} - 用户选择结果
 */
export const showAlert = (content, title = '警告') => {
  return showModal(title, content, false)
}

/**
 * 显示操作菜单
 * @param {Array<string>} itemList - 菜单项列表
 * @returns {Promise<number>} - 用户选择的索引，取消返回-1
 */
export const showActionSheet = (itemList) => {
  return new Promise((resolve) => {
    uni.showActionSheet({
      itemList,
      success: (res) => {
        resolve(res.tapIndex)
      },
      fail: () => {
        resolve(-1)
      }
    })
  })
}
