# 健康助手小程序API接口速查表

## 快速索引

### 用户相关接口
```
GET    /applet/v1/app/profile                    # 获取用户资料
POST   /applet/v1/app/update_profile             # 更新用户资料
POST   /applet/v1/app/upload_avatar              # 上传头像
GET    /applet/index/vip_list                    # VIP价格列表
```

### 健康档案接口 - 已移除
```
# 健康档案相关接口已从项目中移除
```

### 地址管理接口
```
GET    /applet/v1/addresses                      # 获取地址列表
POST   /applet/v1/addresses                      # 创建地址
GET    /applet/v1/addresses/simple               # 获取简化地址列表
GET    /applet/v1/addresses/{id}                 # 获取地址详情
PUT    /applet/v1/addresses/{id}                 # 更新地址
DELETE /applet/v1/addresses/{id}                 # 删除地址
POST   /applet/v1/addresses/set-default          # 设置默认地址
```

### 分销员管理接口
```
POST   /applet/user/apply_ref                    # 申请成为分销员
GET    /applet/user/get_moneys                   # 获取收入信息
GET    /applet/user/get_balance_r                # 获取余额变动记录
GET    /applet/user/l_level_users                # 获取下级用户列表
POST   /applet/user/up_dist_level                # 修改下级分销等级
GET    /applet/user/get_poster                   # 获取分销海报列表
GET    /applet/index/dist_level                  # 获取分销等级列表
```

### AI聊天咨询接口
```
GET    /applet/v1/chat/conversations             # 获取对话列表
POST   /applet/v1/chat/conversations             # 创建新对话
GET    /applet/v1/chat/conversations/{id}/messages    # 获取对话消息
POST   /applet/v1/chat/conversations/{id}/messages    # 发送消息
PUT    /applet/v1/chat/conversations/{id}/title       # 更新对话标题
DELETE /applet/v1/chat/conversations/{id}             # 删除对话
DELETE /applet/v1/chat/conversations/all              # 删除所有对话
POST   /applet/v1/chat/upload_image              # 上传聊天图片
POST   /applet/v1/chat/speech_to_text            # 语音转文字
POST   /applet/v1/trans/tts                      # 文本转语音
GET    /applet/v1/trans/tts/languages            # 获取TTS语言列表
```

### 医生相关接口
```
GET    /applet/v1/doctors                        # 获取医生列表
GET    /applet/v1/doctors/with-interaction       # 获取包含互动状态的医生列表
GET    /applet/v1/doctors/{id}                   # 获取医生详情
POST   /applet/v1/doctors/{id}/like              # 点赞医生
DELETE /applet/v1/doctors/{id}/like              # 取消点赞医生
POST   /applet/v1/doctors/{id}/favorite          # 收藏医生
DELETE /applet/v1/doctors/{id}/favorite          # 取消收藏医生
GET    /applet/v1/doctors/{id}/status            # 获取医生互动状态
GET    /applet/v1/doctors/favorites              # 获取收藏的医生列表
GET    /applet/v1/doctors/likes                  # 获取点赞的医生列表
```

### 产品商城接口（用户端）
```
GET    /applet/v1/products                       # 获取产品列表
GET    /applet/v1/products/categories            # 获取产品分类列表
GET    /applet/v1/products/{id}                  # 获取产品详情
GET    /applet/v1/products/doctor/{doctorId}     # 获取医生的产品
```

### 购物车接口
```
GET    /applet/v1/cart                           # 获取购物车列表
POST   /applet/v1/cart/add                       # 加入购物车
PUT    /applet/v1/cart/{id}                      # 更新购物车商品
DELETE /applet/v1/cart/{id}                      # 删除购物车商品
DELETE /applet/v1/cart/clear                     # 清空购物车
DELETE /applet/v1/cart/batch                     # 批量删除购物车商品
```

### 订单管理接口（用户端）
```
POST   /applet/v1/products/orders                # 创建订单
GET    /applet/v1/products/orders/my             # 获取我的订单
GET    /applet/v1/products/orders/{id}           # 获取订单详情
POST   /applet/v1/products/orders/{id}/cancel    # 取消订单
GET    /applet/v1/products/orders/my/shipped     # 获取已发货订单
GET    /applet/v1/products/orders/{id}/shipping-status  # 获取物流状态
```

### 支付接口
```
POST   /applet/v1/products/orders/{id}/payment        # 创建订单支付
GET    /applet/v1/products/orders/{id}/payment/status # 查询支付状态
POST   /applet/v1/products/orders/{id}/payment/sync   # 同步支付状态
```

### 医生产品管理接口（医生端）
```
POST   /applet/v1/doctor/products                # 创建产品
GET    /applet/v1/doctor/products                # 获取产品列表
GET    /applet/v1/doctor/products/{id}           # 获取产品详情
PUT    /applet/v1/doctor/products/{id}           # 更新产品
DELETE /applet/v1/doctor/products/{id}           # 删除产品
GET    /applet/v1/doctor/products/statistics/overview  # 获取产品统计
GET    /applet/v1/doctor/products/orders/list    # 获取产品订单列表
POST   /applet/v1/doctor/products/upload-image   # 单张图片上传
POST   /applet/v1/doctor/products/upload-images  # 批量图片上传
GET    /applet/v1/doctor/products/orders/pending-shipment  # 获取待发货订单
POST   /applet/v1/doctor/products/orders/{id}/ship         # 订单发货
GET    /applet/v1/doctor/products/orders/{id}/shipping-status  # 获取物流状态
```



## 常用HTTP状态码
- `200` - 成功
- `400` - 请求参数错误
- `401` - 未授权访问
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器内部错误

## 认证方式
```
Authorization: Bearer {token}
```

## 多语言支持
- `zh-CN` - 中文（简体）
- `uy-CN` - 维吾尔语
- `en-US` - 英语

指定方式：
1. 请求头：`Accept-Language: zh-CN`
2. URL参数：`?lang=zh-CN`
3. 请求体：`{"lang": "zh-CN"}`
