// 统一的样式系统入口文件
// 整合了项目中所有的样式定义，提供统一的样式管理

// ==================== 变量导入 ====================
@import './variables/colors.scss';
@import './variables/fonts.scss';
@import './variables/spacing.scss';
@import './variables/layout.scss';

// ==================== 基础样式 ====================
// 全局重置和基础样式
* {
  box-sizing: border-box;
}

page {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-color-regular);
  background-color: var(--bg-color-page);
  
  // 确保页面高度
  height: 100%;
  min-height: 100vh;
}

// ==================== 维吾尔文字体定义 ====================
@font-face {
  font-family: 'uy';
  src: url('data:font/truetype;charset=utf-8;base64,') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

// ==================== 全局字体类 ====================
.ug,
.lang-ug,
.uyghur-font {
  font-family: var(--font-family-uyghur) !important;

  // 确保所有文本元素都应用维吾尔文字体，但排除图标元素
  text:not(.fui-icon):not([class*="icon"]):not([class*="Icon"]),
  view:not(.fui-icon):not([class*="icon"]):not([class*="Icon"]),
  button:not(.fui-icon):not([class*="icon"]):not([class*="Icon"]),
  input, textarea, label,
  .uni-input-input, .uni-textarea-textarea, .uni-button-text {
    font-family: var(--font-family-uyghur) !important;
  }

  // placeholder文本
  input::placeholder,
  textarea::placeholder,
  .uni-input-placeholder,
  .uni-textarea-placeholder {
    font-family: var(--font-family-uyghur) !important;
  }
}

// ==================== 图标字体保护 ====================
// 确保图标字体不被维吾尔文字体覆盖
.fui-icon,
[class*="icon"],
[class*="Icon"],
.icon,
.iconfont {
  font-family: fuiFont, "iconfont", "Material Icons", "Font Awesome", sans-serif !important;
}

// FirstUI图标特殊保护
.fui-icon {
  font-family: fuiFont !important;
  font-style: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  line-height: 1 !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

// ==================== 主题切换 ====================
.dark-theme {
  --bg-color-page: #{$dark-bg-color};
  --bg-color-content: #{$dark-bg-color-secondary};
  --text-color-primary: #{$dark-text-color};
  --text-color-regular: #{$dark-text-color-secondary};
  --border-color-base: #{$dark-border-color};
  
  // 卡片在暗色主题下的样式
  .card {
    background-color: var(--bg-color-content);
    border-color: var(--border-color-base);
  }
}

// ==================== RTL布局支持 ====================
.rtl {
  direction: rtl;
  
  // RTL下的文本对齐
  .text-left { text-align: right; }
  .text-right { text-align: left; }
  
  // RTL下的边距调整
  .ml-auto { margin-left: 0; margin-right: auto; }
  .mr-auto { margin-right: 0; margin-left: auto; }
  
  // RTL下的浮动调整
  .float-left { float: right; }
  .float-right { float: left; }
}

// ==================== 公共组件样式 ====================
// 卡片组件
.card {
  background-color: var(--bg-color-white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-base);
  padding: var(--card-padding);
  margin-bottom: var(--card-margin);
  border: var(--border-width-thin) solid var(--border-color-light);
  
  &.card-hover {
    transition: var(--transition-all);
    
    &:hover {
      box-shadow: var(--box-shadow-md);
      transform: translateY(-2rpx);
    }
  }
}

// 按钮组件基础样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  text-align: center;
  transition: var(--transition-all);
  cursor: pointer;
  border: var(--border-width-thin) solid transparent;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  // 按钮尺寸
  &.btn-sm {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
  }
  
  &.btn-md {
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-base);
  }
  
  &.btn-lg {
    padding: var(--spacing-4) var(--spacing-6);
    font-size: var(--font-size-lg);
  }
  
  // 按钮类型
  &.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-color-inverse);
    
    &:hover {
      background-color: var(--primary-dark);
    }
  }
  
  &.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
    
    &:hover {
      background-color: var(--primary-color);
      color: var(--text-color-inverse);
    }
  }
}

// 输入框组件基础样式
.input {
  width: 100%;
  padding: var(--spacing-3);
  border: var(--border-width-thin) solid var(--border-color-base);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-color-regular);
  background-color: var(--bg-color-white);
  transition: var(--transition-all);
  
  &::placeholder {
    color: var(--text-color-placeholder);
  }
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3rpx rgba(70, 92, 255, 0.1);
  }
  
  &:disabled {
    background-color: var(--bg-color-grey);
    color: var(--text-color-disabled);
    cursor: not-allowed;
  }
}

// 模态框基础样式
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-index-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  
  &-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-color-mask);
    z-index: var(--z-index-modal-backdrop);
  }
  
  &-content {
    position: relative;
    background-color: var(--bg-color-white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--box-shadow-xl);
    padding: var(--spacing-6);
    margin: var(--spacing-4);
    max-width: 90%;
    max-height: 90%;
    overflow: auto;
    z-index: var(--z-index-modal);
  }
}

// ==================== 页面布局样式 ====================
.page {
  min-height: 100vh;
  background-color: var(--bg-color-page);
  
  &-content {
    padding: var(--page-padding-vertical) var(--page-padding-horizontal);
    min-height: var(--page-content-min-height);
  }
}

// 导航栏样式
.navbar {
  height: var(--navbar-height);
  background-color: var(--bg-color-white);
  border-bottom: var(--border-width-thin) solid var(--border-color-light);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-fixed);
}

// 标签栏样式
.tabbar {
  height: var(--tabbar-height);
  background-color: var(--bg-color-white);
  border-top: var(--border-width-thin) solid var(--border-color-light);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-fixed);
}

// ==================== 响应式工具类 ====================
// 文本对齐
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

// 文本颜色
.text-primary { color: var(--text-color-primary); }
.text-regular { color: var(--text-color-regular); }
.text-secondary { color: var(--text-color-secondary); }
.text-placeholder { color: var(--text-color-placeholder); }
.text-disabled { color: var(--text-color-disabled); }
.text-inverse { color: var(--text-color-inverse); }

// 背景颜色
.bg-white { background-color: var(--bg-color-white); }
.bg-page { background-color: var(--bg-color-page); }
.bg-content { background-color: var(--bg-color-content); }
.bg-grey { background-color: var(--bg-color-grey); }
.bg-primary { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }

// 边框
.border { border: var(--border-width-thin) solid var(--border-color-base); }
.border-t { border-top: var(--border-width-thin) solid var(--border-color-base); }
.border-b { border-bottom: var(--border-width-thin) solid var(--border-color-base); }
.border-l { border-left: var(--border-width-thin) solid var(--border-color-base); }
.border-r { border-right: var(--border-width-thin) solid var(--border-color-base); }
.border-none { border: none; }

// ==================== 动画类 ====================
.fade-in {
  animation: fadeIn var(--transition-duration-base) ease-in-out;
}

.fade-out {
  animation: fadeOut var(--transition-duration-base) ease-in-out;
}

.slide-up {
  animation: slideUp var(--transition-duration-base) ease-out;
}

.slide-down {
  animation: slideDown var(--transition-duration-base) ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@keyframes slideDown {
  from { transform: translateY(-100%); }
  to { transform: translateY(0); }
}
