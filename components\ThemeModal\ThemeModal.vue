<template>
	<view class="theme-modal-overlay" v-if="visible" @tap="handleOverlayTap">
		<view class="theme-modal" @tap.stop>
			<view class="modal-header">
				<text class="modal-title ug">{{ $t('common.themeSwitch') }}</text>
			</view>

			<view class="modal-content">
				<text class="modal-desc ug">{{ $t('common.confirmThemeSwitch') }}</text>
			</view>

			<view class="modal-actions">
				<view
					v-for="button in modalButtons"
					:key="button.key"
					class="action-btn"
					:class="button.btnClass"
					@tap="button.action"
				>
					<text class="btn-text" :class="[button.textClass, 'ug']">{{ $t(button.textKey) }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, defineEmits, defineProps } from 'vue'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()

const props = defineProps({
	visible: {
		type: Boolean,
		default: false
	}
})

const emit = defineEmits(['close', 'confirm', 'cancel'])

// 处理遮罩层点击
const handleOverlayTap = () => {
	emit('close')
}

// 处理取消按钮
const handleCancel = () => {
	emit('cancel')
	emit('close')
}

// 处理确认按钮
const handleConfirm = () => {
	emit('confirm')
	emit('close')
}

// 弹窗按钮配置数组
const modalButtons = [
	{
		key: 'cancel',
		btnClass: 'cancel-btn',
		textClass: 'cancel-text',
		textKey: 'common.cancel',
		action: handleCancel
	},
	{
		key: 'confirm',
		btnClass: 'confirm-btn',
		textClass: 'confirm-text',
		textKey: 'common.confirm',
		action: handleConfirm
	}
]
</script>

<style scoped>
.theme-modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.theme-modal {
	width: 600rpx;
	background-color: var(--fui-bg-color, #ffffff);
	border-radius: 24rpx;
	overflow: hidden;
	margin: 0 40rpx;
}

.modal-header {
	padding: 48rpx 40rpx 24rpx;
	text-align: center;
}

.modal-title {
	font-size: 36rpx;
	font-weight: 600;
	color: var(--fui-color-title, #181818);
}

.modal-content {
	padding: 0 40rpx 48rpx;
}

.modal-desc {
	font-size: 28rpx;
	color: var(--fui-color-section, #333333);
	line-height: 1.6;
	text-align: center;
}

.modal-actions {
	display: flex;
	border-top: 1px solid var(--fui-color-border, #EEEEEE);
}

.action-btn {
	flex: 1;
	height: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}

.action-btn:active {
	background-color: var(--fui-bg-color-hover, rgba(0, 0, 0, 0.05));
}

.cancel-btn {
	border-right: 1px solid var(--fui-color-border, #EEEEEE);
}

.btn-text {
	font-size: 32rpx;
	font-weight: 500;
}

.cancel-text {
	color: var(--fui-color-subtitle, #7F7F7F);
}

.confirm-text {
	color: #4CAF50;
}

/* 暗色主题适配 */
.dark-theme .theme-modal {
	background-color: var(--fui-bg-color-content, #333333);
}

.dark-theme .modal-title {
	color: var(--fui-color-title, #ffffff);
}

.dark-theme .modal-desc {
	color: var(--fui-color-section, #e0e0e0);
}

.dark-theme .modal-actions {
	border-top-color: var(--fui-color-border, #404040);
}

.dark-theme .cancel-btn {
	border-right-color: var(--fui-color-border, #404040);
}

.dark-theme .cancel-text {
	color: var(--fui-color-subtitle, #b0b0b0);
}
</style>
