<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">头像URL修复测试</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">测试用例：</text>
      
      <view class="test-case" v-for="(testCase, index) in testCases" :key="index">
        <view class="test-info">
          <text class="test-label">{{ testCase.label }}</text>
          <text class="test-original">原始URL: {{ testCase.original }}</text>
          <text class="test-processed">处理后: {{ testCase.processed }}</text>
        </view>
        
        <view class="test-avatar">
          <image 
            :src="testCase.processed" 
            class="avatar-image" 
            mode="aspectFill"
            @error="handleImageError(testCase, $event)"
            @load="handleImageLoad(testCase, $event)"
          />
        </view>
      </view>
    </view>
    
    <view class="test-results">
      <text class="results-title">测试结果：</text>
      <view v-for="(result, index) in testResults" :key="index" class="result-item">
        <text :class="['result-text', result.success ? 'success' : 'error']">
          {{ result.message }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { processAvatarUrl } from '@/request/avatar.js'

const testCases = ref([
  {
    label: '错误域名测试',
    original: 'https://127.0.0.1:8002/static/uploads/doctor_avatar/example.jpg',
    processed: ''
  },
  {
    label: '相对路径测试',
    original: '/file/user_avatar/example.jpg',
    processed: ''
  },
  {
    label: '文件名测试',
    original: 'example.jpg',
    processed: ''
  },
  {
    label: '正确域名测试',
    original: 'https://appdava.sulmas.com.cn/file/user_avatar/example.jpg',
    processed: ''
  },
  {
    label: '空值测试',
    original: '',
    processed: ''
  }
])

const testResults = ref([])

const handleImageError = (testCase, event) => {
  console.log('图片加载失败:', testCase.label, event)
  testResults.value.push({
    success: false,
    message: `${testCase.label}: 图片加载失败`
  })
}

const handleImageLoad = (testCase, event) => {
  console.log('图片加载成功:', testCase.label, event)
  testResults.value.push({
    success: true,
    message: `${testCase.label}: 图片加载成功`
  })
}

onMounted(() => {
  // 处理所有测试用例
  testCases.value.forEach(testCase => {
    testCase.processed = processAvatarUrl(testCase.original)
    console.log('测试用例处理:', testCase.label, testCase.original, '->', testCase.processed)
  })
})
</script>

<style scoped>
.test-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.test-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.test-case {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 1px solid #eee;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.test-info {
  flex: 1;
  margin-right: 20rpx;
}

.test-label {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.test-original, .test-processed {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
  word-break: break-all;
}

.test-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  overflow: hidden;
  border: 2px solid #ddd;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.test-results {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
}

.results-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.result-item {
  margin-bottom: 10rpx;
}

.result-text {
  font-size: 26rpx;
}

.success {
  color: #4CAF50;
}

.error {
  color: #F44336;
}
</style>
