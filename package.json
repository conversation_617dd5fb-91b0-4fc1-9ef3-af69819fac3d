{"name": "health-app", "version": "1.0.0", "description": "健康助手应用", "main": "main.js", "scripts": {"dev:mp-weixin": "uni build --watch --platform mp-weixin", "build:mp-weixin": "uni build --platform mp-weixin", "dev:h5": "uni serve", "build:h5": "uni build --platform h5", "git:push": "git add . && git commit -m \"更新代码\" && git push", "git:auto": "node simple-git.js", "git:status": "git status", "git:log": "git log --oneline -10"}, "dependencies": {"@dcloudio/uni-cli-shared": "^2.0.2-4070520250711001", "@dcloudio/uni-mp-weixin": "^2.0.2-4070520250711001", "firstui-uni": "^2.4.0"}, "devDependencies": {"nodemon": "^3.1.10"}}