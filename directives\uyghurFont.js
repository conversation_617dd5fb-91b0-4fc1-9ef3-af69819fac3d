// 维吾尔文字体指令
import { useAppStore } from '@/store/app.js'

// 应用维吾尔文字体到元素（小程序兼容版本）
function applyUyghurFont(el, binding, vnode) {
  try {
    const appStore = useAppStore()

    if (appStore.isUyghur) {
      // 在小程序中主要通过添加类名来控制字体
      if (el.classList) {
        el.classList.add('ug')
        el.classList.add('lang-ug')
      }

      // 设置内联样式作为备用
      if (el.style) {
        el.style.fontFamily = 'uy'
      }
    } else {
      // 移除维吾尔文字体类
      if (el.classList) {
        el.classList.remove('ug')
        el.classList.remove('lang-ug')
      }

      // 清除内联样式
      if (el.style) {
        el.style.fontFamily = ''
      }
    }
  } catch (error) {
    console.warn('应用维吾尔文字体指令失败:', error)
  }
}

// 维吾尔文字体指令定义
export const uyghurFont = {
  // 元素插入时
  mounted(el, binding, vnode) {
    applyUyghurFont(el, binding, vnode)
    
    // 监听语言变化
    const handleLanguageChange = () => {
      applyUyghurFont(el, binding, vnode)
    }
    
    // 保存监听器引用，用于清理
    el._languageChangeHandler = handleLanguageChange
    uni.$on('languageFontChanged', handleLanguageChange)
  },
  
  // 元素更新时
  updated(el, binding, vnode) {
    applyUyghurFont(el, binding, vnode)
  },
  
  // 元素卸载时
  unmounted(el) {
    // 清理事件监听
    if (el._languageChangeHandler) {
      uni.$off('languageFontChanged', el._languageChangeHandler)
      delete el._languageChangeHandler
    }
  }
}

// 自动维吾尔文字体指令 - 自动检测并应用
export const autoUyghurFont = {
  mounted(el, binding, vnode) {
    // 自动应用字体
    applyUyghurFont(el, binding, vnode)
    
    // 监听语言变化并自动更新
    const handleLanguageChange = () => {
      applyUyghurFont(el, binding, vnode)
    }
    
    el._autoLanguageChangeHandler = handleLanguageChange
    uni.$on('languageFontChanged', handleLanguageChange)
  },
  
  updated(el, binding, vnode) {
    applyUyghurFont(el, binding, vnode)
  },
  
  unmounted(el) {
    if (el._autoLanguageChangeHandler) {
      uni.$off('languageFontChanged', el._autoLanguageChangeHandler)
      delete el._autoLanguageChangeHandler
    }
  }
}

// 条件维吾尔文字体指令 - 根据条件应用
export const conditionalUyghurFont = {
  mounted(el, binding, vnode) {
    const condition = binding.value
    if (condition) {
      applyUyghurFont(el, binding, vnode)
    }
    
    const handleLanguageChange = () => {
      if (binding.value) {
        applyUyghurFont(el, binding, vnode)
      }
    }
    
    el._conditionalLanguageChangeHandler = handleLanguageChange
    uni.$on('languageFontChanged', handleLanguageChange)
  },
  
  updated(el, binding, vnode) {
    const condition = binding.value
    if (condition) {
      applyUyghurFont(el, binding, vnode)
    }
  },
  
  unmounted(el) {
    if (el._conditionalLanguageChangeHandler) {
      uni.$off('languageFontChanged', el._conditionalLanguageChangeHandler)
      delete el._conditionalLanguageChangeHandler
    }
  }
}
