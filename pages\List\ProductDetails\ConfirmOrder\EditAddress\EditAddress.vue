<template>
	<view class="add-address-page" :class="[fontSizeClass, fontClass]" :style="pageStyle" :key="`${fontSizeUpdateKey}-${i18nUpdateKey}`">
		<!-- 页面内容区域 -->
		<view class="content">
			<!-- 表单项容器 -->
			<view class="form-item">
				<!-- 表单字段循环 -->
				<view
					v-for="field in formFields"
					:key="field.key"
					class="input-group"
				>
					<view class="label ug">{{ $t(field.labelKey) }}</view>

					<!-- 普通输入框 -->
					<view v-if="field.type === 'input'" class="input-wrapper">
						<fui-icon :name="field.iconName" :size="60" color="#999"></fui-icon>
						<input
							class="input ug"
							:type="field.inputType"
							:placeholder="$t(field.placeholderKey)"
							:value="field.value"
							@input="field.onInput"
						/>
					</view>

					<!-- 地区选择器 -->
					<picker v-else-if="field.type === 'region'" mode="region" @change="onRegionChange" :value="regionValue">
						<view class="input-wrapper">
							<fui-icon :name="field.iconName" :size="60" color="#999"></fui-icon>
							<view class="region-text ug" :class="{ 'placeholder': regionText === '请选择所在地区' }">{{ regionText }}</view>
							<view class="region-icons">
								<fui-icon name="right" :size="32" color="#999"></fui-icon>
							</view>
						</view>
					</picker>
				</view>

				<!-- 邮政编码 -->
				<view class="input-group">
					<view class="label">邮政编码（可选）</view>
					<view class="input-wrapper special-input">
						<fui-icon name="mail" :size="60" color="#999"></fui-icon>
						<input class="input" type="text" placeholder="请输入邮政编码" v-model="formData.zipCode" />
					</view>
				</view>

				<!-- 地址标签 -->
				<view class="input-group">
					<view class="label">地址标签（可选）</view>
					<view class="input-wrapper">
						<fui-icon name="classify" :size="60" color="#999"></fui-icon>
						<input class="input" type="text" placeholder="如：家、公司、学校等" v-model="formData.tag" />
					</view>
				</view>

				<!-- 设为默认地址 -->
				<view class="input-group switch-item" style="margin-bottom: 200rpx;">
					<view class="label">设为默认地址</view>
					<switch class="switch" :checked="formData.isDefault" @change="onSwitchChange" color="#4CAF50" />
				</view>
			</view>
		</view>

		<!-- 固定底部按钮 -->
		<view class="bottom-btn">
			<button class="save-btn" @click="saveAddress">保存修改</button>
		</view>


	</view>
</template>

<script setup>
	import {
		ref,
		onMounted
	} from 'vue'
	import { useFontSizePage } from '@/utils/fontSizeMixin.js'

	// 使用字体大小功能
	const { fontSizeClass, fontSizeUpdateKey } = useFontSizePage()

	// 表单数据
	const formData = ref({
		name: '',
		phone: '',
		province: '',
		city: '',
		district: '',
		address: '',
		zipCode: '',
		tag: '',
		isDefault: false
	})

	// 地区选择相关
	const regionText = ref('请选择所在地区')
	const regionValue = ref(['', '', '']) // uniapp picker的值

	// 编辑相关数据
	const editIndex = ref(-1) // 编辑的地址索引
	const originalAddress = ref(null) // 原始地址数据

	// 页面加载时获取传递的地址数据
	onMounted(() => {
		const pages = getCurrentPages()
		const currentPage = pages[pages.length - 1]
		const options = currentPage.options

		console.log('EditAddress页面参数:', options)

		if (options.addressData && options.index !== undefined) {
			try {
				// 解析地址数据
				const addressData = JSON.parse(decodeURIComponent(options.addressData))
				editIndex.value = parseInt(options.index)
				originalAddress.value = addressData

				console.log('解析的地址数据:', addressData)
				console.log('编辑索引:', editIndex.value)

				// 填充表单数据
				formData.value = {
					name: addressData.name || '',
					phone: addressData.phone || '',
					province: addressData.province || '',
					city: addressData.city || '',
					district: addressData.district || '',
					address: addressData.address || '',
					zipCode: addressData.zipCode || '',
					tag: addressData.tag || '',
					isDefault: addressData.isDefault || false
				}

				// 设置地区显示
				if (addressData.province && addressData.city && addressData.district) {
					regionText.value = `${addressData.province} ${addressData.city} ${addressData.district}`
					regionValue.value = [addressData.province, addressData.city, addressData.district]
				}

			} catch (error) {
				console.error('解析地址数据失败:', error)
				uni.showToast({
					title: '数据解析失败',
					icon: 'none'
				})
			}
		}
	})

	// 表单字段配置数组
	const formFields = computed(() => [
		{
			key: 'name',
			type: 'input',
			labelKey: 'editAddress.receiverName',
			placeholderKey: 'editAddress.namePlaceholder',
			iconName: 'addressbook',
			inputType: 'text',
			value: formData.value.name,
			onInput: (e) => { formData.value.name = e.detail.value }
		},
		{
			key: 'phone',
			type: 'input',
			labelKey: 'editAddress.contactPhone',
			placeholderKey: 'editAddress.phonePlaceholder',
			iconName: 'telephone',
			inputType: 'text',
			value: formData.value.phone,
			onInput: (e) => { formData.value.phone = e.detail.value }
		},
		{
			key: 'region',
			type: 'region',
			labelKey: 'editAddress.region',
			iconName: 'location'
		},
		{
			key: 'address',
			type: 'input',
			labelKey: 'editAddress.detailAddress',
			placeholderKey: 'editAddress.addressPlaceholder',
			iconName: 'location-fill',
			inputType: 'text',
			value: formData.value.address,
			onInput: (e) => { formData.value.address = e.detail.value }
		}
	])

	// uniapp地区选择处理
	const onRegionChange = (e) => {
		console.log('地区选择变化:', e)
		const {
			value
		} = e.detail

		// 更新选择的值
		regionValue.value = value

		// 更新显示文本
		if (value && value.length === 3 && value[0] && value[1] && value[2]) {
			regionText.value = `${value[0]} ${value[1]} ${value[2]}`

			// 更新表单数据
			formData.value.province = value[0]
			formData.value.city = value[1]
			formData.value.district = value[2]

			console.log('地区选择完成:', {
				province: value[0],
				city: value[1],
				district: value[2]
			})
		} else {
			regionText.value = '请选择所在地区'
		}
	}



	// 开关切换
	const onSwitchChange = (e) => {
		formData.value.isDefault = e.detail.value
	}

	// 保存地址（编辑模式）
	const saveAddress = () => {
		// 验证表单
		if (!formData.value.name.trim()) {
			uni.showToast({
				title: '请输入收货人姓名',
				icon: 'none'
			})
			return
		}

		if (!formData.value.phone.trim()) {
			uni.showToast({
				title: '请输入联系电话',
				icon: 'none'
			})
			return
		}

		if (regionText.value === '请选择所在地区' || !formData.value.province) {
			uni.showToast({
				title: '请选择所在地区',
				icon: 'none'
			})
			return
		}

		if (!formData.value.address.trim()) {
			uni.showToast({
				title: '请输入详细地址',
				icon: 'none'
			})
			return
		}

		// 构建完整的地址信息
		const addressInfo = {
			id: originalAddress.value?.id || Date.now(), // 保持原有ID或生成新ID
			name: formData.value.name,
			phone: formData.value.phone,
			province: formData.value.province,
			city: formData.value.city,
			district: formData.value.district,
			address: formData.value.address,
			zipCode: formData.value.zipCode,
			tag: formData.value.tag,
			isDefault: formData.value.isDefault,
			region: `${formData.value.province} ${formData.value.city} ${formData.value.district}`,
			fullAddress: `${formData.value.province} ${formData.value.city} ${formData.value.district} ${formData.value.address}`
		}

		// 获取现有地址列表
		const existingAddresses = uni.getStorageSync('addressList') || []

		// 如果设置为默认地址，将其他地址的默认状态取消
		if (addressInfo.isDefault) {
			existingAddresses.forEach((addr, index) => {
				if (index !== editIndex.value) {
					addr.isDefault = false
				}
			})
		}

		// 更新指定索引的地址（编辑模式）
		if (editIndex.value >= 0 && editIndex.value < existingAddresses.length) {
			existingAddresses[editIndex.value] = addressInfo
			console.log('更新地址成功', addressInfo)
		} else {
			// 如果索引无效，则添加为新地址
			existingAddresses.push(addressInfo)
			console.log('添加新地址', addressInfo)
		}

		// 保存到本地存储
		uni.setStorageSync('addressList', existingAddresses)

		uni.showToast({
			title: '修改成功',
			icon: 'success'
		})

		// 跳转回选择地址页面
		setTimeout(() => {
			uni.redirectTo({
				url: '/pages/List/ProductDetails/ConfirmOrder/SelectAddress/SelectAddress'
			})
		}, 1500)
	}
</script>

<style scoped>
	.add-address-page {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx;
	}

	.content {
		padding: 0 32rpx;
		padding-top: 32rpx;
	}

	.form-item {
		background-color: #fff;
		margin-bottom: 24rpx;
		border-radius: 20rpx;
		padding: 0;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
	}

	.input-group {
		padding: 36rpx 32rpx 32rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.input-group:last-child {
		border-bottom: none;
	}

	.label {
		font-size: 30rpx;
		color: #666;
		font-weight: 400;
		margin-bottom: 24rpx;
	}

	.input-wrapper {
		display: flex;
		align-items: center;
		background-color: #f8f9fa;
		border-radius: 12rpx;
		padding: 28rpx 24rpx;
		min-height: 96rpx;
		border: 1rpx solid #e9ecef;
		transition: all 0.3s ease;
	}

	.input-wrapper:focus-within {
		border-color: #4CAF50;
		background-color: #fff;
		box-shadow: 0 0 0 2rpx rgba(76, 175, 80, 0.1);
	}

	.input-wrapper .fui-icon {
		margin-right: 20rpx;
		flex-shrink: 0;
		color: #999;
	}

	.input {
		flex: 1;
		font-size: 34rpx;
		color: #333;
		background: transparent;
		border: none;
		outline: none;
		padding: 0;
		line-height: 1.4;
	}

	.input::placeholder {
		color: #bbb;
		font-size: 34rpx;
	}

	/* 特殊输入框样式（邮政编码） */
	.postal-code-wrapper {
		border: 2rpx solid #4CAF50 !important;
		background-color: #f0f9f0 !important;
	}

	.postal-code-wrapper .input {
		color: #4CAF50 !important;
		font-weight: 500;
	}

	.postal-code-wrapper .input::placeholder {
		color: rgba(76, 175, 80, 0.6) !important;
	}

	/* 地区选择项 */
	.region-item {
		cursor: pointer;
	}

	.region-text {
		flex: 1;
		font-size: 34rpx;
		color: #333;
		margin-left: 20rpx;
		font-weight: 400;
	}

	.region-text.placeholder {
		color: #bbb;
		font-size: 34rpx;
	}

	.region-icons {
		display: flex;
		align-items: center;
		gap: 12rpx;
		position: relative;
	}

	/* 地区选择绿色圆点 */
	.region-icons::before {
		content: '';
		position: absolute;
		right: 40rpx;
		top: 50%;
		transform: translateY(-50%);
		width: 12rpx;
		height: 12rpx;
		border-radius: 50%;
		background-color: #4CAF50;
	}

	/* 开关项 */
	.switch-item {
		display: flex;
		align-items: center;
		justify-content: space-between;

	}

	.switch-item .label {
		margin-bottom: 0;
		flex: 1;
		font-size: 32rpx;
		color: #333;
		font-weight: 400;
	}

	.switch {
		transform: scale(1.2);
	}

	/* 固定底部按钮 */
	.bottom-btn {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 32rpx;
		background-color: #fff;
		z-index: 999;
	}

	.save-btn {
		width: 100%;
		height: 96rpx;
		background: #4CAF50;
		color: #fff;
		font-size: 34rpx;
		font-weight: 500;
		border: none;
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 16rpx rgba(76, 175, 80, 0.3);
		transition: all 0.3s ease;
	}

	.save-btn:active {
		background: #45a049;
		transform: translateY(2rpx);
		box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.4);
	}

	/* 页面安全区域适配 */
	.add-address-page {
		padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
	}

	.bottom-btn {
		padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
	}





	/* 页面整体优化 */
	page {
		background-color: #f5f5f5;
	}

	/* 输入框聚焦状态优化 */
	.input-wrapper:focus-within .fui-icon {
		color: #4CAF50;
	}

	/* 地区选择项特殊处理 */
	.region-item .input-wrapper:focus-within::before {
		background-color: #4CAF50;
		box-shadow: 0 0 0 4rpx rgba(76, 175, 80, 0.2);
	}
</style>