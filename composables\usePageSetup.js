// 统一的页面设置 Composition API
// 整合了所有页面共同的逻辑，包括字体管理、国际化、主题等

import { computed, ref, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useAppStore } from '@/store/app'

/**
 * 页面基础设置 Hook
 * 提供统一的页面配置和状态管理
 */
export function usePageSetup(options = {}) {
  const {
    enableRTL = true,           // 是否启用RTL布局
    enableTheme = true,         // 是否启用主题切换
    enableFontSize = true,      // 是否启用字体大小调整
    pageClass = '',             // 页面自定义类名
    autoUpdateI18n = true       // 是否自动更新国际化
  } = options

  // Store和国际化
  const appStore = useAppStore()
  const { t: $t, locale } = useI18n()
  
  // 响应式状态
  const i18nUpdateKey = ref(0)
  const isPageReady = ref(false)

  // ==================== 字体类计算 ====================
  const fontClass = computed(() => {
    const classes = {
      // 基础页面类
      [pageClass]: !!pageClass,
      
      // 语言相关类
      'ug': appStore.isUyghur,
      [`lang-${appStore.lang}`]: true,
      
      // RTL布局类
      'rtl': enableRTL && appStore.isRTL,
      
      // 主题类
      'dark-theme': enableTheme && appStore.isDarkTheme,
      
      // 字体大小类
      [`font-size-${appStore.fontSize}`]: enableFontSize && appStore.fontSize !== 'normal'
    }
    
    return classes
  })

  // ==================== 页面样式计算 ====================
  const pageStyle = computed(() => {
    const style = {}
    
    // 主题相关样式
    if (enableTheme && appStore.isDarkTheme) {
      style['--bg-color-page'] = '#1a1a1a'
      style['--text-color-primary'] = '#ffffff'
    }
    
    // 字体大小相关样式
    if (enableFontSize) {
      const fontSizeMap = {
        small: '0.9',
        normal: '1',
        large: '1.1',
        'extra-large': '1.2'
      }
      const scale = fontSizeMap[appStore.fontSize] || '1'
      style['--font-scale'] = scale
    }
    
    return style
  })

  // ==================== 导航栏配置 ====================
  const navbarConfig = computed(() => ({
    backgroundColor: appStore.isDarkTheme ? '#2d2d2d' : '#ffffff',
    color: appStore.isDarkTheme ? '#ffffff' : '#333333',
    borderColor: appStore.isDarkTheme ? '#404040' : '#e4e7ed'
  }))

  // ==================== 国际化更新 ====================
  const updateI18n = () => {
    i18nUpdateKey.value++
  }

  // 监听语言变化
  if (autoUpdateI18n) {
    watch(() => appStore.lang, () => {
      locale.value = appStore.lang
      updateI18n()
    }, { immediate: true })
  }

  // ==================== 页面生命周期 ====================
  onMounted(() => {
    // 页面加载完成
    isPageReady.value = true
    
    // 设置页面标题（如果提供）
    if (options.title) {
      uni.setNavigationBarTitle({
        title: typeof options.title === 'function' ? options.title() : options.title
      })
    }
  })

  // ==================== 工具方法 ====================
  
  /**
   * 获取翻译文本
   * @param {string} key 翻译键
   * @param {object} params 参数
   * @returns {string} 翻译后的文本
   */
  const getTranslation = (key, params = {}) => {
    return $t(key, params)
  }

  /**
   * 切换主题
   */
  const toggleTheme = () => {
    if (enableTheme) {
      appStore.toggleTheme()
    }
  }

  /**
   * 切换语言
   * @param {string} lang 语言代码
   */
  const switchLanguage = (lang) => {
    appStore.setLanguage(lang)
  }

  /**
   * 调整字体大小
   * @param {string} size 字体大小
   */
  const setFontSize = (size) => {
    if (enableFontSize) {
      appStore.setFontSize(size)
    }
  }

  /**
   * 显示提示信息
   * @param {string} message 提示内容
   * @param {string} type 提示类型
   */
  const showToast = (message, type = 'success') => {
    const iconMap = {
      success: 'success',
      error: 'error',
      warning: 'none',
      info: 'none'
    }
    
    uni.showToast({
      title: message,
      icon: iconMap[type] || 'none',
      duration: 2000
    })
  }

  /**
   * 显示加载中
   * @param {string} title 加载文本
   */
  const showLoading = (title = '加载中...') => {
    uni.showLoading({
      title: getTranslation(title)
    })
  }

  /**
   * 隐藏加载中
   */
  const hideLoading = () => {
    uni.hideLoading()
  }

  /**
   * 页面跳转
   * @param {string} url 跳转地址
   * @param {object} options 跳转选项
   */
  const navigateTo = (url, options = {}) => {
    const { type = 'navigateTo', ...params } = options
    
    const navigateMap = {
      navigateTo: uni.navigateTo,
      redirectTo: uni.redirectTo,
      reLaunch: uni.reLaunch,
      switchTab: uni.switchTab
    }
    
    const navigate = navigateMap[type] || uni.navigateTo
    navigate({
      url,
      ...params
    })
  }

  // ==================== 返回值 ====================
  return {
    // 响应式状态
    fontClass,
    pageStyle,
    navbarConfig,
    i18nUpdateKey,
    isPageReady,
    
    // Store引用
    appStore,
    
    // 工具方法
    getTranslation,
    $t: getTranslation,
    updateI18n,
    toggleTheme,
    switchLanguage,
    setFontSize,
    showToast,
    showLoading,
    hideLoading,
    navigateTo
  }
}

/**
 * 表单页面设置 Hook
 * 在基础页面设置基础上，添加表单相关功能
 */
export function useFormPageSetup(options = {}) {
  const baseSetup = usePageSetup(options)
  
  // 表单状态
  const formLoading = ref(false)
  const formErrors = ref({})
  
  /**
   * 设置表单加载状态
   * @param {boolean} loading 是否加载中
   */
  const setFormLoading = (loading) => {
    formLoading.value = loading
    if (loading) {
      baseSetup.showLoading('common.submitting')
    } else {
      baseSetup.hideLoading()
    }
  }

  /**
   * 设置表单错误
   * @param {object} errors 错误对象
   */
  const setFormErrors = (errors) => {
    formErrors.value = errors
  }

  /**
   * 清除表单错误
   */
  const clearFormErrors = () => {
    formErrors.value = {}
  }

  /**
   * 获取字段错误信息
   * @param {string} field 字段名
   * @returns {string} 错误信息
   */
  const getFieldError = (field) => {
    return formErrors.value[field] || ''
  }

  /**
   * 验证表单
   * @param {object} formData 表单数据
   * @param {object} rules 验证规则
   * @returns {boolean} 是否验证通过
   */
  const validateForm = (formData, rules) => {
    const errors = {}
    let isValid = true

    Object.keys(rules).forEach(field => {
      const rule = rules[field]
      const value = formData[field]

      if (rule.required && (!value || value.toString().trim() === '')) {
        errors[field] = baseSetup.$t(rule.message || 'common.fieldRequired')
        isValid = false
      }
    })

    setFormErrors(errors)
    return isValid
  }

  return {
    ...baseSetup,
    
    // 表单状态
    formLoading,
    formErrors,
    
    // 表单方法
    setFormLoading,
    setFormErrors,
    clearFormErrors,
    getFieldError,
    validateForm
  }
}

/**
 * 列表页面设置 Hook
 * 在基础页面设置基础上，添加列表相关功能
 */
export function useListPageSetup(options = {}) {
  const baseSetup = usePageSetup(options)
  
  // 列表状态
  const listLoading = ref(false)
  const listData = ref([])
  const hasMore = ref(true)
  const currentPage = ref(1)
  
  /**
   * 设置列表加载状态
   * @param {boolean} loading 是否加载中
   */
  const setListLoading = (loading) => {
    listLoading.value = loading
  }

  /**
   * 设置列表数据
   * @param {array} data 列表数据
   * @param {boolean} append 是否追加数据
   */
  const setListData = (data, append = false) => {
    if (append) {
      listData.value = [...listData.value, ...data]
    } else {
      listData.value = data
    }
  }

  /**
   * 刷新列表
   */
  const refreshList = async (loadFunction) => {
    currentPage.value = 1
    hasMore.value = true
    setListLoading(true)
    
    try {
      const data = await loadFunction(1)
      setListData(data, false)
    } catch (error) {
      baseSetup.showToast('common.loadFailed', 'error')
    } finally {
      setListLoading(false)
    }
  }

  /**
   * 加载更多
   */
  const loadMore = async (loadFunction) => {
    if (!hasMore.value || listLoading.value) return
    
    setListLoading(true)
    currentPage.value++
    
    try {
      const data = await loadFunction(currentPage.value)
      if (data.length === 0) {
        hasMore.value = false
      } else {
        setListData(data, true)
      }
    } catch (error) {
      currentPage.value--
      baseSetup.showToast('common.loadFailed', 'error')
    } finally {
      setListLoading(false)
    }
  }

  return {
    ...baseSetup,
    
    // 列表状态
    listLoading,
    listData,
    hasMore,
    currentPage,
    
    // 列表方法
    setListLoading,
    setListData,
    refreshList,
    loadMore
  }
}
