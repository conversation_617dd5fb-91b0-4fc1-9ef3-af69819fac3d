<template>
	<!--
		📋 列表页面模板
		这是医生列表和产品列表的展示页面
		包含：搜索功能、标签切换、医生列表、产品列表
	-->

	<!--
		📄 页面容器
		- class="page-container": 基础页面样式
		- :class: 动态样式类，包括字体大小和字体类型
		- :style: 动态样式，主要用于RTL布局
		- :key: 强制重新渲染的键，当字体或语言变化时重新渲染
	-->
	<view
		class="page-container"
		:class="[fontSizeClass, fontClass]"
		:style="pageStyle"
		:key="`${fontSizeUpdateKey}-${i18nUpdateKey}`"
	>
		<!--
			📱 顶部导航栏
			:title: 动态标题，从语言包获取
			background: 背景颜色设为白色
		-->
		<CustomNavbar :title="$t('nav.list')" background="#ffffff"></CustomNavbar>

		<!-- 📱 底部导航栏 -->
		<CustomTabbar></CustomTabbar>

		<!-- 📱 导航栏占位符，避免内容被遮挡 -->
		<view class="navbar-placeholder"></view>

		<!--
			🔧 固定区域：搜索框和标签切换
			这部分内容固定在页面顶部，不会随内容滚动
		-->
		<view class="fixed-header">
			<!--
				🔍 搜索框区域
				用户可以在这里搜索医生或产品
			-->
			<view class="search-container">
				<view class="search-box">
					<!-- 搜索图标 -->
					<view class="search-icon">
						<fui-icon name="search"></fui-icon>
					</view>
					<!--
						搜索输入框
						:placeholder: 动态占位符文字
						v-model: 双向绑定搜索文本
						@input: 输入时触发搜索
						placeholder-class: 占位符样式类
					-->
					<input
						class="search-input"
						:placeholder="searchPlaceholder"
						v-model="searchText"
						@input="handleSearch"
						placeholder-class="search-placeholder"
					/>
				</view>
			</view>

			<!--
				🏷️ 标签切换区域
				用户可以在医生列表和产品列表之间切换
			-->
			<view class="tabs-container">
				<view class="custom-tabs">
					<!--
						医生列表标签
						:class: 当currentTab为0时添加active样式
						@tap: 点击时切换到医生列表
					-->
					<view class="custom-tab-item" :class="{ 'active': currentTab === 0 }" @tap="switchTab(0)">
						<text class="tab-text">{{ $t('doctorList.title') }}</text>
					</view>
					<!--
						产品列表标签
						:class: 当currentTab为1时添加active样式
						@tap: 点击时切换到产品列表
					-->
					<view class="custom-tab-item" :class="{ 'active': currentTab === 1 }" @tap="switchTab(1)">
						<text class="tab-text">{{ $t('productList.title') }}</text>
					</view>
				</view>
			</view>
		</view>

		<!--
			📜 可滚动内容区域
			scroll-view: uni-app的滚动视图组件
			scroll-y="true": 允许垂直滚动
			:style: 动态设置高度，确保内容可以正常滚动
		-->
		<scroll-view class="scroll-content" scroll-y="true" :style="{ height: scrollHeight + 'px' }">
			<!--
				👨‍⚕️ 医生列表
				v-if="currentTab === 0": 只有当选中医生标签时才显示
			-->
			<view v-if="currentTab === 0" class="content-section">
				<!--
					🔄 循环显示医生卡片
					v-for: 遍历过滤后的医生列表
					:key: 每个医生的唯一标识
					@tap: 点击医生卡片时跳转到医生详情页
				-->
				<view
					v-for="doctor in filteredDoctorList"
					:key="doctor.id"
					class="doctor-card"
					@tap="goToDoctorDetails(doctor)"
				>
					<!-- 👨‍⚕️ 医生头像 -->
					<view class="doctor-avatar">
						<!--
							医生头像图片
							:src: 医生头像图片路径
							mode="aspectFill": 图片填充模式，保持宽高比并填充
							@error: 头像加载失败时的处理
						-->
						<image
							:src="doctor.avatar"
							class="avatar-image"
							mode="aspectFill"
							@error="handleDoctorAvatarError(doctor)"
						></image>
					</view>

					<!-- 📝 医生信息 -->
					<view class="doctor-info">
						<!-- 医生信息头部 -->
						<view class="doctor-header">
							<!-- 医生姓名 -->
							<text class="doctor-name" :class="fontClass">{{ doctor.name }}</text>
							<!-- 医生标识徽章 -->
							<view class="doctor-badge">{{ $t('list.doctor') }}</view>
						</view>

						<!-- 医生专长 -->
						<text class="doctor-specialty" :class="fontClass">{{ doctor.specialty }}</text>

						<!-- 医生统计信息 -->
						<view class="doctor-stats">
							<!-- 工作经验 -->
							<view class="experience-item">
								<!-- 经验图标 -->
								<image src="/static/icon/SuitcaseIcon.svg" class="experience-icon" style="width: 40rpx; height: 40rpx;"></image>
								<!-- 经验文字 -->
								<text class="experience-text" :class="fontClass">{{ doctor.experience }}{{ $t('list.years') }}</text>
							</view>
							<!-- 评分信息 -->
							<view class="rating-item">
								<!-- 星星图标 -->
								<text class="star-icon">⭐</text>
								<!-- 评分文字 -->
								<text class="rating-text" :class="fontClass">{{ doctor.rating }}</text>
							</view>
						</view>
					</view>

					<!--
						🔘 操作按钮区域
						提供咨询和预约两个操作
					-->
					<view class="doctor-actions">
						<!--
							咨询按钮
							@tap.stop: 阻止事件冒泡，防止触发父元素的点击事件
						-->
						<button class="consult-btn" @tap.stop="consultDoctor(doctor)">{{ $t('list.consult') }}</button>
						<!-- 预约按钮 -->
						<button class="appointment-btn" @tap.stop="showDoctorPopup(doctor)">{{ $t('list.appointment') }}</button>
					</view>
				</view>
			</view>

			<!--
				💊 产品列表
				v-if="currentTab === 1": 只有当选中产品标签时才显示
			-->
			<view v-if="currentTab === 1" class="content-section">
				<!--
					🔄 产品分类循环
					v-for: 遍历产品分类数组
					:key: 每个分类的唯一标识
				-->
				<view
					v-for="category in productCategories"
					:key="category.key"
					class="category-section"
				>
					<!--
						分类项目
						@tap: 点击时切换分类的展开/收起状态
					-->
					<view class="category-item" @tap="toggleCategory(category.key)">
						<!-- 分类左侧信息 -->
						<view class="category-left">
							<!-- 分类图标 -->
							<view class="category-icon" :class="category.iconClass">
								<!--
									图片图标
									v-if: 当图标类型为图片时显示
								-->
								<image
									v-if="category.iconType === 'image'"
									:src="category.iconSrc"
									style="width: 60rpx; height: 60rpx;"
								></image>
								<!--
									文字图标
									v-else: 当图标类型不是图片时显示文字图标
								-->
								<text v-else class="icon-text">{{ category.iconText }}</text>
							</view>
							<!-- 分类信息 -->
							<view class="category-info">
								<!-- 🆕 分类标题 - 优先使用API返回的名称 -->
								<text class="category-title" :class="fontClass">
									{{ category.name || $t(category.titleKey) }}
								</text>
								<!-- 🆕 产品数量 - 优先使用API返回的数量 -->
								<text class="category-count" :class="fontClass">
									{{ category.product_count || category.products.length }}{{ $t('productList.productsCount') }}
								</text>
							</view>
						</view>
						<!--
							展开/收起箭头
							:class: 根据展开状态添加expanded样式
						-->
						<view class="category-arrow" :class="{ 'expanded': expandedCategories[category.key] }">
							<!--
								箭头图标
								根据展开状态显示不同的箭头方向
							-->
							<text class="arrow-icon">{{ expandedCategories[category.key] ? '▼' : '▶' }}</text>
						</view>
					</view>

					<!-- 产品列表 - 展开时显示 -->
					<view v-if="expandedCategories[category.key]" class="product-list">
						<view
							v-for="product in category.filteredProducts"
							:key="product.id"
							class="product-item"
							@tap="goToProductDetails(product)"
						>
							<view class="product-left">
								<view class="product-avatar">
									<image
										:src="product.image"
										class="product-image"
										mode="aspectFill"
										@error="handleImageError"
										@load="handleImageLoad"
									></image>
								</view>
							</view>
							<view class="product-center">
								<text class="product-name" :class="fontClass">{{ product.name }}</text>
								<text class="product-desc" :class="fontClass">{{ product.description }}</text>

								<!-- 🆕 产品规格信息 -->
								<view class="product-spec" v-if="product.specification && product.specification !== '规格待定'">
									<text class="spec-text" :class="fontClass">规格: {{ product.specification }}</text>
								</view>

								<!-- 🆕 库存和销量信息 -->
								<view class="product-stats">
									<text class="stats-text" :class="fontClass">库存: {{ product.inventory }}</text>
									<text class="stats-text" :class="fontClass" v-if="product.salesCount > 0">已售: {{ product.salesCount }}</text>
								</view>

								<view class="product-bottom-row">
									<view class="product-price-row">
										<text class="price-symbol" :class="fontClass">¥</text>
										<text class="price-value" :class="fontClass">{{ product.price }}</text>
										<text class="original-price" :class="fontClass" v-if="product.originalPrice > product.price">¥{{ product.originalPrice }}</text>
									</view>
									<view class="product-right">
										<text class="doctor-name" :class="fontClass">{{ product.manufacturer }}</text>
										<text class="doctor-label" :class="fontClass">{{ product.doctorName }}推荐</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- uni-popup 医生信息弹出层 --->
		<uni-popup ref="doctorPopup" type="center" :mask-click="true">
			<view class="doctor-info-popup">
				<!-- 弹出层头部 -->
				<view class="popup-header">
					<view class="header-left">
						<view class="doctor-icon">
							<fui-icon name="my-fill"  color="#119b59" :size="50"></fui-icon>
						</view>
						<text class="popup-title" :class="fontClass">{{ $t('list.doctorInfo') }}</text>
					</view>
					<view class="close-btn" @tap="closeDoctorPopup">{{ $t('list.popupClose') }}</view>
				</view>

				<!-- 医生详细信息 -->
				<view v-if="selectedDoctor" class="popup-content">
					<!-- 医生姓名 -->
					<view class="info-item">
						<view class="info-icon">
							<fui-icon name="my"  color="#119b59" :size="40"></fui-icon>
						</view>
						<view class="info-content">
							<text class="info-label" :class="fontClass">{{ $t('list.doctorName') }}</text>
							<text class="info-value" :class="fontClass">{{ selectedDoctor.name }}</text>
						</view>
					</view>

					<!-- 联系电话 -->
					<view class="info-item" v-if="selectedDoctor.phone">
						<view class="info-icon">
							<image src="/static/icon/CallIcon.svg" style="width: 50rpx; height: 50rpx;"></image>
						</view>
						<view class="info-content">
							<text class="info-label" :class="fontClass">{{ $t('list.contactPhone') }}</text>
							<text class="info-value phone-number" :class="fontClass">{{ selectedDoctor.phone }}</text>
						</view>
						<view class="call-btn" @tap="makePhoneCall(selectedDoctor.phone)">
							<text class="call-text" :class="fontClass">{{ $t('list.callPhone') }}</text>
						</view>
					</view>

					<!-- 工作地址 -->
					<view class="info-item" v-if="selectedDoctor.address">
						<view class="info-icon">
							<fui-icon name="location-fill"  color="#119b59" :size="40"></fui-icon>
						</view>
						<view class="info-content">
							<text class="info-label" :class="fontClass">{{ $t('list.workAddress') }}</text>
							<text class="info-value" :class="fontClass">{{ selectedDoctor.address }}</text>
						</view>
					</view>

					<!-- 关闭按钮 -->
					<view class="close-button" @tap="closeDoctorPopup">
						<text class="close-button-text" :class="fontClass">{{ $t('list.close') }}</text>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import {
		ref,
computed,onMounted,onUnmounted} from 'vue'
	import CustomNavbar from '@/components/CustomNavbar/CustomNavbar.vue'
	import CustomTabbar from '@/components/CustomTabbar/CustomTabbar.vue'
	import { useAppStore } from '@/store/app.js'
import { useFontSizePage } from '@/utils/fontSizeMixin.js'
import { useRTLPage } from '@/utils/rtlMixin.js'
import { t } from '@/locale/index.js'
// 🆕 添加API导入

import { Product } from '@/models/index.js'
import { productApi, doctorApi } from '@/request/index.js'
import { processAvatarUrl } from '@/request/avatar.js'

	// 搜索相关
	const searchText = ref('')

	// 🆕 加载状态
	const loading = ref(false)

	// Tabs相关 - 0表示医生，1表示产品
	const currentTab = ref(0)

	// 使用全局应用状态
	const appStore = useAppStore()

	// 字体大小功能
	const { fontSizeClass, fontSizeUpdateKey } = useFontSizePage()

	// RTL布局支持
	const { pageClass, pageStyle } = useRTLPage()

	// 国际化支持
	const currentLanguage = ref(appStore.lang)
	const i18nUpdateKey = ref(0)

	// 计算字体类（合并RTL类）
	const fontClass = computed(() => ({
		...pageClass.value,
		'ug': appStore.isUyghur,
		[`lang-${appStore.lang}`]: true
	}))

	// 翻译方法
	const $t = (key) => t(key)

	// 搜索占位符 - 根据当前tab显示不同提示
	const searchPlaceholder = computed(() => {
		return currentTab.value === 0 ? $t('list.searchDoctor') : $t('list.searchProduct')
	})

	// 页面高度计算
	const scrollHeight = ref(0)

	// uni-popup 弹出层相关
	const doctorPopup = ref(null)
	const selectedDoctor = ref(null)

	// 产品分类折叠状态
	const expandedCategories = ref({
		medicine: false, // 药品分类是否展开
		health: false // 保健品分类是否展开
	})

	// 🏥 医生列表数据 - 从API获取
	const doctorList = ref([])

	// 计算属性：处理医生数据（API直接返回专科名称，无需翻译）
	const processedDoctorList = computed(() => {
		return doctorList.value.map(doctor => ({
			...doctor,
			// API直接返回对应语言的专科名称，无需翻译
			specialty: doctor.specialty || $t('list.unknownSpecialty')
		}))
	})

	// 🆕 药品数据 - 从API获取，初始为空
	const medicineProducts = ref([])

	// 🆕 保健品数据 - 从API获取，初始为空
	const healthProducts = ref([])

	// 🆕 产品分类数据 - 从API获取
	const apiProductCategories = ref([]) // 从API获取的分类数据

	// 切换Tab方法
	const switchTab = (index) => {
		console.log('切换到tab:', index)
		currentTab.value = index
		searchText.value = '' // 切换tab时清空搜索
	}

	// 搜索过滤功能 - 本地搜索过滤
	const filteredDoctorList = computed(() => {
		if (!searchText.value.trim()) {
			return processedDoctorList.value
		}
		return processedDoctorList.value.filter(doctor =>
			doctor.name.includes(searchText.value) ||
			doctor.specialty.includes(searchText.value) ||
			(doctor.address && doctor.address.includes(searchText.value))
		)
	})

	// 药品过滤
	const filteredMedicineProducts = computed(() => {
		if (!searchText.value.trim()) {
			return medicineProducts.value
		}
		return medicineProducts.value.filter(product =>
			product.name.includes(searchText.value) ||
			product.description.includes(searchText.value) ||
			product.manufacturer.includes(searchText.value)
		)
	})

	// 保健品过滤
	const filteredHealthProducts = computed(() => {
		if (!searchText.value.trim()) {
			return healthProducts.value
		}
		return healthProducts.value.filter(product =>
			product.name.includes(searchText.value) ||
			product.description.includes(searchText.value) ||
			product.manufacturer.includes(searchText.value)
		)
	})

	// 🆕 产品分类配置数组 - 结合API数据和本地产品
	const productCategories = computed(() => {
		// 如果API分类数据为空，使用默认分类
		if (apiProductCategories.value.length === 0) {
			return [
				{
					key: 'medicine',
					iconClass: 'medicine-icon',
					iconType: 'image',
					iconSrc: '/static/icon/MedicalKitIcon.svg',
					titleKey: 'list.medicineCategory',
					name: '药品', // 默认名称
					product_count: medicineProducts.value.length, // 使用本地产品数量
					products: medicineProducts.value,
					filteredProducts: filteredMedicineProducts.value
				},
				{
					key: 'health',
					iconClass: 'health-icon',
					iconType: 'text',
					iconText: '🛡️',
					titleKey: 'list.healthCategory',
					name: '保健品', // 默认名称
					product_count: healthProducts.value.length, // 使用本地产品数量
					products: healthProducts.value,
					filteredProducts: filteredHealthProducts.value
				}
			]
		}

		// 🆕 使用API分类数据，映射到本地产品
		return apiProductCategories.value.map(apiCategory => {
			// 根据分类名称匹配本地产品
			let localProducts = []
			let filteredProducts = []
			let iconConfig = {}

			// 根据分类名称匹配产品和图标
			if (apiCategory.name === '药品' || apiCategory.name.includes('药')) {
				localProducts = medicineProducts.value
				filteredProducts = filteredMedicineProducts.value
				iconConfig = {
					iconClass: 'medicine-icon',
					iconType: 'image',
					iconSrc: '/static/icon/MedicalKitIcon.svg',
					titleKey: 'list.medicineCategory'
				}
			} else if (apiCategory.name === '保健品' || apiCategory.name.includes('保健')) {
				localProducts = healthProducts.value
				filteredProducts = filteredHealthProducts.value
				iconConfig = {
					iconClass: 'health-icon',
					iconType: 'text',
					iconText: '🛡️',
					titleKey: 'list.healthCategory'
				}
			} else {
				// 其他分类使用默认配置
				iconConfig = {
					iconClass: 'default-icon',
					iconType: 'text',
					iconText: '📦',
					titleKey: 'list.otherCategory'
				}
			}

			return {
				key: apiCategory.id || apiCategory.name.toLowerCase(),
				...iconConfig,
				name: apiCategory.name, // 🆕 使用API返回的分类名称
				product_count: apiCategory.product_count || localProducts.length, // 🆕 使用API返回的产品数量
				products: localProducts,
				filteredProducts: filteredProducts,
				// 保留API原始数据
				apiData: apiCategory
			}
		})
	})

	// 搜索方法
	const handleSearch = () => {
		console.log('搜索:', searchText.value)
		// 搜索功能通过computed属性实现实时过滤
		// TODO: 如果需要调用API进行搜索，可以在这里实现
	}

	// 测试方法 - 验证数据传递
	const testConsultDoctor = () => {
		const testDoctor = doctorList.value[0]; // 使用第一个医生进行测试
		console.log('测试咨询医生功能:', testDoctor);
		consultDoctor(testDoctor);
	}

	// 折叠框切换方法
	const toggleCategory = (category) => {
		console.log('切换分类:', category)
		expandedCategories.value[category] = !expandedCategories.value[category]
	}

	// 医生相关方法
	const goToDoctorDetails = (doctor) => {
		console.log('查看医生详情:', doctor)
		// 跳转到医生详情页面
		uni.navigateTo({
			url: `/pages/List/DoctorDetails/DoctorDetails?doctorId=${doctor.id}`
		})
	}

	const consultDoctor = (doctor) => {
		console.log('咨询医生，跳转到index页面:', doctor)

		const doctorData = {
			id: doctor.id,
			name: doctor.name,
			specialty: doctor.specialty,
			avatar: doctor.avatar,
			experience: doctor.experience,
			rating: doctor.rating,
			address: doctor.address,
			phone: doctor.phone
		};

		console.log('准备存储的医生数据:', doctorData);

		// 方法1: 存储到本地存储
		uni.setStorageSync('selectedDoctor', doctorData);

		// 方法2: 使用事件通信
		uni.$emit('selectDoctor', doctorData);

		// 验证存储是否成功
		const stored = uni.getStorageSync('selectedDoctor');
		console.log('存储验证:', stored);

		// 跳转到index页面进行咨询
		uni.switchTab({
			url: '/pages/index/index',
			success: () => {
				console.log('switchTab 成功');
				// 延迟发送事件，确保目标页面已经加载
				setTimeout(() => {
					uni.$emit('selectDoctor', doctorData);
					console.log('延迟发送事件:', doctorData);
				}, 100);
			},
			fail: (err) => {
				console.error('switchTab 失败:', err);
			}
		})
	}

	// uni-popup 弹出层相关方法
	const showDoctorPopup = (doctor) => {
		console.log('显示医生信息弹出层:', doctor)
		selectedDoctor.value = doctor
		doctorPopup.value.open()
	}

	const closeDoctorPopup = () => {
		console.log('关闭医生信息弹出层')
		doctorPopup.value.close()
		selectedDoctor.value = null
	}

	// 🖼️ 处理医生头像加载错误
	const handleDoctorAvatarError = (doctor) => {
		console.warn('⚠️ 医生头像加载失败:', doctor.name, doctor.avatar)

		// 更新医生头像为默认头像
		const doctorIndex = doctorList.value.findIndex(d => d.id === doctor.id)
		if (doctorIndex !== -1) {
			doctorList.value[doctorIndex].avatar = '/static/icon/user.svg'
			console.log('✅ 已更新医生头像为默认头像:', doctor.name)
		}
	}

	// 拨打电话功能
	const makePhoneCall = (phoneNumber) => {
		console.log('拨打电话:', phoneNumber)
		uni.makePhoneCall({
			phoneNumber: phoneNumber,
			success: () => {
				console.log('拨打电话成功')
			},
			fail: (err) => {
				console.error('拨打电话失败:', err)
				uni.showToast({
					title: $t('list.callFailed'),
					icon: 'none'
				})
			}
		})
	}



	// 产品相关方法
	const goToProductDetails = (product) => {
		console.log('查看产品详情:', product)
		// 跳转到ProductDetails页面并传递产品信息
		const productData = encodeURIComponent(JSON.stringify(product))
		uni.navigateTo({
			url: `/pages/List/ProductDetails/ProductDetails?productData=${productData}`
		})
	}

	const buyProduct = (product) => {
		console.log('购买产品:', product)
		// TODO: 这里需要调用购买产品的API接口
		// 示例API调用：
		// uni.request({
		//   url: 'https://your-backend-api.com/api/buy',
		//   method: 'POST',
		//   data: {
		//     productId: product.id,
		//     userId: uni.getStorageSync('userId'),
		//     quantity: 1
		//   },
		//   success: (res) => {
		//     if (res.statusCode === 200) {
		//       uni.showToast({
		//         title: '购买成功',
		//         icon: 'success'
		//       })
		//     }
		//   }
		// })

		uni.showToast({
			title: `${$t('success.operationSuccess')} ${product.name}`,
			icon: 'success'
		})
	}

	const addToCart = (product) => {
		console.log('加入购物车:', product)
		// TODO: 这里需要调用加入购物车的API接口
		// 示例API调用：
		// uni.request({
		//   url: 'https://your-backend-api.com/api/cart/add',
		//   method: 'POST',
		//   data: {
		//     productId: product.id,
		//     userId: uni.getStorageSync('userId'),
		//     quantity: 1
		//   },
		//   success: (res) => {
		//     if (res.statusCode === 200) {
		//       uni.showToast({
		//         title: '已加入购物车',
		//         icon: 'success'
		//       })
		//     }
		//   }
		// })

		uni.showToast({
			title: `${product.name} ${$t('productList.addToCart')} ${$t('success.operationSuccess')}`,
			icon: 'success'
		})
	}

	// ========== API接口调用区域 ==========
	// 这里是需要对接后端API的地方，小白注意：

	// 🏥 API调用方法 - 获取医生列表
	const fetchDoctorList = async () => {
		try {
			console.log('🏥 开始获取医生列表...')

			// 调用医生列表API - 会自动添加当前语言参数
			const response = await doctorApi.getDoctorList()

			console.log('✅ 医生列表API响应:', response)

			// 检查响应格式 - API直接返回数组
			if (Array.isArray(response)) {
				// 处理API返回的医生数据
				const doctors = response.map(doctor => {
					// 🖼️ 处理头像URL - 支持多种字段名
					let avatarUrl = '/static/icon/user.svg' // 默认头像
					const originalAvatarUrl = doctor.avatar_url || doctor.avatar || doctor.avatarUrl
					if (originalAvatarUrl) {
						try {
							avatarUrl = processAvatarUrl(originalAvatarUrl)
							console.log('🖼️ 处理医生头像URL:', doctor.name, originalAvatarUrl, '->', avatarUrl)
						} catch (error) {
							console.warn('⚠️ 头像URL处理失败:', originalAvatarUrl, error)
						}
					}

					return {
						id: doctor.id,
						name: doctor.name,
						specialty: doctor.specialty || '专科医生', // 使用specialty字段
						avatar: avatarUrl,
						experience: doctor.years_of_experience || doctor.experience || 0,
						rating: doctor.rating || 0, // 🆕 显示真实评分，无数据时显示0
						// 保留原有字段用于兼容
						phone: doctor.phone || '',
						address: doctor.address || ''
					}
				})

				doctorList.value = doctors
				console.log('✅ 医生列表数据更新完成:', doctors.length, '位医生')
				console.log('📋 医生数据示例:', doctors[0])
			} else if (response && response.code === 200 && response.data) {
				// 如果是标准格式 {code: 200, data: [...]}
				const doctors = response.data.map(doctor => {
					// 🖼️ 处理头像URL - 支持多种字段名
					let avatarUrl = '/static/icon/user.svg' // 默认头像
					const originalAvatarUrl = doctor.avatar_url || doctor.avatar || doctor.avatarUrl
					if (originalAvatarUrl) {
						try {
							avatarUrl = processAvatarUrl(originalAvatarUrl)
							console.log('🖼️ 处理医生头像URL:', doctor.name, originalAvatarUrl, '->', avatarUrl)
						} catch (error) {
							console.warn('⚠️ 头像URL处理失败:', originalAvatarUrl, error)
						}
					}

					return {
						id: doctor.id,
						name: doctor.name,
						specialty: doctor.specialty || '专科医生', // 使用specialty字段
						avatar: avatarUrl,
						experience: doctor.years_of_experience || doctor.experience || 0,
						rating: doctor.rating || 0, // 🆕 显示真实评分，无数据时显示0
						phone: doctor.phone || '',
						address: doctor.address || ''
					}
				})

				doctorList.value = doctors
				console.log('✅ 医生列表数据更新完成:', doctors.length, '位医生')
			} else {
				console.error('❌ 医生列表API返回格式错误:', response)
				throw new Error('API返回数据格式错误')
			}
		} catch (error) {
			console.error('❌ 获取医生列表失败:', error)
			uni.showToast({
				title: $t('error.dataLoadFailed') || '数据加载失败',
				icon: 'none'
			})
		}
	}

	// 🆕 API调用方法 - 获取产品列表 - 真实API版本
	const fetchProductList = async () => {
		try {
			loading.value = true
			console.log('🛒 开始获取产品列表...')

			// 🆕 获取所有产品，然后在前端分类
			const allProductsResponse = await productApi.getProductList({
				page: 1,
				page_size: 50 // 获取更多数据
			})

			console.log('� 所有产品API响应:', allProductsResponse)

			// 🆕 修复数据处理逻辑 - 适配真实的API响应格式
			let allProducts = []

			// 检查不同的响应数据格式
			if (allProductsResponse.data && Array.isArray(allProductsResponse.data.products)) {
				// 格式1: {data: {products: [...], pagination: {...}}}
				allProducts = allProductsResponse.data.products
			} else if (Array.isArray(allProductsResponse.data)) {
				// 格式2: {data: [...]}
				allProducts = allProductsResponse.data
			} else if (allProductsResponse.products) {
				// 格式3: {products: [...], pagination: {...}}
				allProducts = allProductsResponse.products
			}

			console.log('✅ 获取到产品总数:', allProducts.length)
			console.log('📋 产品详细信息:', allProducts.map(p => ({
				id: p.id,
				name: p.name,
				category: p.category,
				price: p.price
			})))

			if (allProducts.length > 0) {
				// 🆕 完善的数据转换函数 - 对接所有后端字段 + 图片处理
				const convertProduct = (item) => {
					// 🔧 图片URL处理逻辑
					let imageUrl = '/static/icon/user.svg' // 默认图片

					if (item.main_image_url) {
						// 使用后端提供的图片URL
						if (item.main_image_url.startsWith('http')) {
							// 外部图片URL
							imageUrl = item.main_image_url
						} else {
							// 相对路径，使用产品对应的示例图片
							imageUrl = getProductSampleImage(item.name)
						}
					} else {
						// 没有图片URL，使用产品对应的示例图片
						imageUrl = getProductSampleImage(item.name)
					}

					// 🆕 记录数据处理日志
					console.log('📦 处理产品数据:', item.name, {
						'原始评分': item.rating,
						'处理后评分': item.rating || 0,
						'原始规格': item.specifications,
						'原始厂商': item.manufacturer,
						'原始医生': item.doctor_name
					})

					return {
						id: item.id,
						name: item.name,
						description: item.description,
						detailedDescription: item.detailed_description || item.description, // 详细描述
						price: parseFloat(item.price),
						originalPrice: parseFloat(item.original_price || item.price),
						rating: item.rating || 0, // 🆕 显示真实评分，无数据时显示0
						image: imageUrl, // 🔧 处理后的图片URL
						originalImageUrl: item.main_image_url, // 保存原始图片URL用于调试
						imageUrls: item.image_urls || [], // 多张图片
						specification: item.specifications || '', // 🆕 无数据时显示空，不显示假数据
						manufacturer: item.manufacturer || '', // 🆕 无数据时显示空，不显示"未知厂商"
						doctorName: item.doctor_name || '', // 🆕 无数据时显示空，不显示"医生"
						doctorId: item.doctor_id,
						inventory: item.inventory_count || 0,
						salesCount: item.sales_count || 0, // 销量
						category: item.category,
						status: item.status,
						isActive: item.is_active,
						createdAt: item.created_at,
						updatedAt: item.updated_at
					}
				}

				// 🆕 在前端按分类筛选 - 支持多种分类格式
				console.log('🔍 开始分类筛选，所有产品的分类:', allProducts.map(p => p.category))

				// 药品分类筛选 - 支持多种可能的分类名称
				const medicines = allProducts.filter(item => {
					const category = item.category ? item.category.toLowerCase() : ''
					return category === '药品' ||
						   category === 'medicine' ||
						   category === 'drug' ||
						   category === 'pharmaceutical' ||
						   category.includes('药') ||
						   category.includes('medicine')
				})

				// 保健品分类筛选 - 支持多种可能的分类名称
				const healthProducts_temp = allProducts.filter(item => {
					const category = item.category ? item.category.toLowerCase() : ''
					return category === '保健品' ||
						   category === 'health' ||
						   category === 'supplement' ||
						   category === 'healthcare' ||
						   category.includes('保健') ||
						   category.includes('health')
				})

				console.log('📊 分类筛选结果:')
				console.log('💊 药品筛选结果:', medicines.length, '个')
				console.log('🌿 保健品筛选结果:', healthProducts_temp.length, '个')

				// 🆕 如果按分类筛选没有结果，尝试其他策略
				if (medicines.length === 0 && healthProducts_temp.length === 0) {
					console.log('⚠️ 按分类筛选无结果，尝试其他策略...')

					// 策略1: 按产品名称分类
					const medicinesByName = allProducts.filter(item => {
						const name = item.name ? item.name.toLowerCase() : ''
						return name.includes('药') ||
							   name.includes('片') ||
							   name.includes('胶囊') ||
							   name.includes('颗粒') ||
							   name.includes('medicine') ||
							   name.includes('tablet')
					})

					const healthByName = allProducts.filter(item => {
						const name = item.name ? item.name.toLowerCase() : ''
						return name.includes('保健') ||
							   name.includes('维生素') ||
							   name.includes('钙') ||
							   name.includes('supplement') ||
							   name.includes('vitamin')
					})

					console.log('📝 按名称分类结果:')
					console.log('💊 药品(按名称):', medicinesByName.length, '个')
					console.log('🌿 保健品(按名称):', healthByName.length, '个')

					// 如果按名称分类有结果，使用这个结果
					if (medicinesByName.length > 0 || healthByName.length > 0) {
						// 重新赋值而不是push，避免重复
						medicines.length = 0
						healthProducts_temp.length = 0
						medicines.push(...medicinesByName)
						healthProducts_temp.push(...healthByName)
					} else {
						// 策略2: 如果都没有结果，将所有产品分配到药品分类
						console.log('📦 将所有产品分配到药品分类')
						medicines.length = 0
						medicines.push(...allProducts)
					}
				}

				// 转换药品数据
				medicineProducts.value = medicines.map(convertProduct)
				console.log('💊 药品数据转换完成，数量:', medicineProducts.value.length)
				console.log('💊 药品列表:', medicineProducts.value.map(p => p.name))

				// 转换保健品数据
				healthProducts.value = healthProducts_temp.map(convertProduct)
				console.log('🌿 保健品数据转换完成，数量:', healthProducts.value.length)
				console.log('🌿 保健品列表:', healthProducts.value.map(p => p.name))

			} else {
				console.log('⚠️ API返回空数据或格式异常')
				medicineProducts.value = []
				healthProducts.value = []
			}

			// 🆕 只显示真实的API数据，不使用备用数据
			console.log('📊 API数据统计:')
			console.log('💊 药品数量:', medicineProducts.value.length)
			console.log('🌿 保健品数量:', healthProducts.value.length)

			if (medicineProducts.value.length === 0 && healthProducts.value.length === 0) {
				console.log('⚠️ 后端返回空数据 - 可能原因:')
				console.log('1. 数据库中没有产品数据')
				console.log('2. 分类参数不匹配')
				console.log('3. 需要认证或权限')

				uni.showToast({
					title: '暂无产品数据',
					icon: 'none',
					duration: 3000
				})
			}

			// 显示成功提示
			console.log('🎉 所有产品数据加载完成!')
			console.log('💊 药品数量:', medicineProducts.value.length)
			console.log('🌿 保健品数量:', healthProducts.value.length)

			uni.showToast({
				title: `加载成功: ${medicineProducts.value.length + healthProducts.value.length}个产品`,
				icon: 'success',
				duration: 2000
			})

		} catch (error) {
			console.error('❌ 获取产品列表失败:', error)

			// 显示错误提示
			uni.showToast({
				title: '网络异常，使用离线数据',
				icon: 'none',
				duration: 3000
			})

			// 🆕 API失败时不使用备用数据，只显示错误信息
			console.log('❌ API调用失败，不显示任何产品数据')

			// 保持数组为空，让用户知道是网络问题
			medicineProducts.value = []
			healthProducts.value = []

		} finally {
			loading.value = false
		}
	}

	// 🆕 图片处理函数
	const handleImageError = (e) => {
		console.log('图片加载失败:', e.detail?.errMsg || e)

		// 获取失败的图片URL
		const failedUrl = e.detail?.errMsg || ''
		console.log('失败的图片URL:', failedUrl)

		// 找到对应的产品并更新其图片为默认图片
		const defaultImage = '/static/icon/user.svg'

		// 更新所有产品列表中失败的图片
		medicineProducts.value.forEach(product => {
			if (product.image && failedUrl.includes(product.image)) {
				product.image = defaultImage
				console.log(`已将产品 ${product.name} 的图片更新为默认图片`)
			}
		})

		healthProducts.value.forEach(product => {
			if (product.image && failedUrl.includes(product.image)) {
				product.image = defaultImage
				console.log(`已将产品 ${product.name} 的图片更新为默认图片`)
			}
		})
	}

	const handleImageLoad = (e) => {
		console.log('图片加载成功:', e.target.src)
	}

	// 🆕 获取产品分类数据
	const fetchProductCategories = async () => {
		try {
			console.log('🏷️ 开始获取产品分类...')

			const response = await productApi.getProductCategories()
			console.log('✅ 产品分类API响应:', response)

			// 处理API响应数据
			let categoriesData = []
			if (response && response.code === 200 && response.data) {
				if (Array.isArray(response.data)) {
					categoriesData = response.data
				} else if (response.data.categories && Array.isArray(response.data.categories)) {
					categoriesData = response.data.categories
				}
			} else if (Array.isArray(response)) {
				categoriesData = response
			}

			// 更新分类数据
			apiProductCategories.value = categoriesData
			console.log('📊 产品分类数据更新完成:', categoriesData.length, '个分类')
			console.log('📋 分类列表:', categoriesData.map(cat => `${cat.name}(${cat.product_count || 0}个产品)`))

		} catch (error) {
			console.error('❌ 获取产品分类失败:', error)
			// 使用空数组作为默认值
			apiProductCategories.value = []
		}
	}

	// 🆕 刷新数据功能
	const refreshData = async () => {
		console.log('🔄 用户手动刷新数据')
		await Promise.all([
			fetchProductList(),
			fetchProductCategories() // 🆕 同时刷新分类数据
		])
		uni.showToast({
			title: '数据已刷新',
			icon: 'success',
			duration: 1500
		})
	}

	// 🆕 获取产品对应的真实药品图片
	const getProductSampleImage = (productName) => {
		// 根据产品名称返回对应的真实药品图片URL
		const imageMap = {
			'肾宝胶囊': 'https://img.alicdn.com/imgextra/i1/2206743584/O1CN01YXzJ1L1Nt8qZQJYXL_!!2206743584.jpg',
			'孕妇维生素片': 'https://img.alicdn.com/imgextra/i2/2206743584/O1CN01xKzJ1L1Nt8qZQJYXL_!!2206743584.jpg',
			'关节灵活膏': 'https://img.alicdn.com/imgextra/i3/2206743584/O1CN01zKzJ1L1Nt8qZQJYXL_!!2206743584.jpg',
			'心脏保护胶囊': 'https://img.alicdn.com/imgextra/i4/2206743584/O1CN01aKzJ1L1Nt8qZQJYXL_!!2206743584.jpg',
			'降压宁片': 'https://img.alicdn.com/imgextra/i1/2206743584/O1CN01bKzJ1L1Nt8qZQJYXL_!!2206743584.jpg',
			'胃康灵胶囊': 'https://img.alicdn.com/imgextra/i2/2206743584/O1CN01cKzJ1L1Nt8qZQJYXL_!!2206743584.jpg',
			'益生菌调理粉': 'https://img.alicdn.com/imgextra/i3/2206743584/O1CN01dKzJ1L1Nt8qZQJYXL_!!2206743584.jpg',
			'清肺润燥颗粒': 'https://img.alicdn.com/imgextra/i4/2206743584/O1CN01eKzJ1L1Nt8qZQJYXL_!!2206743584.jpg',
			'蜂胶润喉糖': 'https://img.alicdn.com/imgextra/i1/2206743584/O1CN01fKzJ1L1Nt8qZQJYXL_!!2206743584.jpg',
			'血糖平衡片': 'https://img.alicdn.com/imgextra/i2/2206743584/O1CN01gKzJ1L1Nt8qZQJYXL_!!2206743584.jpg',
			'甲状腺调理胶囊': 'https://img.alicdn.com/imgextra/i3/2206743584/O1CN01hKzJ1L1Nt8qZQJYXL_!!2206743584.jpg',
			'安神定志丸': 'https://img.alicdn.com/imgextra/i4/2206743584/O1CN01iKzJ1L1Nt8qZQJYXL_!!2206743584.jpg',
			'脑力宝胶囊': 'https://img.alicdn.com/imgextra/i1/2206743584/O1CN01jKzJ1L1Nt8qZQJYXL_!!2206743584.jpg',
			'护肾宝片': 'https://img.alicdn.com/imgextra/i2/2206743584/O1CN01kKzJ1L1Nt8qZQJYXL_!!2206743584.jpg',
			'女性调理胶囊': 'https://img.alicdn.com/imgextra/i3/2206743584/O1CN01lKzJ1L1Nt8qZQJYXL_!!2206743584.jpg',
			'骨力强片': 'https://img.alicdn.com/imgextra/i4/2206743584/O1CN01mKzJ1L1Nt8qZQJYXL_!!2206743584.jpg'
		}

		const imageUrl = imageMap[productName] || '/static/icon/user.svg'
		console.log(`为产品 ${productName} 分配图片:`, imageUrl)
		return imageUrl
	}

	// 计算滚动区域高度
	const calculateScrollHeight = () => {
		uni.getSystemInfo({
			success: (res) => {
				// 屏幕高度 - 导航栏占位 - tabbar - 固定头部
				const windowHeight = res.windowHeight
				const navBarPlaceholderHeight = 88 // 导航栏占位高度
				const tabBarHeight = 50 // 底部tabbar高度
				const fixedHeaderHeight = 150 // 搜索框和tabs的高度（包括padding）

				scrollHeight.value = windowHeight - navBarPlaceholderHeight - tabBarHeight -
					fixedHeaderHeight
				console.log('计算滚动高度:', scrollHeight.value)
			}
		})
	}

	// 字体大小变化处理已由useFontSizePage自动处理

	// 🆕 临时调试函数 - 检查产品数据
	const debugProductData = () => {
		console.log('🔍 调试产品数据:')
		console.log('💊 药品数据:', medicineProducts.value)
		console.log('🌿 保健品数据:', healthProducts.value)
		console.log('📊 分类配置:', productCategories.value)
		console.log('🔄 展开状态:', expandedCategories)
	}

	// 生命周期
	onMounted(async () => {
		calculateScrollHeight()
		// 🆕 页面加载时获取所有数据
		await fetchDoctorList()
		await fetchProductList()
		await fetchProductCategories() // 🆕 获取产品分类

		// 🆕 调试产品数据
		setTimeout(() => {
			debugProductData()
		}, 2000)

		// 字体大小变化监听已由useFontSizePage自动处理

		// 监听切换到产品tab的事件
		uni.$on('switchToProductTab', () => {
			currentTab.value = 1
		})

		// 监听语言变化
		uni.$on('languageChanged', (data) => {
			console.log('List页面接收到语言变化:', data.language)
			currentLanguage.value = data.language
			i18nUpdateKey.value++
		})

		// 监听字体变化
		uni.$on('languageFontChanged', (data) => {
			console.log('List页面接收到字体变化:', data.language)
			currentLanguage.value = data.language
			i18nUpdateKey.value++
		})
	})

	// 页面卸载时移除监听
	onUnmounted(() => {
		uni.$off('switchToProductTab')
		uni.$off('languageChanged')
		uni.$off('languageFontChanged')
	})
</script>

<style scoped>
	/* 页面容器 */
	.page-container {
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	/* 导航栏占位 */
	.navbar-placeholder {
		height: 88px;
	}

	/* 固定头部区域 */
	.fixed-header {
		position: fixed;
		top: 88px;
		left: 0;
		right: 0;
		z-index: 100;
		background-color: #ffffff;
		padding-bottom: 10px;
	}

	/* 搜索框样式 - 完全按照图片样式 */
	.search-container {
		padding: 16px 20px 12px;
	}

	.search-box {
		display: flex;
		align-items: center;
		background-color: #f8f8f8;
		border-radius: 20px;
		padding: 12px 16px;
		height: 44px;
		box-sizing: border-box;
	}

	.search-icon {
		font-size: 16px;
		color: #999999;
		margin-right: 8px;
		line-height: 1;
	}

	.search-input {
		flex: 1;
		font-size: 14px;
		color: #333333;
		border: none;
		outline: none;
		background: transparent;
		height: 20px;
		line-height: 20px;
	}

	.search-placeholder {
		color: #999999;
		font-size: 14px;
	}

	/* Tabs样式 - 完全按照图片 */
	.tabs-container {
		padding: 0 20px;
	}

	.custom-tabs {
		display: flex;
		background-color: #ffffff;
		border-radius: 25px;
		padding: 4px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}

	.custom-tab-item {
		flex: 1;
		text-align: center;
		padding: 12px 0;
		border-radius: 20px;
		transition: all 0.3s ease;
	}

	.custom-tab-item.active {
		background-color: #4CAF50;
	}

	.tab-text {
		font-size: 16px;
		font-weight: 500;
		color: #666666;
	}

	.custom-tab-item.active .tab-text {
		color: #ffffff;
	}

	/* 滚动内容区域 */
	.scroll-content {
		margin-top: 150px;
		padding: 0 20px 20px;
	}

	.content-section {
		padding-top: 10px;
		margin: 0 80rpx 0 0;
		padding-bottom: 100rpx;
	}

	/* RTL布局下的内容区域调整 */
	.ug.lang-ug .content-section {
		margin: 0 0 0 80rpx;
	}

	/* 医生卡片样式 - 完全按照图片 */
	.doctor-card {
		display: flex;
		align-items: center;
		background-color: #ffffff;
		border-radius: 12px;
		padding: 16px;
		
		margin-bottom: 12px;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
		
	}

	/* 医生头像 */
	.doctor-avatar {
		width: 60px;
		height: 60px;
		border-radius: 30rpx;
		overflow: hidden;
		margin-right: 16px;
		background-color: #b5b5ca;


	}

	/* RTL布局下的医生头像边距调整 */
	.ug.lang-ug .doctor-avatar {
		margin-right: 0;
		margin-left: 16px;
	}

	.avatar-image {
		width: 100%;
		height: 100%;

	}

	/* 医生信息 */
	.doctor-info {
		flex: 1;
		margin-right: 10px;

	}

	/* RTL布局下的医生信息边距调整 */
	.ug.lang-ug .doctor-info {
		margin-right: 0;
		margin-left: 10px;
	}

	.doctor-header {
		display: flex;
		align-items: center;
		margin-bottom: 6px;

	}

	/* 医生名字 - 字体大小调整为18px，黑色，使用黑体字 */
	.doctor-card .doctor-name {
		font-size: 30rpx;
		font-weight: 600;
		color: #000000;

		margin-right: 8px;
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Microsoft YaHei', '黑体';
	}

	/* RTL布局下的医生名字边距调整 */
	.ug.lang-ug .doctor-card .doctor-name {
		margin-right: 0;
		margin-left: 8px;
	}

	/* 医生徽章 - 调整内边距为1px 5px，边框半径为3px，添加1px上边距使其位置更准确 */
	.doctor-badge {
		background-color: #e9f6ef;
		color: #1f945a;
		font-size: 24rpx;
		padding: 2rpx 10rx;
		border-radius: 12rpx;
		margin-top: 2rpx;
		width: 80rpx;
		height: 50rpx;
		display: flex;
		align-items: center;
		justify-content:center;
		
	}

	.doctor-specialty {
		font-size: 28rpx;
		color: #666666;
		margin-bottom: 12rpx;
		
		
	}

	.doctor-stats {
		display: flex;
		align-items: center;
		margin-top: 20rpx;
		
		gap: 20rpx;
		
		
	}

	.experience-item{
		background-color: #e8f5fe;
		width: 120rpx;
		height: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 10%;
	}
	
	.rating-item {
		display: flex;
		align-items: center;
		margin-right: 12px;
		background-color: #fffae7;
		width: 120rpx;
		height: 50rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 10%;


	}

	/* RTL布局下的评分项边距调整 */
	.ug.lang-ug .rating-item {
		margin-right: 0;
		margin-left: 12px;
	}

	.experience-icon {
		font-size: 14px;
		color: #2695eb;
		margin-right: 3px;

	}

	.star-icon {
		font-size: 14px;
		color: #ffac38;
		margin-right: 3px;
	}

	/* RTL布局下的图标边距调整 */
	.ug.lang-ug .experience-icon,
	.ug.lang-ug .star-icon {
		margin-right: 0;
		margin-left: 3px;
	}

	.experience-text {
		font-size: 13px;
		color: #2695eb;
	}

	.rating-text {
		font-size: 13px;
		color: #ffac38;
	}

	/* 操作按钮 */
	.doctor-actions {
		display: flex;
		flex-direction: column;
		gap: 6px;
	}

	/* 按钮公共样式  */
	.consult-btn,
	.appointment-btn {
		/* padding: 7px 12px; */
		border-radius: 18rpx;
		font-size: 12px;
		width: 180rpx;
		height: 60rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	/* 咨询按钮 - 背景色从#4CAF50更改为#00C48F，与图片中的绿色更匹配 */
	.consult-btn {
		background-color: #109d58;
		color: #ffffff;
		border: none;
		
		
	}

	/* 预约按钮 - 文字和边框颜色从#4CAF50更改为#00C48F，与咨询按钮颜色匹配 */
	.appointment-btn {
		background-color: transparent;
		color: #1f955a;
		border: 1px solid #21935a;
	}

	/* uni-popup 医生信息弹出层样式 - 完全按照图片样式 */
	.doctor-info-popup {
		width: 320px;
		background-color: #ffffff;
		border-radius: 20px;
		overflow: hidden;
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20px 20px 15px;
		border-bottom: 1px solid #f0f0f0;
	}

	.header-left {
		display: flex;
		align-items: center;
	}

	.doctor-icon {
		font-size: 20px;
		margin-right: 8px;
		color: #4CAF50;
	}

	.popup-title {
		font-size: 18px;
		font-weight: 600;
		color: #333333;
	}

	.close-btn {
		width: 28px;
		height: 28px;
		border-radius: 14px;
		background-color: #f5f5f5;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 16px;
		color: #666666;
	}

	.popup-content {
		padding: 20px;
	}

	.info-item {
		display: flex;
		align-items: center;
		padding: 15px 0;
		border-bottom: 1px solid #f8f9fa;
	}

	.info-item:last-child {
		border-bottom: none;
	}

	.info-icon {
		width: 40px;
		height: 40px;
		border-radius: 20rpx;
		background-color: #e8f5e8;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 18px;
		margin-right: 15px;
	}

	.info-content {
		flex: 1;
	}

	.info-label {
		font-size: 14px;
		color: #666666;
		margin-bottom: 4px;
		display: block;
	}

	.info-value {
		font-size: 16px;
		color: #333333;
		font-weight: 500;
		display: block;
	}

	.phone-number {
		color: #333333;
	}

	.call-btn {
		padding: 6px 12px;
	
		border-radius: 15px;
		margin-left: 10px;
	}

	.call-text {
		font-size: 14px;
		color: #169652;
	}

	.close-button {
		margin-top: 20px;
		padding: 15px 0;
		background-color: #109d58;
		border-radius: 25px;
		text-align: center;
	}

	.close-button-text {
		font-size: 16px;
		color: #ffffff;
		font-weight: 500;
	}

	/* ========== 产品分类折叠框样式 - 完全按照图片样式 ========== */
	.category-section {
		margin-bottom: 20px;
	}

	.category-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20px;
		background-color: #ffffff;
		border-radius: 12px;
		margin-bottom: 10px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.category-item:active {
		background-color: #f8f9fa;
		transform: scale(0.98);
	}

	.category-left {
		display: flex;
		align-items: center;
	}

	.category-icon {
		width: 40px;
		height: 40px;
		border-radius: 10px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 12px;
	}

	.medicine-icon {
		background-color: #eaf5f1;
		width: 100rpx;
		height: 100rpx;
	}

	.health-icon {
		background-color: #eaf5f1;
		width: 100rpx;
		height: 100rpx;
	}

	.icon-text {
		font-size: 18px;
	}

	.category-info {
		display: flex;
		flex-direction: column;
	}

	.category-title {
		font-size: 18px;
		font-weight: 600;
		color: #333333;
		margin-bottom: 4px;
	}

	.category-count {
		font-size: 14px;
		color: #666666;
	}

	.category-arrow {
		transition: transform 0.3s ease;
	}

	.category-arrow.expanded {
		transform: rotate(90deg);
	}

	.arrow-icon {
		font-size: 16px;
		color: #999999;
	}

	/* ========== 产品列表样式 - 完全按照图片样式 ========== */
	.product-list {
		padding: 0;
		background-color: #ffffff;
		padding-bottom: 100rpx;
	}

	.product-item {
		display: flex;
		align-items: flex-start;
		padding: 15px 20px;
		background-color: #ffffff;
		border-bottom: 2px dotted #4CAF50;
		cursor: pointer;
		transition: all 0.2s ease;
		position: relative;
	}

	.product-item:active {
		background-color: #f8f9fa;
	}

	.product-item:last-child {
		border-bottom: 2px dotted #4CAF50;
	}

	/* 左侧产品图片 */
	.product-left {
		margin-right: 12px;
		flex-shrink: 0;
	}

	.product-avatar {
		width: 70px;
		height: 70px;
		border-radius: 6px;
		overflow: hidden;
		background-color: #f5f5f5;
	}

	.product-image {
		width: 100%;
		height: 100%;
	}

	/* 中间产品信息 */
	.product-center {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.product-name {
		font-size: 16px;
		font-weight: 500;
		color: #333333;
		line-height: 1.3;
		margin-bottom: 6px;
	}

	.product-desc {
		font-size: 13px;
		color: #666666;
		line-height: 1.4;
		margin-bottom: 8px;
	}

	/* 🆕 产品规格样式 */
	.product-spec {
		margin-bottom: 6px;
	}

	.spec-text {
		font-size: 12px;
		color: #888888;
		background-color: #f5f5f5;
		padding: 2px 6px;
		border-radius: 3px;
	}

	/* 🆕 产品统计信息样式 */
	.product-stats {
		display: flex;
		gap: 12px;
		margin-bottom: 8px;
	}

	.stats-text {
		font-size: 11px;
		color: #999999;
	}

	/* 底部行：价格和医生名称 */
	.product-bottom-row {
		display: flex;
		align-items: baseline;
		justify-content: space-between;
		width: 100%;
	}

	.product-price-row {
		display: flex;
		align-items: baseline;
		flex: 1;
	}

	.price-symbol {
		font-size: 14px;
		color: #ff4757;
		font-weight: 600;
	}

	.price-value {
		font-size: 18px;
		color: #ff4757;
		font-weight: 600;
		margin-right: 8px;
	}

	.original-price {
		font-size: 13px;
		color: #999999;
		text-decoration: line-through;
	}

	/* 右侧医生信息 - 在右下角 */
	.product-right {
		flex-shrink: 0;
		display: flex;
		align-items: baseline;
	}

	.doctor-name {
		font-size: 12px;
		color: #333333;
		font-weight: 500;
		background-color: transparent;
		padding: 0;
		margin-bottom: 2px;
	}

	/* 🆕 医生推荐标签样式 */
	.doctor-label {
		font-size: 11px;
		color: #4CAF50;
		background-color: #e8f5e8;
		padding: 1px 4px;
		border-radius: 2px;
		margin-left: 4px;
	}
</style>