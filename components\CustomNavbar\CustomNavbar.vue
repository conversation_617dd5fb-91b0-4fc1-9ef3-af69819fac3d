<template>
	<!--
		🧭 自定义导航栏组件模板
		这是一个可复用的导航栏组件，支持多种配置和样式
		包含：左侧菜单按钮、中间标题、右侧新建按钮等功能
		支持动态高度、背景色、字体大小等自定义配置
	-->

	<!--
		📄 导航栏外层容器
		- class="navigation-bar": 基础导航栏样式
		- :class="fontSizeClass": 动态字体大小样式类
		- :key: 强制重新渲染的键，当字体大小变化时重新渲染
		- :style: 动态设置导航栏总高度
	-->
	<view class="navigation-bar" :class="fontSizeClass" :key="fontSizeUpdateKey" :style="{ height: navbarHeight}">
		<!--
			📱 导航栏内部主体
			这是导航栏的核心区域，包含所有可见内容
			动态设置高度、顶部边距和背景色以适配不同设备
		-->
		<view class="navigation-bar__inner"
			:style="{ height: innerHeight + 'px', top: innerTop + 'px', backgroundColor: background }">

			<!--
				⬅️ 导航栏左侧区域
				通常放置返回按钮或菜单按钮
				:style: 动态设置左侧区域宽度
			-->
			<view class="navigation-bar__inner-left" :style="{ width: leftWidth + 'px'}">
				<!--
					菜单按钮显示条件
					v-if: 当back为true且showMenuButton为true时显示
					back: 是否显示返回功能
					showMenuButton: 是否显示菜单按钮
				-->
				<block v-if="back && showMenuButton">
					<!--
						菜单按钮
						@tap: 点击时触发previous函数（通常是返回上一页）
					-->
					<view @tap="previous">
						<!-- FirstUI的菜单图标 -->
						<fui-icon name="menu"></fui-icon>
					</view>
				</block>
			</view>

			<!--
				📝 导航栏中间区域
				显示页面标题或自定义内容
				- class="ug-font": 支持维吾尔文字体
				- :style: 动态设置中间区域宽度
			-->
			<view class="navigation-bar__inner-center ug-font" :style="{ width: centerWidth + 'px' }">
				<!--
					标题显示条件
					v-if="title": 当传入title属性时显示标题
				-->
				<block v-if="title">
					<!--
						标题文字
						- class="ug": 维吾尔文字体样式
						- :style: 动态设置文字颜色
						- title: 从父组件传入的标题文字
					-->
					<text class="ug" :style="{ color: color }">{{ title }}</text>
				</block>
			</view>

			<!--
				➡️ 导航栏右侧区域
				v-if="showNewButton": 当需要显示新建按钮时才渲染
			-->
			<view class="navigation-bar__inner-right" v-if="showNewButton">
				<!--
					新建按钮
					@tap: 点击时触发handleNewClick函数
					:style: 动态设置按钮的高度和宽度
				-->
				<view class="new-button" @tap="handleNewClick" :style="{ height: buttonHeight + 'px', width: buttonWidth + 'px' }">
					<!--
						新建按钮文字
						- class="ug": 维吾尔文字体样式
						- $t('home.newChat'): 从语言包获取"新建聊天"文字
					-->
					<text class="new-button-text ug">{{ $t('home.newChat') }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<!--
	🔧 JavaScript 逻辑部分
	使用 Vue 3 的 Composition API 语法
-->
<script setup>
	// ==================== 📦 导入依赖 ====================

	// Vue 3 核心功能导入
	import {
		computed,    // 计算属性
		ref,        // 响应式引用
		onMounted   // 生命周期钩子
	} from 'vue'

	// 注释掉的国际化导入（项目使用自定义国际化方案）
	// import {useI18n} from 'vue-i18n';
	// const {locale} = useI18n();

	// 状态管理导入
	import {
		useAppStore  // 应用全局状态管理
	} from '@/store/app.js';

	// 工具函数导入
	import { useFontSizePage } from '@/utils/fontSizeMixin.js';  // 字体大小控制混入
	import { t } from '@/locale/index.js';  // 国际化翻译函数

	// ==================== 📊 状态和工具初始化 ====================

	// 获取应用状态管理实例
	const appStores = useAppStore()

	// 使用字体大小功能混入
	// fontSizeClass: 当前字体大小对应的CSS类名
	// fontSizeUpdateKey: 字体大小更新时的强制重渲染键
	const { fontSizeClass, fontSizeUpdateKey } = useFontSizePage()

	// 翻译函数封装
	// 将导入的t函数封装为$t，保持与模板中使用习惯一致
	const $t = (key) => t(key);

	const props = defineProps({
		title: {
			type: String,
			required: true
		}, // 标题
		background: {
			type: String,
			default: 'transparent'
		},
		color: {
			type: String,
			default: '#000000'
		},
		back: {
			type: Boolean,
			default: false
		}, // 是否显示返回按钮
		showNewButton: {
			type: Boolean,
			default: false
		}, // 是否显示新建按钮
		showMenuButton: {
			type: Boolean,
			default: true
		}, // 是否显示菜单按钮
	})

	const emit = defineEmits(['menuClick', 'newClick'])
	//获取颜色
	const primaryColor = ref(props.color)
	const bg = ref('')
	const statusBarH = ref(0); // 状态栏高度
	const leftWidth = ref(0); // 左侧区域宽度
	const innerHeight = ref(0); // 导航栏内部高度
	const innerTop = ref(0); // 内部顶部距离
	const innerWidth = ref(0)
	const centerWidth = ref(0); // 中间区域宽度
	const buttonHeight = ref(32); // 新建按钮高度
	const buttonWidth = ref(62); // 新建按钮宽度
	// 计算导航栏总高度
	const navbarHeight = computed(() => {
		const systemInfo = uni.getDeviceInfo();
		const isIOS = (systemInfo.platform && systemInfo.platform.toLowerCase() === 'ios') || (systemInfo.system &&
			systemInfo.system.toLowerCase().includes('ios'));
		let fixedHeight = isIOS ? 48 : 46; // iOS和安卓高度差异
		return appStores.statusBarHeight ? `${statusBarH.value + fixedHeight + appStores.statusBarHeight}px` :
			`${statusBarH.value + fixedHeight}px`;
	});
	//返回上一页
	const previous = () => {
		emit('menuClick')
	}

	// 新建按钮点击
	const handleNewClick = () => {
		emit('newClick')
	}
	// 组件挂载后，动态计算各区域宽度和高度
	onMounted(() => {
		const rect = uni.getMenuButtonBoundingClientRect(); // 获取胶囊按钮信息
		console.log('胶囊按钮尺寸:', rect); // 调试信息

		innerHeight.value = rect.height + 12; // 设置导航栏内部高度
		innerTop.value = rect.top - statusBarH.value - 3; // 设置内部顶部距离

		// 设置新建按钮尺寸与胶囊按钮完全相同
		buttonHeight.value = rect.height;
		buttonWidth.value = rect.width;

		// 计算左侧宽度
		leftWidth.value = 60;

		// 计算内部宽度，新建按钮往左移动一点
		const newButtonWidth = props.showNewButton ? rect.width : 0; // 新建按钮宽度与胶囊按钮相同
		const spacing = 10; // 与胶囊按钮的间距，往左移动一点
		innerWidth.value = rect.left - spacing; // 胶囊按钮左边界往左一点

		// 计算中间区域宽度
		centerWidth.value = innerWidth.value - leftWidth.value - newButtonWidth;
	});
</script>

<style lang="scss">
	page {
		// background: linear-gradient(to bottom, #CCF2E5 0%, #B3E0FF 20%, #E0F0FF 30%, #F0F7FF 40%, white 50%, white 100%);
		// min-height: 11vh;
		// display: flex;
		// flex-direction: column;
		// align-items: center;
		// justify-content: center;
		// width: 100%;
	}

	$borer-color: v-bind(primaryColor);

	.navigation-bar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background-color: #ffffff;

		&__inner {
			position: relative;
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			padding: 0 20rpx;

			&-left {

				height: 32px;
				display: flex;
				align-items: center;
				// background-color: red;
			}

			&-center {
				font-size: 16px;
				text-align: center;
				position: relative;
				display: flex;
				align-items: center;
				justify-content: center;
				font-weight: bold;
				height: 100%;
			}

			&-right {
				position: absolute;
				right: 0;
				top: 0;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		.new-button {
			background-color: #4cd964;
			border: 1px solid #4cd964;
			border-radius: 17px;
			display: flex;
			align-items: center;
			justify-content: center;
			box-sizing: border-box;
		}

		.new-button-text {
			color: #fff;
			font-size: 13px;
			font-weight: 500;
			line-height: 1;
		}

		.change-lang {
			align-items: center;
			width: 110rpx;
			display: flex;
			border-radius: 40rpx;
			border: 2rpx solid $borer-color;
			justify-content: space-between;
			// padding: 4rpx 8rpx;
			height: 40rpx;
			font-weight: 600;
			overflow: hidden;
		}

		.lang-selected {
			color: #fff;
			background-color: $borer-color;
		}
	}

	/* 字体大小响应式样式 */
	.font-size-small {
		.navigation-bar__inner-center text {
			font-size: var(--font-size-lg) !important;
		}

		.new-button-text {
			font-size: var(--font-size-sm) !important;
		}
	}

	.font-size-medium {
		.navigation-bar__inner-center text {
			font-size: var(--font-size-xl) !important;
		}

		.new-button-text {
			font-size: var(--font-size-md) !important;
		}
	}

	.font-size-large {
		.navigation-bar__inner-center text {
			font-size: var(--font-size-2xl) !important;
		}

		.new-button-text {
			font-size: var(--font-size-lg) !important;
		}
	}
</style>