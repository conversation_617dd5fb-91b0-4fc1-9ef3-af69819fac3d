<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">字段映射测试</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">前端数字 → API字符串映射测试</text>
      
      <view class="mapping-test">
        <text class="test-subtitle">血型映射测试：</text>
        <view v-for="(test, index) in bloodTypeTests" :key="index" class="mapping-item">
          <text class="mapping-input">输入: {{ test.input }}</text>
          <text class="mapping-output">输出: {{ test.output }}</text>
          <text :class="['mapping-status', test.expected === test.output ? 'success' : 'error']">
            {{ test.expected === test.output ? '✅' : '❌' }}
          </text>
        </view>
      </view>
      
      <view class="mapping-test">
        <text class="test-subtitle">运动频率映射测试：</text>
        <view v-for="(test, index) in exerciseTests" :key="index" class="mapping-item">
          <text class="mapping-input">输入: {{ test.input }}</text>
          <text class="mapping-output">输出: {{ test.output }}</text>
          <text :class="['mapping-status', test.expected === test.output ? 'success' : 'error']">
            {{ test.expected === test.output ? '✅' : '❌' }}
          </text>
        </view>
      </view>
      
      <view class="mapping-test">
        <text class="test-subtitle">吸烟状态映射测试：</text>
        <view v-for="(test, index) in smokingTests" :key="index" class="mapping-item">
          <text class="mapping-input">输入: {{ test.input }}</text>
          <text class="mapping-output">输出: {{ test.output }}</text>
          <text :class="['mapping-status', test.expected === test.output ? 'success' : 'error']">
            {{ test.expected === test.output ? '✅' : '❌' }}
          </text>
        </view>
      </view>
      
      <view class="mapping-test">
        <text class="test-subtitle">睡眠时长映射测试：</text>
        <view v-for="(test, index) in sleepTests" :key="index" class="mapping-item">
          <text class="mapping-input">输入: {{ test.input }}</text>
          <text class="mapping-output">输出: {{ test.output }}</text>
          <text :class="['mapping-status', test.expected === test.output ? 'success' : 'error']">
            {{ test.expected === test.output ? '✅' : '❌' }}
          </text>
        </view>
      </view>
    </view>
    
    <view class="test-section">
      <button class="test-btn" @click="runCompleteTest">
        运行完整映射测试
      </button>
    </view>
    
    <view class="test-results" v-if="testResults.length > 0">
      <text class="results-title">测试结果：</text>
      <view v-for="(result, index) in testResults" :key="index" class="result-item">
        <text :class="['result-text', result.success ? 'success' : 'error']">
          {{ result.message }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const testResults = ref([])

// 血型映射测试数据
const bloodTypeTests = ref([
  { input: 0, expected: 'unknown', output: '' },
  { input: 1, expected: 'A', output: '' },
  { input: 2, expected: 'B', output: '' },
  { input: 3, expected: 'AB', output: '' },
  { input: 4, expected: 'O', output: '' }
])

// 运动频率映射测试数据
const exerciseTests = ref([
  { input: 0, expected: 'sedentary', output: '' },
  { input: 1, expected: 'light', output: '' },
  { input: 2, expected: 'regular', output: '' },
  { input: 3, expected: 'frequent', output: '' },
  { input: 4, expected: 'intense', output: '' }
])

// 吸烟状态映射测试数据
const smokingTests = ref([
  { input: 0, expected: 'never', output: '' },
  { input: 1, expected: 'current', output: '' },
  { input: 2, expected: 'quit', output: '' }
])

// 睡眠时长映射测试数据
const sleepTests = ref([
  { input: 0, expected: '7_8', output: '' },
  { input: 1, expected: 'less_6', output: '' },
  { input: 2, expected: '6_7', output: '' },
  { input: 3, expected: '8_9', output: '' },
  { input: 4, expected: 'more_9', output: '' }
])

// 映射函数（复制自EditProfile.vue）
const mapBloodType = (value) => {
  const bloodTypeMap = {
    0: 'unknown',
    1: 'A',
    2: 'B',
    3: 'AB',
    4: 'O'
  }
  return bloodTypeMap[value] || 'unknown'
}

const mapExerciseFrequency = (value) => {
  const exerciseFrequencyMap = {
    0: 'sedentary',
    1: 'light',
    2: 'regular',
    3: 'frequent',
    4: 'intense'
  }
  return exerciseFrequencyMap[value] || 'sedentary'
}

const mapSmokingStatus = (value) => {
  const smokingStatusMap = {
    0: 'never',
    1: 'current',
    2: 'quit'
  }
  return smokingStatusMap[value] || 'never'
}

const mapSleepDuration = (value) => {
  const sleepDurationMap = {
    0: '7_8',
    1: 'less_6',
    2: '6_7',
    3: '8_9',
    4: 'more_9'
  }
  return sleepDurationMap[value] || '7_8'
}

// 运行测试
const runTests = () => {
  // 测试血型映射
  bloodTypeTests.value.forEach(test => {
    test.output = mapBloodType(test.input)
  })
  
  // 测试运动频率映射
  exerciseTests.value.forEach(test => {
    test.output = mapExerciseFrequency(test.input)
  })
  
  // 测试吸烟状态映射
  smokingTests.value.forEach(test => {
    test.output = mapSmokingStatus(test.input)
  })
  
  // 测试睡眠时长映射
  sleepTests.value.forEach(test => {
    test.output = mapSleepDuration(test.input)
  })
}

// 运行完整测试
const runCompleteTest = () => {
  testResults.value = []
  
  const testData = {
    bloodType: 4,
    exerciseFrequency: 2,
    smokingHistory: 0,
    drinkingHistory: 1,
    sleepDuration: 0,
    sleepQuality: 1,
    stressLevel: 2
  }
  
  const mappedData = {
    blood_type: mapBloodType(testData.bloodType),
    exercise_frequency: mapExerciseFrequency(testData.exerciseFrequency),
    smoking_status: mapSmokingStatus(testData.smokingHistory),
    sleep_duration: mapSleepDuration(testData.sleepDuration)
  }
  
  testResults.value.push({
    success: true,
    message: `映射结果: ${JSON.stringify(mappedData, null, 2)}`
  })
  
  // 验证映射结果
  const expectedResults = {
    blood_type: 'O',
    exercise_frequency: 'regular',
    smoking_status: 'never',
    sleep_duration: '7_8'
  }
  
  let allCorrect = true
  Object.keys(expectedResults).forEach(key => {
    if (mappedData[key] !== expectedResults[key]) {
      allCorrect = false
      testResults.value.push({
        success: false,
        message: `❌ ${key}: 期望 ${expectedResults[key]}, 实际 ${mappedData[key]}`
      })
    } else {
      testResults.value.push({
        success: true,
        message: `✅ ${key}: ${mappedData[key]} (正确)`
      })
    }
  })
  
  if (allCorrect) {
    testResults.value.unshift({
      success: true,
      message: '🎉 所有字段映射测试通过！'
    })
  }
}

onMounted(() => {
  runTests()
})
</script>

<style scoped>
.test-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.test-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.mapping-test {
  margin-bottom: 30rpx;
}

.test-subtitle {
  font-size: 28rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 15rpx;
}

.mapping-item {
  display: flex;
  align-items: center;
  padding: 10rpx;
  border: 1px solid #eee;
  border-radius: 5rpx;
  margin-bottom: 10rpx;
}

.mapping-input {
  flex: 1;
  font-size: 24rpx;
  color: #333;
}

.mapping-output {
  flex: 1;
  font-size: 24rpx;
  color: #666;
}

.mapping-status {
  width: 60rpx;
  text-align: center;
  font-size: 24rpx;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.test-results {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
}

.results-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.result-item {
  margin-bottom: 10rpx;
}

.result-text {
  font-size: 26rpx;
}

.success {
  color: #52c41a;
}

.error {
  color: #ff4d4f;
}
</style>
