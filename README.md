# 健康助手小程序

## 📱 项目简介

基于uni-app框架开发的健康助手小程序，提供AI健康咨询、医生服务、产品商城等功能。

## 🚀 技术栈

- **框架**: uni-app (Vue 3 + Composition API)
- **状态管理**: Pinia
- **UI组件**: FirstUI + uni-ui
- **多语言**: 中文、英文、维吾尔文
- **样式**: SCSS

## 📦 功能特性

- ✅ 微信小程序登录
- ✅ AI健康咨询聊天
- ✅ 医生服务预约
- ✅ 产品商城购买
- ✅ 多语言国际化
- ✅ RTL布局支持
- ✅ 响应式字体系统
- ✅ 主题切换

## 🛠️ 开发环境

```bash
# 安装依赖
npm install

# 微信小程序开发
npm run dev:mp-weixin

# H5开发
npm run dev:h5

# 构建微信小程序
npm run build:mp-weixin

# 构建H5
npm run build:h5
```

## 📁 项目结构

```
HealthApp/
├── components/          # 组件库
├── pages/              # 页面
├── store/              # 状态管理
├── request/            # API请求
├── utils/              # 工具函数
├── locale/             # 国际化
└── static/             # 静态资源
```

## 🔧 Git操作

```bash
# 快速提交
npm run git:push

# 自动监控提交
npm run git:auto

# 查看状态
npm run git:status

# 查看日志
npm run git:log
```

## 📝 更新日志

- 2024-01-XX: 项目初始化
- 持续更新中...

## 👥 贡献者

- [@ablezz](https://gitee.com/ablezz)

## 📄 许可证

MIT License
