<template>
	<!--
		👤 个人中心页面模板
		这是用户的个人中心页面，包含用户信息、健康数据、功能菜单等
	-->

	<!-- 📱 顶部导航栏 -->
	<CustomNavbar :title="$t('nav.my')" background="#ffffff"></CustomNavbar>

	<!-- 📱 底部导航栏 -->
	<CustomTabbar></CustomTabbar>

	<!-- 📱 导航栏占位符，避免内容被遮挡 -->
	<view class="navbar-placeholder"></view>

	<!--
		📄 主容器
		- class="container": 基础容器样式
		- :class: 动态样式类，包括字体大小和字体类型
		- :style: 动态样式，主要用于RTL布局
		- :key: 强制重新渲染的键，当字体或语言变化时重新渲染
	-->
	<view
		class="container"
		:class="[fontSizeClass, fontClass]"
		:style="pageStyle"
		:key="`${fontSizeUpdateKey}-${i18nUpdateKey}`"
	>
		<!--
			🔐 登录区域卡片
			根据用户登录状态显示不同的内容
			:class: 当用户已登录时添加logged-in样式
		-->
		<view class="login-card" :class="{ 'logged-in': isLoggedIn }">
			<!--
				❌ 未登录状态
				v-if="!isLoggedIn": 只有当用户未登录时才显示
			-->
			<template v-if="!isLoggedIn">
				<!--
					登录提示区域
					@click: 点击时触发临时登录（演示用）
				-->
				<view class="login-area" @click="handleTempLogin">
					<!-- 默认头像容器 -->
					<view class="avatar-container">
						<!--
							默认用户图标
							src: 使用静态的用户图标
							mode="aspectFit": 图片适应模式，保持宽高比
						-->
						<image class="avatar-icon" src="/static/icon/user.svg" mode="aspectFit"></image>
					</view>
					<!-- 登录提示文字 -->
					<view class="login-text">
		  				<!-- 主标题：点击登录 -->
		  				<text class="login-title">{{ $t('user.clickToLogin') }}</text>
		  				<!-- 副标题：登录获取更多功能 -->
		  				<text class="login-subtitle">{{ $t('user.loginForMore') }}</text>
		  			</view>
				</view>
			</template>

			<!--
				✅ 已登录状态
				v-else: 当用户已登录时显示
			-->
			<template v-else>
				<view class="user-info-header" :style="{ flexDirection: appStore.isUyghur ? 'row-reverse' : 'row' }">
					<!--
						用户头像容器
						:style: 根据语言方向调整边距
					-->
					<view class="user-avatar-container" :style="{ marginRight: appStore.isUyghur ? '0' : '30rpx', marginLeft: appStore.isUyghur ? '30rpx' : '0' }">
						<!--
							用户头像
							:src: 用户头像图片路径，如果没有则使用默认头像
							mode="aspectFill": 图片填充模式，保持宽高比并填充
						-->
						<image class="user-avatar" :src="processAvatarUrl(userInfo.avatar) || '/static/icon/user.svg'" mode="aspectFill"></image>
					</view>

					<!-- 用户详细信息 -->
					<view class="user-details">
						<!-- 用户姓名 -->
						<text class="user-name">{{ userInfo.nickname}}</text>
						<!-- 用户手机号 -->
						<text class="user-phone">{{ userInfo.phone }}</text>
						<!-- 用户类型徽章 -->
						<view class="user-type-badge">
							<text class="badge-text">{{ $t('settings.normalUser') }}</text>
						</view>
					</view>

					<!--
						编辑资料按钮
						@click: 点击时跳转到编辑资料页面
					-->
					<view class="edit-profile-btn" @click="goToEditProfile">
						<text class="edit-text">{{ $t('settings.editProfile') }}</text>
					</view>
				</view>

				<!-- 📏 分割线 -->
				<view class="divider-line"></view>

				<!--
					💊 健康数据卡片
					@click: 点击时跳转到健康记录页面
				-->
				<view class="health-data-cards" @click="goToHealthRecords">
					<!--
						🔄 循环显示健康数据卡片
						v-for: 遍历健康数据卡片数组
						:key: 每个卡片的唯一标识
						:class: 动态样式类，每个卡片有不同的样式
					-->
					<view
						v-for="healthCard in healthCards"
						:key="healthCard.key"
						class="health-card"
						:class="healthCard.cardClass"
					>
						<!-- 卡片图标 -->
						<view class="card-icon" :class="healthCard.iconClass">
							<!-- 图标符号 -->
							<text class="icon-symbol">{{ healthCard.icon }}</text>
						</view>
						<!-- 卡片数值 -->
						<text class="card-value">{{ healthCard.value }}</text>
						<!-- 卡片标签 -->
						<text class="card-label">{{ $t(healthCard.labelKey) }}</text>
					</view>
				</view>
			</template>
		</view>

		<!--
			🎯 功能卡片区域（仅登录状态显示）
			v-if="isLoggedIn": 只有当用户已登录时才显示
			显示一些快捷功能的卡片
		-->
		<view v-if="isLoggedIn" class="feature-cards">
			<!--
				🔄 循环显示功能卡片
				v-for: 遍历功能卡片数组
				:key: 每个卡片的唯一标识
				@click: 点击时执行对应的功能
			-->
			<view
				v-for="featureCard in featureCards"
				:key="featureCard.key"
				class="feature-card"
				@click="featureCard.action"
			>
				<!-- 功能图标 -->
				<view class="feature-icon" :class="featureCard.iconClass">
					<!--
						FirstUI图标组件
						:name: 图标名称
						:size: 图标大小，默认60
						:color: 图标颜色
					-->
					<fui-icon
						:name="featureCard.iconName"
						:size="featureCard.iconSize || 60"
						:color="featureCard.iconColor"
						class="feature-icon-text"
					></fui-icon>
				</view>
				<!-- 功能标签 -->
				<text class="feature-label">{{ $t(featureCard.labelKey) }}</text>
			</view>
		</view>

		<!--
			📋 功能列表区域
			显示所有可用的功能菜单项
		-->
		<view class="function-list">
			<!--
				🔄 循环显示菜单项
				v-for: 遍历菜单项数组
				:key: 每个菜单项的唯一标识
				@click: 点击时执行对应的功能
			-->
			<view
				v-for="menuItem in menuItems"
				:key="menuItem.key"
				class="list-item"
				@click="menuItem.action"
			>
				<!--
					➡️ RTL状态下的左侧箭头
					v-if="appStore.isUyghur": 只有在维吾尔文模式下显示
					在从右到左的布局中，箭头显示在左侧
				-->
				<view class="item-arrow-left" v-if="appStore.isUyghur">
					<text class="arrow-icon">›</text>
				</view>

				<!-- 菜单项左侧内容 -->
				<view class="item-left">
					<!-- 菜单项图标 -->
					<view class="item-icon" :class="menuItem.iconClass"></view>
					<!-- 菜单项文字 -->
					<text class="item-text">{{ $t(menuItem.labelKey) }}</text>
				</view>

				<!--
					➡️ LTR状态下的右侧箭头
					v-if="!appStore.isUyghur": 只有在非维吾尔文模式下显示
					在从左到右的布局中，箭头显示在右侧
				-->
				<view class="item-right" v-if="!appStore.isUyghur">
					<text class="arrow-icon">›</text>
				</view>
			</view>
		</view>
	</view>

	<!-- 微信登录组件 -->
	<WeixinLogin ref="weixinLoginRef" @close="handleWeixinLoginClose" />
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import WeixinLogin from '@/components/Weixin/index.vue'
import { useUserStore } from '@/store/user.js'
import { useAppStore } from '@/store/app.js'
import { useFontSizePage } from '@/utils/fontSizeMixin.js'
import { useRTLPage } from '@/utils/rtlMixin.js'
import { t } from '@/locale/index.js'
import { getCurrentUserInfo, onUserInfoUpdated, offUserInfoUpdated } from '@/request/userSync.js'
import { processAvatarUrl } from '@/request/avatar.js'


// 使用全局应用状态
const appStore = useAppStore()
const userInfo=computed(()=>appStore.user_info)
const isLoggedIn=computed(()=>{
	return appStore.user_info.auth;
})
// 字体大小功能
const { fontSizeClass, fontSizeUpdateKey } = useFontSizePage()

// RTL布局支持
const { pageClass, pageStyle, isRTL, getFlexContainer, getListItemStyle } = useRTLPage()

// 国际化支持
const currentLanguage = ref(appStore.lang)
const i18nUpdateKey = ref(0)

// 计算字体类（合并RTL类）
const fontClass = computed(() => ({
	...pageClass.value,
	'ug': appStore.isUyghur,
	[`lang-${appStore.lang}`]: true
}))

// 翻译方法
const $t = (key) => t(key)

// 微信登录组件引用
const weixinLoginRef = ref(null)

// 是否显示用户信息填写表单
const showUserInfoForm = ref(false)

// 临时用户信息（用于填写表单）
const tempUserInfo = ref({
  name: '',
  avatar: '/static/icon/user.svg',
  code: '' // 保存微信登录code
})

// 健康数据
const healthData = ref({})

// 血型选项配置
const bloodTypeOptions = ['不清楚', 'A型', 'B型', 'AB型', 'O型']

// 计算属性 - 格式化健康数据显示
const healthDisplayData = computed(() => {
	const data = healthData.value
	return {
		weight: data.weight ? `${data.weight} kg` : '-- kg',
		height: data.height ? `${data.height} cm` : '-- cm',
		bloodType: bloodTypeOptions[data.bloodType] || '--'
	}
})

// 健康卡片配置数组
const healthCards = computed(() => [
	{
		key: 'weight',
		cardClass: 'weight-card',
		iconClass: 'weight-icon',
		icon: '⚖',
		value: healthDisplayData.value.weight,
		labelKey: 'my.weight'
	},
	{
		key: 'height',
		cardClass: 'height-card',
		iconClass: 'height-icon',
		icon: '↕',
		value: healthDisplayData.value.height,
		labelKey: 'my.height'
	},
	{
		key: 'bloodType',
		cardClass: 'blood-card',
		iconClass: 'blood-icon',
		icon: '💧',
		value: healthDisplayData.value.bloodType,
		labelKey: 'my.bloodType'
	}
])

// 功能卡片配置数组
const featureCards = computed(() => [
	{
		key: 'likes',
		iconClass: 'like-icon',
		iconName: 'fabulous',
		iconColor: '#ff4b4c',
		labelKey: 'my.myLikes',
		action: goToLikes
	},
	{
		key: 'favorites',
		iconClass: 'favorite-icon',
		iconName: 'like',
		iconColor: '#fdb801',
		labelKey: 'my.myFavorites',
		action: goToFavorites
	},
	{
		key: 'cart',
		iconClass: 'cart-icon',
		iconName: 'cart',
		iconColor: '#52c41a',
		labelKey: 'my.shoppingCart',
		action: goToCart
	}
])

// 菜单项配置数组
const menuItems = computed(() => [
	{
		key: 'orders',
		iconClass: 'order-icon',
		labelKey: 'my.orders',
		action: goToMyOrder
	},
	{
		key: 'chatHistory',
		iconClass: 'chat-icon',
		labelKey: 'my.chatHistory',
		action: goToChatHistory
	},
	{
		key: 'distribution',
		iconClass: 'distribution-icon',
		labelKey: 'my.distributionManagement',
		action: null // 没有点击事件
	},
	{
		key: 'settings',
		iconClass: 'setting-icon',
		labelKey: 'my.settings',
		action: goToSettings
	}
])

// 加载健康数据
const loadHealthData = () => {
	try {
		const userInfo = getCurrentUserInfo()
		healthData.value = userInfo
	} catch (error) {
		console.error('My页面加载健康数据失败:', error)
		healthData.value = {}
	}
}

// 用户信息更新回调
const handleUserInfoUpdate = (newUserInfo) => {
	console.log('My页面接收到用户信息更新:', newUserInfo)
	healthData.value = newUserInfo
}

// 字体大小变化处理已由useFontSizePage自动处理

// 监听用户信息更新事件
onMounted(() => {
  // 加载健康数据
  loadHealthData()
  onUserInfoUpdated(handleUserInfoUpdate)

  // 监听页面显示时的健康数据重新加载事件
  uni.$on('reloadMyHealthData', loadHealthData)

  // 监听昵称更新事件
  uni.$on('nicknameUpdated', (newNickname) => {
    console.log('My页面收到昵称更新事件:', newNickname)
    // 由于My页面使用appStore，这里主要是为了日志记录
    // 实际的状态更新会通过appStore自动同步
  })

  // 🔄 监听头像更新事件
  uni.$on('avatarUpdated', (newAvatar) => {
    console.log('My页面收到头像更新事件:', newAvatar)
    // 强制更新appStore中的头像
    const currentUserInfo = appStore.user_info
    appStore.setUserInfo({
      ...currentUserInfo,
      avatar: newAvatar
    })
  })

  // 🔄 监听用户信息更新事件
  uni.$on('userInfoUpdated', (newUserInfo) => {
    console.log('My页面收到用户信息更新事件:', newUserInfo)
    // 更新appStore
    appStore.setUserInfo({
      ...newUserInfo,
      auth: true
    })
  })

  // 🔄 监听登录成功事件
  uni.$on('loginSuccess', (userData) => {
    console.log('My页面收到登录成功事件:', userData)
    // 更新appStore
    appStore.setUserInfo({
      ...userData,
      auth: true
    })
  })

  // 🔄 监听强制刷新用户信息事件
  uni.$on('forceRefreshUserInfo', (userData) => {
    console.log('My页面收到强制刷新用户信息事件:', userData)
    // 更新appStore
    appStore.setUserInfo({
      ...userData,
      auth: true
    })
  })

  // 🔄 监听刷新My页面事件
  uni.$on('refreshMyPage', () => {
    console.log('My页面收到刷新页面事件')
    // 重新加载健康数据
    loadHealthData()
  })

  // 监听用户资料更新事件
  uni.$on('profileUpdated', (newProfileData) => {
    console.log('My页面收到用户资料更新事件:', newProfileData)
    // 重新加载健康数据
    loadHealthData()
  })

  // 监听语言变化
  uni.$on('languageChanged', (data) => {
    console.log('My页面接收到语言变化:', data.language)
    currentLanguage.value = data.language
    i18nUpdateKey.value++
  })

  // 监听字体变化
  uni.$on('languageFontChanged', (data) => {
    console.log('My页面接收到字体变化:', data.language)
    currentLanguage.value = data.language
    i18nUpdateKey.value++
  })
})

// 页面卸载时移除监听
onUnmounted(() => {
  // 移除健康数据监听
  offUserInfoUpdated(handleUserInfoUpdate)

  // 移除事件监听
  uni.$off('reloadMyHealthData', loadHealthData)
  uni.$off('avatarUpdated')
  uni.$off('nicknameUpdated')
  uni.$off('userInfoUpdated')
  uni.$off('forceRefreshUserInfo')
  uni.$off('refreshMyPage')
  uni.$off('profileUpdated')
  uni.$off('languageChanged')
  uni.$off('languageFontChanged')
})

// 页面加载时检查登录状态
onMounted(() => {
  
  // 字体大小变化监听已由useFontSizePage自动处理
})

// 页面卸载时移除监听已由useFontSizePage自动处理

// 处理登录点击（获取微信登录凭证）
const handleLoginClick = () => {
  if (!isLoggedIn.value) {
    // 获取微信登录凭证
    uni.showLoading({
      title: $t('my.loginInProgress')
    })

    uni.login({
      provider: 'weixin',
      success: function (loginRes) {
        console.log('uni.login success:', loginRes)

        if (loginRes.code) {
          // 保存code
          tempUserInfo.value.code = loginRes.code
          uni.hideLoading()
          console.log('微信登录code获取成功:', loginRes.code)
          // 注意：头像选择会通过button的chooseavatar事件触发
        } else {
          uni.hideLoading()
          uni.showToast({
            title: '登录失败，请重试',
            icon: 'none'
          })
        }
      },
      fail: function (err) {
        console.error('uni.login fail:', err)
        uni.hideLoading()
        uni.showToast({
          title: $t('my.loginFailed'),
          icon: 'none'
        })
      }
    })
  }
}

// 显示微信登录组件
const showWeixinLogin = () => {
  console.log('显示微信登录组件')
  if (weixinLoginRef.value) {
    weixinLoginRef.value.open()
  }
}

// 处理点击登录
const handleTempLogin = () => {
  console.log('点击登录，打开微信登录组件')
  showWeixinLogin()
}

// 处理微信登录组件关闭事件
const handleWeixinLoginClose = (loginSuccessFlag, userData) => {
  console.log('微信登录组件关闭，登录成功:', loginSuccessFlag, '用户数据:', userData)
  if (loginSuccessFlag && userData) {
    // 使用全局状态管理的登录成功方法
    loginSuccess(userData)

    uni.showToast({
      title: $t('my.loginSuccess'),
      icon: 'success'
    })
  } else {
    // 重新检查登录状态，以防本地存储已更新
    
  }
}

// 处理头像选择 - 选择头像后直接输入昵称
const onChooseAvatar = (e) => {
  console.log('选择头像：', e)
  const { avatarUrl } = e.detail
  if (avatarUrl) {
    tempUserInfo.value.avatar = avatarUrl
    console.log('头像已更新：', avatarUrl)

    // 头像选择完成后，直接弹出昵称输入框
    inputNickname()
  }
}

// 输入昵称
const inputNickname = () => {
  uni.showModal({
    title: $t('my.inputNickname'),
    editable: true,
    placeholderText: $t('my.pleaseInputNickname'),
    success: (res) => {
      if (res.confirm && res.content && res.content.trim()) {
        tempUserInfo.value.name = res.content.trim()

        // 昵称输入完成，直接完成登录
        completeLogin()
      } else {
        uni.showToast({
          title: $t('my.nicknameRequired'),
          icon: 'none'
        })
      }
    }
  })
}

// 完成登录
const completeLogin = () => {
  uni.showLoading({
    title: $t('my.completingLogin')
  })

  // 准备用户信息，确保昵称字段正确
  const loginUserInfo = {
    name: tempUserInfo.value.name,
    nickname: tempUserInfo.value.name, // 同时设置nickname字段
    phone: '未绑定手机号',
    avatar: tempUserInfo.value.avatar,
    code: tempUserInfo.value.code // 保存微信登录code
  }

  // 🔄 更新appStore中的用户信息（关键修复）
  appStore.setUserInfo({
    ...loginUserInfo,
    auth: true // 设置登录状态
  })

  // 保存到本地存储，确保昵称字段正确
  uni.setStorageSync('auth', true)
  uni.setStorageSync('userInfo', {
    nickname: loginUserInfo.name,
    name: loginUserInfo.name,
    phone: loginUserInfo.phone,
    avatar: loginUserInfo.avatar,
    code: loginUserInfo.code
  })

  uni.hideLoading()
  uni.showToast({
    title: $t('my.loginSuccess'),
    icon: 'success'
  })

  console.log('登录完成，用户信息：', userInfo.value)
  console.log('微信登录code：', tempUserInfo.value.code)
}

// 处理昵称输入
const onNicknameChange = (e) => {
  console.log('昵称输入：', e)
  const nickname = e.detail.value
  tempUserInfo.value.name = nickname
  console.log('昵称已更新：', nickname)
}

// 确认登录
const confirmLogin = () => {
  if (!tempUserInfo.value.name.trim()) {
    uni.showToast({
      title: $t('my.nicknameRequired'),
      icon: 'none'
    })
    return
  }

  // 显示登录中
  uni.showLoading({
    title: $t('my.completingLogin')
  })

  // 🔄 更新appStore中的用户信息（关键修复）
  const loginUserInfo = {
    name: tempUserInfo.value.name,
    nickname: tempUserInfo.value.name,
    phone: '未绑定手机号',
    avatar: tempUserInfo.value.avatar
  }

  appStore.setUserInfo({
    ...loginUserInfo,
    auth: true // 设置登录状态
  })

  // 关闭弹窗
  closePopup()

  uni.hideLoading()
  uni.showToast({
    title: $t('my.loginSuccess'),
    icon: 'success'
  })

  console.log('登录完成，用户信息：', userInfo.value)
  console.log('微信登录code：', tempUserInfo.value.code)

  // 这里可以将code和用户信息发送到后端服务器
  // sendUserInfoToServer(tempUserInfo.value.code, userInfo.value)
}

// 备选方案：使用原生方式收集用户信息
const showUserInfoActionSheet = () => {
  // 先让用户选择头像
  uni.showModal({
    title: '选择头像',
    content: '请点击确定选择您的头像',
    success: (res) => {
      if (res.confirm) {
        // 这里应该调用头像选择，但由于限制，我们使用默认头像
        tempUserInfo.value.avatar = '/static/icon/user.svg'

        // 然后让用户输入昵称
        uni.showModal({
          title: $t('my.inputNickname'),
          editable: true,
          placeholderText: $t('my.pleaseInputNickname'),
          success: (nickRes) => {
            if (nickRes.confirm && nickRes.content) {
              tempUserInfo.value.name = nickRes.content

              // 确认登录
              confirmLogin()
            } else {
              uni.showToast({
                title: $t('my.nicknameRequired'),
                icon: 'none'
              })
            }
          }
        })
      }
    }
  })
}

// 发送用户信息到服务器（可选实现）
const sendUserInfoToServer = (code, userInfo) => {
  // 根据uni-app文档，这里应该将code发送到开发者服务器
  // 服务器使用code换取session_key和openid
  uni.request({
    url: 'https://your-server.com/api/wechat-login', // 替换为您的服务器地址
    method: 'POST',
    data: {
      code: code,
      userInfo: userInfo
    },
    success: (res) => {
      console.log('发送到服务器成功:', res)
    },
    fail: (err) => {
      console.error('发送到服务器失败:', err)
    }
  })
}

// 功能卡片点击事件
const goToLikes = () => {
  uni.navigateTo({
    url: '/pages/My/MyAppreciation/MyAppreciation'
  })
}

const goToFavorites = () => {
  uni.navigateTo({
    url: '/pages/My/MyCollection/MyCollection'
  })
}

const goToCart = () => {
  uni.navigateTo({
    url: '/pages/My/ShoppingCart/ShoppingCart'
  })
}

// 跳转到我的订单页面
const goToMyOrder = () => {
  uni.navigateTo({
    url: '/pages/My/Myorder/Myorder'
  })
}

// 跳转到聊天历史
const goToChatHistory = () => {
  uni.navigateTo({
    url: '/pages/My/ChatHistory/ChatHistory'
  })
}


// 跳转到设置页面
const goToSettings = () => {
  uni.navigateTo({
    url: '/pages/My/Settings/Settings'
  })
}

// 跳转到编辑个人资料页面
const goToEditProfile = () => {
  uni.navigateTo({
    url: '/pages/My/EditProfile/EditProfile'
  })
}
// 跳转到健康档案页面
const goToHealthRecords = () => {
  uni.navigateTo({
    url: '/pages/My/HealthRecords/HealthRecords'
  })
}
</script>

<script>
// 使用uni-app的页面生命周期
export default {
	onShow() {
		// 页面显示时重新加载健康数据
		uni.$emit('reloadMyHealthData')
	}
}
</script>

<style lang="scss">
	/* 添加占位样式，高度和导航栏保持一致 */
	.navbar-placeholder {
	    height: calc(var(--status-bar-height) + 42px); /* iOS默认42px，安卓40px */
	    width: 100%;
	}
	
	/* 或者用固定高度，一般手机状态栏+导航栏约88px */
	.navbar-placeholder {
	    height: 88px;
	    width: 100%;
	}
	
	/* 主容器样式 */
	.container {
    padding: 40rpx 20rpx 180rpx 20rpx;
	
    background-color: #f3f3f3;
}
	
	/* 登录卡片样式 */
	.login-card {
    background-image: linear-gradient(to right, #e1f2e2, #acd9ae); /* 渐变背景 - 精确匹配图片色彩 */
    border-radius: 30rpx; /* 圆角 */
    padding: 40rpx;
    display: flex;
    align-items: center;
    margin-bottom: 60rpx;
	height: 180rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1); /* 最终优化阴影匹配图片柔和悬浮感 */
    transform: translateY(-2rpx); /* 精确调整上移距离实现自然悬浮效果 */
}
	
	/* 头像容器 */
	.avatar-container {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%; /* 圆形 */
    background-color: #10a85f; /* 绿色背景 */
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 30rpx;
	margin: 0 0 0 20rpx;
}
	
	/* 头像图标 */
	.avatar-icon {
	    width: 60rpx;
	    height: 60rpx;
	}
	
	/* 登录文本区域 */
	.login-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
	
	/* 登录标题 */
	.login-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #109d58;
    margin-bottom: 20rpx;
}
	
	/* 登录副标题 */
	.login-subtitle {
	    font-size: 28rpx;
	    color: #606962;
	    opacity: 0.9;
	}

	/* 登录区域样式 */
	.login-area {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		cursor: pointer;
	}

	.login-area:hover {
		opacity: 0.8;
	}

	/* 用户信息填写表单 */
	.user-info-form {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40rpx;
		width: 100%;
	}

	.form-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 40rpx;
	}

	.avatar-upload {
		margin-bottom: 40rpx;
	}

	.avatar-btn {
		background: none;
		border: none;
		padding: 0;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.form-avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		border: 4rpx solid #10a85f;
		margin-bottom: 10rpx;
	}

	.avatar-tip {
		font-size: 24rpx;
		color: #666666;
	}

	.nickname-input {
		width: 100%;
		margin-bottom: 40rpx;
	}

	.nickname-field {
		width: 100%;
		height: 80rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 10rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		background-color: rgba(255, 255, 255, 0.8);
		box-sizing: border-box;
	}

	.form-actions {
		display: flex;
		gap: 20rpx;
		width: 100%;
	}

	.confirm-btn {
		flex: 1;
		height: 80rpx;
		background-color: #10a85f;
		color: white;
		border: none;
		border-radius: 10rpx;
		font-size: 28rpx;
	}

	.cancel-btn {
		flex: 1;
		height: 80rpx;
		background-color: #f5f5f5;
		color: #666666;
		border: none;
		border-radius: 10rpx;
		font-size: 28rpx;
	}

	/* 弹窗样式 */
	.popup-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 9999;
		display: flex;
		align-items: flex-end;
		animation: fadeIn 0.3s ease-out;
	}

	.popup-content {
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		padding: 0;
		max-height: 80vh;
		width: 100%;
		animation: slideUp 0.3s ease-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}

	@keyframes slideUp {
		from {
			transform: translateY(100%);
		}
		to {
			transform: translateY(0);
		}
	}

	.popup-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 40rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.popup-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.popup-close {
		font-size: 40rpx;
		color: #999999;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.popup-body {
		padding: 40rpx;
	}

	.avatar-section, .nickname-section {
		margin-bottom: 40rpx;
	}

	.section-title {
		font-size: 28rpx;
		color: #333333;
		margin-bottom: 20rpx;
		display: block;
	}

	.popup-avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		border: 4rpx solid #10a85f;
		margin-bottom: 10rpx;
	}

	.popup-nickname-field {
		width: 100%;
		height: 80rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 10rpx;
		padding: 0 20rpx;
		font-size: 28rpx;
		background-color: #f9f9f9;
		box-sizing: border-box;
	}

	.popup-footer {
		display: flex;
		gap: 20rpx;
		padding: 30rpx 40rpx;
		border-top: 1rpx solid #f0f0f0;
		background-color: #fafafa;
	}

	.popup-cancel-btn {
		flex: 1;
		height: 80rpx;
		background-color: #f5f5f5;
		color: #666666;
		border: none;
		border-radius: 10rpx;
		font-size: 28rpx;
	}

	.popup-confirm-btn {
		flex: 1;
		height: 80rpx;
		background-color: #10a85f;
		color: white;
		border: none;
		border-radius: 10rpx;
		font-size: 28rpx;
	}
	
	/* 功能卡片区域 */
	.feature-cards {
		display: flex;
		justify-content: space-between;
		margin: 20rpx 30rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.feature-card {
		display: flex;
		flex-direction: column;
		align-items: center;
		flex: 1;
		padding: 20rpx 10rpx;
		transition: all 0.3s ease;
	}

	.feature-card:active {
		transform: scale(0.95);
		opacity: 0.8;
	}

	.feature-icon {
		width: 80rpx;
		height: 80rpx;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 15rpx;
	}

	.like-icon {
		background-color: #ffe6e6;
	}

	.favorite-icon {
		background-color: #fff4e6;
	}

	.cart-icon {
		background-color: #e6f7e6;
	}

	.feature-icon-text {
		font-size: 36rpx;
		
	}

	.feature-label {
		font-size: 26rpx;
		color: #666666;
		text-align: center;
	}

	/* 功能列表区域 */
	.function-list {
	    background-color: white;
	    border-radius: 30rpx;
	    overflow: hidden;
	}
	
	/* 列表项样式 */
	.list-item {
	    display: flex;
	    align-items: center;
	    justify-content: space-between;
	    padding: 32rpx 32rpx 50rpx 32rpx;
	    height: 160rpx;
	    border-bottom: 1rpx solid #f5f5f5; /* 底部分割线 */
	}

	.item-left {
	    display: flex;
	    align-items: center;
	    flex: 1;
	}

	.item-right {
		display: flex;
		align-items: center;
	}

	.item-arrow-left {
		display: flex;
		align-items: center;
		margin-right: 20rpx;
	}

	/* RTL布局下的列表项调整 */
	.ug.lang-ug .function-list .list-item {
	    flex-direction: row-reverse !important;
	    display: flex !important;
	    align-items: center !important;
	}

	.ug.lang-ug .function-list .item-icon {
	    margin-right: 0 !important;
	    margin-left: 30rpx !important;
	}

	/* 列表项图标容器 */
	.item-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 30rpx;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}
	
	/* 各个功能图标背景 */
	.order-icon {
    background-image: url('/static/icon/order.svg');
    background-size: contain;
    background-color: #4CD964;
}
	.share-icon {
    background-image: url('/static/icon/share.svg');
    background-size: contain;
    background-color: #FFD166;
}
	.chat-icon {
    background-image: url('/static/icon/chat.svg');
    background-size: contain;
    background-color: #60D394;
}
	.distribution-icon {
    background-image: url('/static/icon/distribution.svg');
    background-size: contain;
    background-color: #F08080;
}
	.setting-icon {
    background-image: url('/static/icon/setting.svg');
    background-size: contain;
    background-color: #D3D3D3;
}
	
	/* 列表项文本 */
	.item-text {
	    font-size: 32rpx;
	    flex: 1;
	}
	




/* 登录状态样式 */
.login-card.logged-in {
    height: auto;
    padding: 40rpx;
    flex-direction: column;
    align-items: stretch;
}

/* 用户信息头部 */
.user-info-header {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
}

/* 用户头像容器 */
.user-avatar-container {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    background-color: #10a85f;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 4rpx solid #ffffff;
    overflow: hidden;
}



/* 用户头像 */
.user-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

/* 用户详情 */
.user-details {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* 用户姓名 */
.user-name {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 8rpx;
}

/* 用户手机号 */
.user-phone {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 12rpx;
}

/* 用户类型标签 */
.user-type-badge {
    background-color: #6c5ce7;
    border-radius: 20rpx;
    padding: 6rpx 16rpx;
    align-self: flex-start;
}

.badge-text {
    color: #ffffff;
    font-size: 22rpx;
}

/* 编辑资料按钮 */
.edit-profile-btn {
    background-color: rgba(255, 255, 255, 0.3);
    border: 2rpx solid #10a85f;
    border-radius: 20rpx;
    padding: 12rpx 20rpx;
}

.edit-text {
    color: #10a85f;
    font-size: 24rpx;
}

/* 分割线 */
.divider-line {
    height: 2rpx;
    background: linear-gradient(to right, transparent, #10a85f, transparent);
    margin: 20rpx 0;
    opacity: 0.3;
}

/* 健康数据卡片容器 */
.health-data-cards {
    display: flex;
    justify-content: space-between;
    gap: 20rpx;
}

/* 健康数据卡片 */
.health-card {
    flex: 1;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 20rpx;
    padding: 30rpx 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

/* 体重卡片 */
.weight-card {
    background-color: #10a85f;
}

/* 身高卡片 */
.height-card {
    background-color: #4a90e2;
}

/* 血型卡片 */
.blood-card {
    background-color: #e74c3c;
}

/* 卡片图标 */
.card-icon {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15rpx;
}

.icon-symbol {
    font-size: 28rpx;
    color: #ffffff;
    font-weight: bold;
}

/* 卡片数值 */
.card-value {
    font-size: 28rpx;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 8rpx;
}

/* 卡片标签 */
.card-label {
    font-size: 22rpx;
    color: #ffffff;
    opacity: 0.9;
}

/* 字体大小响应式样式 */
.font-size-small {
	.login-title {
		font-size: 32rpx !important;
	}

	.login-subtitle {
		font-size: 26rpx !important;
	}

	.user-name {
		font-size: 32rpx !important;
	}

	.user-phone {
		font-size: 26rpx !important;
	}

	.badge-text {
		font-size: 22rpx !important;
	}

	.edit-text {
		font-size: 26rpx !important;
	}

	.card-value {
		font-size: 24rpx !important;
	}

	.card-label {
		font-size: 20rpx !important;
	}

	.menu-item-title {
		font-size: 28rpx !important;
	}

	.menu-item-subtitle {
		font-size: 24rpx !important;
	}

	.icon-symbol {
		font-size: 24rpx !important;
	}
}

.font-size-medium {
	.login-title {
		font-size: 36rpx !important;
	}

	.login-subtitle {
		font-size: 30rpx !important;
	}

	.user-name {
		font-size: 36rpx !important;
	}

	.user-phone {
		font-size: 30rpx !important;
	}

	.badge-text {
		font-size: 26rpx !important;
	}

	.edit-text {
		font-size: 30rpx !important;
	}

	.card-value {
		font-size: 28rpx !important;
	}

	.card-label {
		font-size: 22rpx !important;
	}

	.menu-item-title {
		font-size: 32rpx !important;
	}

	.menu-item-subtitle {
		font-size: 28rpx !important;
	}

	.icon-symbol {
		font-size: 28rpx !important;
	}
}

.font-size-large {
	.login-title {
		font-size: 40rpx !important;
	}

	.login-subtitle {
		font-size: 34rpx !important;
	}

	.user-name {
		font-size: 40rpx !important;
	}

	.user-phone {
		font-size: 34rpx !important;
	}

	.badge-text {
		font-size: 30rpx !important;
	}

	.edit-text {
		font-size: 34rpx !important;
	}

	.card-value {
		font-size: 32rpx !important;
	}

	.card-label {
		font-size: 26rpx !important;
	}

	.menu-item-title {
		font-size: 36rpx !important;
	}

	.menu-item-subtitle {
		font-size: 32rpx !important;
	}

	.icon-symbol {
		font-size: 32rpx !important;
	}
}

/* 箭头图标样式 */
.arrow-icon {
	font-size: 32rpx;
	color: #cccccc;
	font-weight: bold;
	line-height: 1;
}
</style>