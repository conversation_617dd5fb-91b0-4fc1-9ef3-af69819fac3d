// English language pack
export default {
  // Common
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    themeSwitch: 'Theme Switch',
    confirmThemeSwitch: 'Switch to the new theme mode?',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    loading: 'Loading...',
    noData: 'No Data',
    retry: 'Retry',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    submit: 'Submit',
    reset: 'Reset',
    close: 'Close',
    open: 'Open',
    select: 'Select',
    selectAll: 'Select All',
    clear: 'Clear',
    refresh: 'Refresh',
    more: 'More',
    less: 'Less',
    expand: 'Expand',
    collapse: 'Collapse',
    workingHours: 'Monday to Friday, 9:00-12:00 AM',
    productSafetyNote: '. This product has been strictly quality tested and is safe and effective. Please use according to the instructions. If you feel unwell, please consult a doctor in time.',
    // New common words
    copy: 'Copy',
    share: 'Share',
    play: 'Play',
    pause: 'Pause',
    error: 'Error',
    networkError: 'Network connection failed, please check network settings',
    permissionDenied: 'Permission denied',
    cameraPermissionRequired: 'Camera permission required',
    microphonePermissionRequired: 'Microphone permission required',
    processing: 'Processing...',
    listening: 'Listening...',
    clearHistory: 'Clear History',
    restart: 'Restart'
  },

  // Navigation and tabs
  nav: {
    home: 'Home',
    list: 'History',
    my: 'My',
    settings: 'Settings',
    profile: 'Profile',
    about: 'About',
    help: 'Help',
    feedback: 'Feedback'
  },

  // Home page
  home: {
    title: 'Health Assistant',
    welcome: 'Welcome to Health Assistant',
    welcomeTo: 'Welcome to',
    smartGuide: 'Smart Guide',
    searchPlaceholder: 'Enter your health question...',
    inputPlaceholder: 'Enter your question...',
    voiceInput: 'Voice Input',
    textInput: 'Text Input',
    send: 'Send',
    sendMessage: 'Send Message',
    aiConsultation: 'AI Consultation',
    doctorList: 'Doctor List',
    healthRecords: 'Health Records',
    quickConsult: 'Quick Consult',
    emergencyCall: 'Emergency Call',
    healthTips: 'Health Tips',
    newChat: 'New',
    holdToSpeak: 'Hold to Speak',
    releaseToSend: 'Release to Send',
    recording: 'Recording',
    releaseToCancel: 'Release to Cancel',
    slideUpToCancel: 'Slide Up to Cancel',
    continueSlideToCancel: 'Continue sliding up to cancel recording',
    recordingCancelled: 'Recording Cancelled',
    recognizingVoice: 'Recognizing voice...',
    recognitionSuccess: 'Recognition successful, sending...',
    recordingFailed: 'Recording Failed',
    unknownError: 'Unknown Error',
    permissionRequest: 'Permission Request',
    recordPermissionNeeded: 'Recording permission is required to use voice input',
    switchedToDoctor: 'Switched to {name}',
    addedDoctor: 'Added {name}',
    newChatStarted: 'New chat started',
    loadedChatWith: 'Loaded chat with {name}',
    editFeatureInDevelopment: 'Edit feature in development'
  },

  // Doctor list
  doctorList: {
    title: 'Doctor List',
    searchDoctor: 'Search Doctor',
    specialty: 'Specialty',
    experience: 'Experience',
    rating: 'Rating',
    consultation: 'Consultation',
    appointment: 'Appointment',
    online: 'Online',
    offline: 'Offline',
    busy: 'Busy',
    cardiology: 'Cardiology',
    neurology: 'Neurology',
    gastroenterology: 'Gastroenterology',
    endocrinology: 'Endocrinology',
    orthopedics: 'Orthopedics',
    dermatology: 'Dermatology',
    pediatrics: 'Pediatrics',
    gynecology: 'Gynecology',
    respiratoryMedicine: 'Respiratory Medicine',
    nephrology: 'Nephrology',
    obstetrics: 'Obstetrics & Gynecology',
    defaultDoctor: 'Dr. Li Na',
    defaultTitle: 'Doctor',
    defaultDepartment: 'Gastroenterology'
  },

  // Doctor swiper component
  doctorSwiper: {
    introduction: 'Professional Introduction',
    specialties: 'Specialties',
    experience: 'Experience',
    rating: 'Rating'
  },

  // Product list
  productList: {
    title: 'Product List',
    medicine: 'Medicine',
    healthProducts: 'Health Products',
    medicalDevices: 'Medical Devices',
    price: 'Price',
    addToCart: 'Add to Cart',
    buyNow: 'Buy Now',
    outOfStock: 'Out of Stock',
    inStock: 'In Stock',
    prescription: 'Prescription',
    otc: 'OTC',
    searchProduct: 'Search Product',
    productsCount: 'products',
    manufacturer: 'Doctor',
    originalPrice: 'Original Price'
  },

  // List page
  list: {
    // Search
    searchDoctor: 'Search Doctor',
    searchProduct: 'Search Product',

    // Doctor related
    doctor: 'Doctor',
    years: 'years',
    consult: 'Consult',
    appointment: 'Appointment',
    doctorInfo: 'Doctor Information',
    doctorName: 'Doctor Name',
    contactPhone: 'Contact Phone',
    workAddress: 'Work Address',
    callPhone: 'Call',
    close: 'Close',
    callFailed: 'Call Failed',

    // Product categories
    medicineCategory: 'Medicine',
    healthCategory: 'Health Products',

    // Popup
    popupClose: '✕'
  },

  // Product Details Page
  productDetails: {
    // Basic information
    manufacturer: 'Manufacturer: ',
    doctor: 'Doctor',
    price: 'Price: ',
    detailDescription: 'Detailed Description',

    // Bottom actions
    buyNow: 'Buy Now',

    // Buy confirmation modal
    buyConfirm: 'Purchase Confirmation',
    productName: 'Product Name: ',
    unitPrice: 'Unit Price: ',
    quantity: 'Quantity: ',
    total: 'Total: ',
    confirmTip: 'Please confirm your purchase information. Click confirm to proceed to order confirmation page.',
    cancel: 'Cancel',
    confirmBuy: 'Confirm Purchase',

    // Cart modal
    selectQuantity: 'Select Quantity',
    stock: 'Stock: ',
    confirm: 'Confirm',
    addToCartSuccess: 'Added {count} items to cart',

    // Product information
    products: {
      // Medicines
      kidneyTonic: {
        name: 'Kidney Tonic Capsules',
        description: 'Nourish kidney essence, strengthen body',
        detailDescription: 'Made with precious Chinese herbs like ginseng, deer antler, and wolfberry. Has the effect of tonifying kidney yang and replenishing essence. Suitable for symptoms caused by kidney deficiency such as weak waist and knees, mental fatigue, and decreased sexual function. Can improve kidney function and enhance physical fitness. Take 3 capsules twice daily with warm water.'
      },
      ibuprofen: {
        name: 'Ibuprofen Extended-Release Capsules',
        description: 'For relief of mild to moderate pain',
        detailDescription: 'Ibuprofen extended-release capsules are a non-steroidal anti-inflammatory drug, mainly used to relieve mild to moderate pain such as headache, joint pain, migraine, toothache, muscle pain, neuralgia, and menstrual pain. Also used for fever caused by common cold or flu.'
      },
      licorice: {
        name: 'Compound Licorice Tablets',
        description: 'For cough suppression and expectorant',
        detailDescription: 'Compound Licorice Tablets are over-the-counter cough suppressant and expectorant drugs. Used for cough suppression and expectorant. Suitable for cough and poor expectoration caused by upper respiratory tract infection, bronchitis and cold.'
      },
      vitaminC: {
        name: 'Vitamin C Tablets',
        description: 'For prevention of scurvy',
        detailDescription: 'Vitamin C tablets are used to prevent scurvy and can also be used as adjuvant treatment for various acute and chronic infectious diseases and purpura. Vitamin C participates in amino acid metabolism, neurotransmitter synthesis, collagen and tissue cell matrix synthesis.'
      },
      // Health products
      calcium: {
        name: 'Calcium Tablets',
        description: 'Calcium supplement, strengthen bones',
        detailDescription: 'Calcium is the main component of human bones and teeth, and many physiological functions also require calcium participation. Each tablet contains 600mg of calcium, using calcium carbonate as calcium source, with high absorption rate, suitable for calcium supplementation needs of all age groups.'
      },
      protein: {
        name: 'Protein Powder',
        description: 'High-quality protein supplement',
        detailDescription: 'Made with high-quality soy protein and whey protein, protein content up to 80%, containing 9 essential amino acids for human body, easy to digest and absorb. Suitable for athletes, elderly, children and other groups who need protein supplementation.'
      },
      fishOil: {
        name: 'Fish Oil Capsules',
        description: 'Rich in Omega-3 fatty acids',
        detailDescription: 'Deep sea fish oil capsules are rich in EPA and DHA, help maintain cardiovascular health, promote brain development, and improve memory. Extracted from Norwegian deep sea fish, high purity, no heavy metal contamination.'
      }
    }
  },

  // Confirm Order page
  confirmOrder: {
    // Page title
    title: 'Confirm Order',

    // Product information
    productInfo: 'Product Information',
    doctorName: 'Doctor Name: ',
    quantity: 'Quantity: ',
    subtotal: 'Subtotal: ',

    // Shipping information
    shippingInfo: 'Shipping Information',
    addressManage: 'Address Management',
    receiverName: 'Receiver Name',
    receiverPhone: 'Receiver Phone',
    region: 'Region',
    address: 'Delivery Address',
    receiverNamePlaceholder: 'Please enter receiver name',
    receiverPhonePlaceholder: 'Please enter receiver phone',
    addressPlaceholder: 'Please enter delivery address',
    defaultRegion: 'Xinjiang Uyghur Autonomous Region Urumqi Tianshan District',

    // Order amount
    orderAmount: 'Order Amount',
    productAmount: 'Product Amount',
    shippingFee: 'Shipping Fee',
    freeShipping: 'Free Shipping',
    totalAmount: 'Total Amount',
    total: 'Total: ',

    // Action buttons
    submitOrder: 'Submit Order',

    // Validation messages
    nameRequired: 'Please enter receiver name',
    phoneRequired: 'Please enter receiver phone',
    addressRequired: 'Please enter delivery address',
    orderSubmitSuccess: 'Order submitted successfully'
  },

  // Add Address page
  addAddress: {
    // Page title
    title: 'Add Address',

    // Form labels
    receiverName: 'Receiver Name',
    contactPhone: 'Contact Phone',
    region: 'Region',
    detailAddress: 'Detailed Address',
    zipCode: 'Zip Code (Optional)',
    addressTag: 'Address Tag (Optional)',
    setDefault: 'Set as Default Address',

    // Placeholders
    namePlaceholder: 'Please enter receiver name',
    phonePlaceholder: 'Please enter contact phone',
    regionPlaceholder: 'Please select region',
    addressPlaceholder: 'Please enter detailed address (street, house number, etc.)',
    zipCodePlaceholder: 'Please enter zip code',
    tagPlaceholder: 'e.g.: Home, Office, School, etc.',

    // Action buttons
    saveAddress: 'Save Address',

    // Validation messages
    nameRequired: 'Please enter receiver name',
    phoneRequired: 'Please enter contact phone',
    regionRequired: 'Please select region',
    addressRequired: 'Please enter detailed address',
    saveSuccess: 'Saved successfully'
  },

  // Edit Address page
  editAddress: {
    // Page title
    title: 'Edit Address',

    // Form labels (reuse most from addAddress)
    receiverName: 'Receiver Name',
    contactPhone: 'Contact Phone',
    region: 'Region',
    detailAddress: 'Detailed Address',
    zipCode: 'Zip Code (Optional)',
    addressTag: 'Address Tag (Optional)',
    setDefault: 'Set as Default Address',

    // Placeholders
    namePlaceholder: 'Please enter receiver name',
    phonePlaceholder: 'Please enter contact phone',
    regionPlaceholder: 'Please select region',
    addressPlaceholder: 'Please enter detailed address (street, house number, etc.)',
    zipCodePlaceholder: 'Please enter zip code',
    tagPlaceholder: 'e.g.: Home, Office, School, etc.',

    // Action buttons
    saveChanges: 'Save Changes',

    // Validation messages
    nameRequired: 'Please enter receiver name',
    phoneRequired: 'Please enter contact phone',
    regionRequired: 'Please select region',
    addressRequired: 'Please enter detailed address',
    saveSuccess: 'Saved successfully'
  },

  // Select Address page
  selectAddress: {
    // Page title
    title: 'Select Address',

    // Empty state
    noAddress: 'No delivery address',
    addAddressHint: 'Click bottom right to add address',

    // Address tags
    defaultTag: 'Default',

    // Action buttons
    edit: 'Edit',
    delete: 'Delete',

    // Delete confirmation
    deleteConfirm: 'Confirm to delete this address?',
    deleteSuccess: 'Deleted successfully'
  },

  // Doctor Details page
  doctorDetails: {
    // Page title
    title: 'Doctor Details',

    // Doctor information
    doctorBadge: 'Doctor',
    workTime: 'Mon-Fri 9:00-17:00',
    consultDoctor: 'Consult Doctor',

    // Detail sections
    doctorDetail: 'Doctor Details',
    contactPhone: 'Contact Phone',
    workAddress: 'Work Address',
    specialtyField: 'Specialty Fields',
    doctorRecommend: 'Doctor Recommendations',

    // Action buttons
    call: 'Call',
    copy: 'Copy',
    viewDetail: 'View Details',

    // Toast messages
    liked: 'Liked',
    unliked: 'Unliked',
    favorited: 'Favorited',
    unfavorited: 'Unfavorited',
    callFailed: 'Call Failed',
    addressCopied: 'Address Copied',

    // Specialty diseases
    diabetes: 'Diabetes',
    thyroidDisease: 'Thyroid Disease',
    endocrineDisorder: 'Endocrine Disorder',

    // Recommended products
    heartProtectionCapsule: 'Heart Protection Capsule',
    heartProtectionDesc: 'Natural plant extract, effectively protects cardiovascular health',
    antihypertensiveTablet: 'Antihypertensive Tablet',
    antihypertensiveDesc: 'Traditional Chinese medicine compound, gentle blood pressure reduction, no side effects'
  },

  // My page
  my: {
    title: 'My',
    profile: 'Profile',
    healthRecords: 'Health Records',
    chatHistory: 'Chat History',
    shoppingCart: 'Shopping Cart',
    orders: 'My Orders',
    addresses: 'Addresses',
    settings: 'Settings',
    help: 'Help & Feedback',
    about: 'About Us',
    logout: 'Logout',
    login: 'Login',
    register: 'Register',
    // Feature cards
    myLikes: 'My Likes',
    myFavorites: 'My Favorites',
    // Health data
    weight: 'Weight',
    height: 'Height',
    bloodType: 'Blood Type',
    // Function list
    shareApp: 'Share App',
    distributionManagement: 'Distribution Management',
    // Login related
    loginInProgress: 'Logging in...',
    loginFailed: 'Login failed, please try again',
    inputNickname: 'Input Nickname',
    pleaseInputNickname: 'Please enter your nickname',
    nicknameRequired: 'Please enter nickname',
    completingLogin: 'Completing login...',
    loginSuccess: 'Login successful'
  },

  // Chat history page
  chatHistory: {
    title: 'Chat History',
    selectYearMonth: 'Select Year Month',
    year: 'Year',
    month: 'Month',
    slideToSelect: 'Slide to select',
    noChatData: 'No chat records for this date',
    noHistory: 'No chat history',
    clearHistory: 'Clear History',
    refreshHistory: 'Refresh History',
    confirmClear: 'Confirm Clear',
    clearWarning: 'Are you sure you want to clear all chat history? This action cannot be undone.',
    historyCleared: 'History cleared',
    deleteHistory: 'Delete History',
    exportHistory: 'Export History',
    searchHistory: 'Search History',
    today: 'Today',
    yesterday: 'Yesterday',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    day: 'Day',
    selectYear: 'Select Year',
    selectMonth: 'Select Month',
    selectDate: 'Select Date',
    // Weekdays
    weekdays: {
      sunday: 'Sun',
      monday: 'Mon',
      tuesday: 'Tue',
      wednesday: 'Wed',
      thursday: 'Thu',
      friday: 'Fri',
      saturday: 'Sat'
    }
  },

  // My orders page
  myOrder: {
    title: 'My Orders',
    tabs: {
      all: 'All',
      pendingPayment: 'Pending Payment',
      pendingShipment: 'Pending Shipment',
      shipped: 'Shipped',
      completed: 'Completed'
    },
    empty: {
      noOrders: 'No orders',
      noPendingPayment: 'No pending payment orders',
      noPendingShipment: 'No pending shipment orders',
      noShipped: 'No shipped orders',
      noCompleted: 'No completed orders'
    }
  },

  // Health records page
  healthRecords: {
    title: 'Health Records',
    noHealthData: 'No health records',
    createHealthRecord: 'Create Health Record',
    basicInfo: 'Basic Information',
    allergyHistory: 'Allergy History',
    chronicDiseaseHistory: 'Chronic Disease History',
    currentMedication: 'Current Medication',
    lifestyle: 'Lifestyle',
    // Health status display
    hasAllergyDesc: 'Do you have allergies to drugs, food or other substances',
    hasChronicDiseaseDesc: 'Do you have chronic diseases',
    hasMedicationDesc: 'Are you taking medication',
    exerciseFrequencyLabel: 'Exercise Frequency',
    smokingStatusLabel: 'Smoking Status',
    drinkingStatusLabel: 'Drinking Status',
    sleepDurationLabel: 'Average Sleep Duration Per Night',
    sleepQualityLabel: 'Sleep Quality',
    stressLevelLabel: 'Recent Stress Level'
  },

  // Shopping cart page
  shoppingCart: {
    title: 'Shopping Cart',
    empty: 'Shopping cart is empty',
    emptySubtitle: 'Go pick your favorite products',
    goShopping: 'Go Shopping',
    selectAll: 'Select All',
    totalItems: 'Total {count} items',
    subtotal: 'Subtotal',
    selectedItems: 'Selected {count} items',
    total: 'Total',
    checkout: 'Checkout ({count})',
    deleteAll: 'Delete All',
    deleteConfirm: 'Confirm Delete',
    deleteItemConfirm: 'Are you sure to delete product "{name}"?',
    deleteAllConfirm: 'Are you sure to delete all selected {count} items?',
    deleteSuccess: 'Delete successful',
    deleteAllSuccess: 'Successfully deleted {count} items',
    selectItemsToDelete: 'Please select items to delete',
    selectItemsToCheckout: 'Please select items to checkout',
    checkoutItems: 'Checkout {count} items',
    delete: 'Delete'
  },

  // User related
  user: {
    profile: 'Profile',
    nickname: 'Nickname',
    avatar: 'Avatar',
    phone: 'Phone',
    email: 'Email',
    gender: 'Gender',
    birthday: 'Birthday',
    address: 'Address',
    male: 'Male',
    female: 'Female',
    unknown: 'Unknown',
    clickToLogin: 'Click to Login',
    loginForMore: 'Login for more features',
    // Edit profile page
    changeAvatar: 'Change Avatar',
    clickToChangePhoto: 'Click avatar to change photo',
    basicInfo: 'Basic Information',
    height: 'Height',
    weight: 'Weight',
    bloodType: 'Blood Type',
    location: 'Residence Address',
    locating: 'Locating',
    locate: 'Locate',
    healthInfo: 'Health Information',
    allergyHistory: 'Allergy History',
    allergyDesc: 'Do you have allergies to drugs, food or other substances',
    yes: 'Yes',
    no: 'No',
    commonAllergens: 'Common Allergens',
    // Allergens
    penicillin: 'Penicillin drugs',
    cephalosporin: 'Cephalosporin drugs',
    aspirin: 'Aspirin',
    peanuts: 'Peanuts',
    seafood: 'Seafood',
    milk: 'Milk',
    eggs: 'Eggs',
    pollenMites: 'Pollen/Dust mites',
    // Input hints
    enterHeight: 'Enter height (cm)',
    enterWeight: 'Enter weight (kg)',
    // Default address
    defaultLocation: 'Xinjiang Uyghur Autonomous Region Urumqi Tianshan District',
    // Gender options
    notSet: 'Not Set',
    // Blood type options
    unknownBloodType: 'Unknown',
    typeA: 'Type A',
    typeB: 'Type B',
    typeAB: 'Type AB',
    typeO: 'Type O',
    // Edit profile page extension
    otherAllergies: 'Other Allergic Substances',
    otherAllergiesPlaceholder: 'Please supplement other allergens',
    currentMedication: 'Current Medication',
    currentMedicationDesc: 'Are you currently taking any medications',
    medicationList: 'Medication List',
    medicationListPlaceholder: 'Please list the names, dosages and frequencies of medications you are taking',
    chronicDiseaseHistory: 'Chronic Disease History',
    chronicDiseaseDesc: 'Do you have chronic diseases',
    specificSymptoms: 'Specific Symptoms',
    hypertension: 'Hypertension',
    diabetes: 'Diabetes',
    hypertensionRange: 'Usual Blood Pressure Range',
    hypertensionPlaceholder: 'e.g. 130/85 mmHg',
    diabetesRange: 'Usual Fasting Blood Sugar Range',
    diabetesPlaceholder: 'e.g. 5.8 mmol/L',
    otherChronicDiseases: 'Other Chronic Diseases',
    otherChronicDiseasesPlaceholder: 'Please supplement other chronic diseases',
    // Medical history
    medicalHistory: 'Medical History',
    surgeryHistory: 'Surgery & Hospitalization History',
    surgeryHistoryDesc: 'Have you had any surgery or hospitalization experience in the past',
    surgeryDetails: 'Details',
    surgeryDetailsPlaceholder: 'Please describe the details of surgery or hospitalization',
    familyHistory: 'Family Medical History',
    familyHistoryDesc: 'Do immediate family members (parents, siblings, children) have the following diseases',
    heartDisease: 'Heart Disease',
    stroke: 'Stroke',
    cancer: 'Cancer',
    mentalHealth: 'Mental Health Disorders',
    otherFamilyHistory: 'Other Family Medical History',
    otherFamilyHistoryPlaceholder: 'Please supplement other family medical history',
    // Lifestyle
    lifestyle: 'Lifestyle',
    exerciseFrequency: 'Exercise Frequency',
    dietPreferences: 'Daily Dietary Preferences',
    spicy: 'Love spicy food',
    sweet: 'Love sweet food',
    salty: 'Love salty food',
    light: 'Love light food',
    oily: 'Love oily food',
    vegetarian: 'Love vegetables',
    smokingStatus: 'Smoking Status',
    drinkingStatus: 'Drinking Status',
    sleepDuration: 'Average Sleep Duration Per Night',
    sleepQuality: 'Sleep Quality',
    stressLevel: 'Recent Stress Level',
    // Women's health
    womenHealth: 'Women\'s Health',
    menopause: 'Have you reached menopause',
    menstrualCycle: 'Is your menstrual cycle regular',
    pregnancy: 'Have you ever been pregnant',
    birthCount: 'Number of births',
    // Select date
    selectDate: 'Select Date',
    save: 'Save',
    // Lifestyle options
    exerciseOptions: {
      sedentary: 'Sedentary (Basically no exercise)',
      light: 'Light exercise',
      moderate: 'Moderate exercise',
      intense: 'High-intensity exercise'
    },
    smokingOptions: {
      never: 'Never smoke',
      occasionally: 'Occasionally smoke',
      frequently: 'Frequently smoke',
      quit: 'Quit smoking'
    },
    drinkingOptions: {
      never: 'Never drink',
      occasionally: 'Occasionally drink',
      frequently: 'Frequently drink',
      quit: 'Quit drinking'
    },
    sleepOptions: {
      normal: '7-8 hours',
      short: '6-7 hours',
      long: '8-9 hours',
      veryShort: 'Less than 6 hours',
      veryLong: 'More than 9 hours'
    },
    sleepQualityOptions: {
      veryGood: 'Very good (rarely have trouble falling asleep)',
      good: 'Average',
      poor: 'Poor',
      veryPoor: 'Very poor'
    },
    stressOptions: {
      veryLow: 'Very low',
      low: 'Average',
      high: 'High',
      veryHigh: 'Very high'
    },
    // Women's health options
    menstrualCycleOptions: {
      regular: 'Regular',
      irregular: 'Irregular',
      sometimes: 'Sometimes irregular'
    },
    birthCountOptions: {
      zero: '0 times',
      one: '1 time',
      two: '2 times',
      three: '3 times',
      fourPlus: '4 times or more'
    },
    // Messages
    messages: {
      regionSelectSuccess: 'Region selection successful',
      locationPermissionTitle: 'Location Permission Request',
      locationPermissionContent: 'We need to access your location information to provide accurate address positioning services. Please enable location permission in settings',
      locationPermissionRequest: 'Location Permission Request',
      locationPermissionDesc: 'We need to access your location information to automatically fill in the address, which will help you quickly complete your personal profile setup. Your location information is only used for address conversion and will not be stored or uploaded.',
      locationPermissionDenied: 'Location permission denied',
      manualAddressInput: 'You can manually enter the address',
      locating: 'Locating...',
      locationFailed: 'Location failed',
      locationSuccess: 'Location successful',
      saving: 'Saving...',
      saveSuccess: 'Save successful',
      saveFailed: 'Save failed',
      goToSettings: 'Go to Settings',
      agree: 'Agree',
      refuse: 'Refuse'
    }
  },

  // Settings page
  settings: {
    title: 'Settings',
    language: 'Language',
    fontSize: 'Font Size',
    notifications: 'Notifications',
    privacy: 'Privacy',
    security: 'Security',
    theme: 'Theme',
    cache: 'Cache',
    version: 'Version',
    checkUpdate: 'Check Update',
    clearCache: 'Clear Cache',
    // Display settings
    display: 'Display Settings',
    darkMode: 'Dark Mode',
    darkModeDesc: 'Switch between light and dark themes',
    followSystem: 'Follow System Theme',
    followSystemDesc: 'Automatically adapt to system light/dark settings',
    // User info related
    notSetNickname: 'Nickname not set',
    notBoundPhone: 'Phone not bound',
    editProfile: 'Edit Profile',
    normalUser: 'Normal User',
    expiryTime: 'Expiry Time',
    notSet: '--',
    // Setting item descriptions
    fontSizeDesc: 'Adjust font size in app',
    languageDesc: 'Select app display language',
    helpFeedbackDesc: 'Help & Feedback',
    aboutUsDesc: 'About Us',
    selectLanguage: 'Select Language',
    selectSourceLanguage: 'Select Source Language',
    selectTargetLanguage: 'Select Target Language',
    myLanguage: 'My Language',
    theirLanguage: 'Their Language',
    sourceLanguage: 'Source',
    targetLanguage: 'Target',
    chinese: 'Chinese',
    english: 'English',
    uyghur: 'Uyghur',
    // Other settings
    other: 'Other',
    logout: 'Logout',
    logoutDesc: 'Clear login status and return to login page',
    shareApp: 'Share App',
    distributionManagement: 'Distribution Management',
    aboutDialogTitle: 'About Us',
    aboutDialogContent: 'Health Assistant v1.0.0\n\nA professional AI health guide application',
    // Popup related
    adjustFontSize: 'Adjust Font Size',
    fontPreview: 'Font Preview',
    previewText: 'This is preview text effect',
    currentSize: 'Current Size',
    aboutUs: 'About Us',
    appName: 'Health Assistant v1.0.0',
    appDesc: 'A professional AI health guide app',
    fontSizes: {
      small: 'Small',
      medium: 'Medium',
      large: 'Large',
      extraLarge: 'Extra Large'
    },
  },

  // Help & Feedback page
  helpFeedback: {
    title: 'Help & Feedback',
    feedbackNotice: 'Feedback Notice',
    noticeContent: 'We will collect your problem description and related application data (excluding sensitive information) to better solve your problem. After submission, we will contact you through the phone number you provided.',
    yourName: 'Your Name (Optional)',
    namePlaceholder: 'Please enter your name',
    yourPhone: 'Your Phone Number (For follow-up)',
    phonePlaceholder: 'Please enter 11-digit phone number',
    problemDescription: 'Please describe your problem or suggestion in detail',
    descriptionPlaceholder: 'Please describe the problem or suggestion you encountered in detail',
    includeLabel: 'Including:',
    includeItems: {
      steps: '• Specific operation steps',
      expected: '• Expected results',
      actual: '• What actually happened',
      other: '• Other relevant information'
    },
    submitFeedback: 'Submit Feedback',
    bottomNotice: 'Note: Your privacy is important to us. We will not collect your sensitive information, only necessary application data to help solve problems.',
    // Validation and prompt messages
    validation: {
      phoneError: 'Please enter a valid phone number',
      contentRequired: 'Please describe your problem or suggestion'
    },
    messages: {
      submitting: 'Submitting...',
      submitSuccess: 'Submitted successfully',
      submitFailed: 'Submission failed'
    }
  },



  // Shopping cart
  shoppingCart: {
    title: 'Shopping Cart',
    empty: 'Cart is Empty',
    selectAll: 'Select All',
    total: 'Total',
    checkout: 'Checkout',
    quantity: 'Quantity',
    remove: 'Remove',
    addMore: 'Continue Shopping',
    selectedItems: 'Selected Items',
    deleteSelected: 'Delete Selected',
    confirmDelete: 'Confirm Delete',
    deleteAllConfirm: 'Are you sure to delete all selected items?'
  },

  // Address management
  address: {
    title: 'Addresses',
    addAddress: 'Add Address',
    editAddress: 'Edit Address',
    selectAddress: 'Select Address',
    defaultAddress: 'Default Address',
    setDefault: 'Set as Default',
    name: 'Name',
    phone: 'Phone',
    region: 'Region',
    detailAddress: 'Detail Address',
    postalCode: 'Postal Code',
    saveAddress: 'Save Address',
    deleteAddress: 'Delete Address',
    confirmDeleteAddress: 'Are you sure to delete this address?'
  },

  // Camera function
  camera: {
    title: 'Camera',
    takePhoto: 'Take Photo',
    selectFromAlbum: 'Select from Album',
    retake: 'Retake',
    confirm: 'Confirm',
    flashOn: 'Flash On',
    flashOff: 'Flash Off',
    switchCamera: 'Switch Camera',
    help: 'Help',
    cameraHelp: 'Camera Help',
    helpContent: {
      takePhoto: 'Tap the camera button to take a photo',
      selectAlbum: 'Tap the album button to select existing photos',
      switchCamera: 'Tap the switch button to toggle front/back camera',
      flash: 'Tap the flash button to control flashlight'
    }
  },

  // Error messages
  error: {
    networkError: 'Network Connection Failed',
    serverError: 'Server Error',
    unknownError: 'Unknown Error',
    permissionDenied: 'Permission Denied',
    cameraPermissionDenied: 'Camera Permission Denied',
    locationPermissionDenied: 'Location Permission Denied',
    storagePermissionDenied: 'Storage Permission Denied',
    operationFailed: 'Operation Failed',
    dataLoadFailed: 'Data Load Failed',
    uploadFailed: 'Upload Failed',
    downloadFailed: 'Download Failed'
  },

  // Success messages
  success: {
    operationSuccess: 'Operation Successful',
    saveSuccess: 'Save Successful',
    deleteSuccess: 'Delete Successful',
    uploadSuccess: 'Upload Successful',
    downloadSuccess: 'Download Successful',
    updateSuccess: 'Update Successful',
    loginSuccess: 'Login Successful',
    logoutSuccess: 'Logout Successful',
    registerSuccess: 'Register Successful'
  },

  // Form validation
  validation: {
    required: 'This field is required',
    invalidEmail: 'Invalid email format',
    invalidPhone: 'Invalid phone format',
    passwordTooShort: 'Password must be at least 6 characters',
    passwordMismatch: 'Passwords do not match',
    invalidFormat: 'Invalid format',
    tooLong: 'Content too long',
    tooShort: 'Content too short'
  },

  // Camera functions
  camera: {
    title: 'Take Photo & Send',
    takePhoto: 'Take Photo',
    retake: 'Retake Photo',
    send: 'Send',
    selectArea: 'Select Send Area'
  },

  // Login related
  login: {
    changeAvatar: 'Change Avatar',
    selectAvatar: 'Select Avatar',
    nicknamePlaceholder: 'Enter your nickname',
    avatarTip: 'Click the button above to select WeChat avatar',
    experienceTip: 'For better experience, please click the avatar icon to get avatar',
    devTip: '(Development environment: If you encounter exceptions, please reselect or restart the tool)',
    loginNow: 'Login Now'
  }
}
