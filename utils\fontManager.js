// 统一的字体管理器
// 整合了项目中所有字体相关的逻辑，提供简化的字体管理

export class FontManager {
  constructor() {
    this.currentLanguage = 'zh-CN';
    this.isUyghur = false;
    this.fontSize = 'normal';
    this.theme = 'light';

    // 字体配置
    this.fontSizes = {
      small: 0.9,
      normal: 1.0,
      large: 1.1,
      'extra-large': 1.2
    };

    this.languages = {
      'zh-CN': { rtl: false, fontFamily: 'system' },
      'en': { rtl: false, fontFamily: 'system' },
      'ug': { rtl: true, fontFamily: 'uyghur' }
    };
  }

  /**
   * 获取语言配置
   * @param {string} lang 语言代码
   * @returns {object} 语言配置
   */
  getLanguageConfig(lang) {
    return this.languages[lang] || this.languages['zh-CN'];
  }

  /**
   * 是否为RTL语言
   * @param {string} lang 语言代码
   * @returns {boolean} 是否为RTL
   */
  isRTL(lang = this.currentLanguage) {
    const config = this.getLanguageConfig(lang);
    return config.rtl;
  }

  /**
   * 获取字体族
   * @param {string} lang 语言代码
   * @returns {string} 字体族
   */
  getFontFamily(lang = this.currentLanguage) {
    const config = this.getLanguageConfig(lang);

    if (config.fontFamily === 'uyghur') {
      return "'uy', 'UKIJ Tuz Tom', 'UKIJ Tuz', 'Alkatip Basma Tom', 'Alkatip Basma', 'Microsoft Uighur', sans-serif";
    }

    return "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif";
  }

  /**
   * 获取字体缩放比例
   * @param {string} size 字体大小
   * @returns {number} 缩放比例
   */
  getFontScale(size = this.fontSize) {
    return this.fontSizes[size] || 1.0;
  }

  // 设置语言字体
  setLanguageFont(language) {
    this.currentLanguage = language;
    this.isUyghur = language === 'ug';

    // 应用到页面根元素
    this.applyToPageRoot();

    // 应用到所有现有元素
    this.applyToAllElements();
  }

  /**
   * 设置字体大小
   * @param {string} size 字体大小
   */
  setFontSize(size) {
    this.fontSize = size;
    this.applyToAllElements();
  }

  /**
   * 设置主题
   * @param {string} theme 主题
   */
  setTheme(theme) {
    this.theme = theme;
    this.applyToAllElements();
  }

  /**
   * 生成字体类名
   * @param {object} options 额外选项
   * @returns {object} 类名对象
   */
  generateFontClass(options = {}) {
    const config = this.getLanguageConfig(this.currentLanguage);
    const { pageClass = '' } = options;

    return {
      // 页面自定义类
      [pageClass]: !!pageClass,

      // 语言相关类
      'ug': this.isUyghur,
      [`lang-${this.currentLanguage}`]: true,

      // RTL布局类
      'rtl': config.rtl,

      // 字体大小类
      [`font-size-${this.fontSize}`]: this.fontSize !== 'normal',

      // 主题类
      [`${this.theme}-theme`]: this.theme !== 'light'
    };
  }

  /**
   * 生成字体样式
   * @param {object} options 额外选项
   * @returns {object} 样式对象
   */
  generateFontStyle(options = {}) {
    const config = this.getLanguageConfig(this.currentLanguage);
    const scale = this.getFontScale();
    const { customStyle = {} } = options;

    const style = {
      fontFamily: this.getFontFamily(),
      direction: config.rtl ? 'rtl' : 'ltr',
      '--font-scale': scale.toString(),
      ...customStyle
    };

    return style;
  }

  // 应用到页面根元素
  applyToPageRoot() {
    try {
      // 在小程序环境中，通过事件通知页面更新
      uni.$emit('fontLanguageChanged', {
        language: this.currentLanguage,
        isUyghur: this.isUyghur,
        fontSize: this.fontSize,
        theme: this.theme,
        fontClass: this.generateFontClass(),
        fontStyle: this.generateFontStyle()
      });
    } catch (error) {
      console.warn('应用页面根元素字体失败:', error);
    }
  }

  // 应用到所有元素
  applyToAllElements() {
    try {
      // 在小程序环境中，主要通过CSS类来控制字体
      // 触发全局字体更新事件
      uni.$emit('globalFontUpdate', {
        language: this.currentLanguage,
        isUyghur: this.isUyghur,
        fontSize: this.fontSize,
        theme: this.theme,
        fontFamily: this.getFontFamily(),
        fontClass: this.generateFontClass(),
        fontStyle: this.generateFontStyle()
      });
    } catch (error) {
      console.warn('应用所有元素字体失败:', error);
    }
  }

  // 设置元素的语言类
  setElementLanguageClass(element) {
    if (!element) return;
    
    try {
      // 移除之前的语言类
      const languageClasses = ['lang-zh', 'lang-en', 'lang-ug'];
      languageClasses.forEach(cls => {
        if (element.classList && element.classList.contains(cls)) {
          element.classList.remove(cls);
        }
      });
      
      // 添加当前语言类
      const currentLangClass = `lang-${this.currentLanguage}`;
      if (element.classList) {
        element.classList.add(currentLangClass);
      }
      
      // 如果是维吾尔文，还要添加ug类
      if (this.isUyghur && element.classList) {
        element.classList.add('ug');
      }
    } catch (error) {
      console.warn('设置元素语言类失败:', error);
    }
  }

  // 为新创建的元素应用字体
  applyToNewElement(element) {
    if (this.isUyghur) {
      this.setElementLanguageClass(element);
    }
  }

  /**
   * 获取文本方向
   * @param {string} lang 语言代码
   * @returns {string} 文本方向
   */
  getTextDirection(lang = this.currentLanguage) {
    return this.isRTL(lang) ? 'rtl' : 'ltr';
  }

  /**
   * 获取文本对齐方式
   * @param {string} align 对齐方式
   * @param {string} lang 语言代码
   * @returns {string} 实际对齐方式
   */
  getTextAlign(align, lang = this.currentLanguage) {
    if (!this.isRTL(lang)) return align;

    // RTL语言下的对齐方式转换
    const alignMap = {
      'left': 'right',
      'right': 'left',
      'center': 'center',
      'justify': 'justify'
    };

    return alignMap[align] || align;
  }

  // 获取当前字体状态
  getCurrentFontState() {
    return {
      language: this.currentLanguage,
      isUyghur: this.isUyghur,
      fontSize: this.fontSize,
      theme: this.theme,
      fontFamily: this.getFontFamily(),
      fontScale: this.getFontScale(),
      isRTL: this.isRTL(),
      textDirection: this.getTextDirection(),
      fontClass: this.generateFontClass(),
      fontStyle: this.generateFontStyle()
    };
  }
}

// 创建全局字体管理器实例
export const fontManager = new FontManager();

// ==================== 便捷方法导出 ====================

/**
 * 全局字体应用函数
 * @param {string} language 语言代码
 */
export function applyGlobalFont(language) {
  fontManager.setLanguageFont(language);
}

/**
 * 设置字体大小
 * @param {string} size 字体大小
 */
export function setGlobalFontSize(size) {
  fontManager.setFontSize(size);
}

/**
 * 设置主题
 * @param {string} theme 主题
 */
export function setGlobalTheme(theme) {
  fontManager.setTheme(theme);
}

/**
 * 为元素应用维吾尔文字体的工具函数
 * @param {HTMLElement} element DOM元素
 */
export function applyUyghurFont(element) {
  if (!element) return;

  try {
    if (fontManager.isUyghur) {
      element.style.fontFamily = fontManager.getFontFamily();
      if (element.classList) {
        element.classList.add('ug');
      }
    }
  } catch (error) {
    console.warn('应用维吾尔文字体失败:', error);
  }
}

/**
 * 检查是否为维吾尔文
 * @returns {boolean} 是否为维吾尔文
 */
export function isUyghurLanguage() {
  return fontManager.isUyghur;
}

/**
 * 检查是否为RTL语言
 * @param {string} lang 语言代码
 * @returns {boolean} 是否为RTL
 */
export function isRTLLanguage(lang) {
  return fontManager.isRTL(lang);
}

/**
 * 获取当前字体状态
 * @returns {object} 字体状态
 */
export function getCurrentFontState() {
  return fontManager.getCurrentFontState();
}

/**
 * 生成字体类名
 * @param {object} options 选项
 * @returns {object} 类名对象
 */
export function generateFontClass(options = {}) {
  return fontManager.generateFontClass(options);
}

/**
 * 生成字体样式
 * @param {object} options 选项
 * @returns {object} 样式对象
 */
export function generateFontStyle(options = {}) {
  return fontManager.generateFontStyle(options);
}

// 导出便捷方法
export const {
  isUyghur,
  getFontFamily,
  getFontScale,
  getTextDirection,
  getTextAlign
} = fontManager;

// 默认导出
export default fontManager;
