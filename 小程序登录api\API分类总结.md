# API接口按页面分类总结

## 分类完成情况

✅ **已完成**：将原有的75个API接口按照小程序页面功能进行了详细分类

## 分类结果

### 16个主要页面模块及具体接口

#### 1. 用户个人中心页面 (Profile/UserCenter) - 3个接口
- `GET /applet/v1/app/profile` - 获取用户个人资料
- `POST /applet/v1/app/update_profile` - 更新用户个人资料
- `POST /applet/v1/app/upload_avatar` - 上传头像

#### 2. VIP会员页面 (VIP/Membership) - 1个接口
- `GET /applet/index/vip_list` - VIP价格列表

#### 3. 分销员管理页面 (Referrer/Distribution) - 7个接口
- `POST /applet/user/apply_ref` - 申请成为分销员
- `GET /applet/user/get_moneys` - 获取分销员收入信息
- `GET /applet/user/get_balance_r` - 获取余额变动记录
- `GET /applet/user/l_level_users` - 获取下级用户列表
- `POST /applet/user/up_dist_level` - 修改下级分销等级
- `GET /applet/user/get_poster` - 获取分销海报列表
- `GET /applet/index/dist_level` - 获取分销等级列表

#### 4. AI健康咨询聊天页面 (Chat/Consultation) - 11个接口
- `GET /applet/v1/chat/conversations` - 获取对话列表
- `GET /applet/v1/chat/conversations/{conversationId}/messages` - 获取对话消息
- `POST /applet/v1/chat/conversations/{conversationId}/messages` - 发送消息
- `POST /applet/v1/chat/conversations` - 创建新对话
- `DELETE /applet/v1/chat/conversations/{conversationId}` - 删除对话
- `PUT /applet/v1/chat/conversations/{conversationId}/title` - 更新对话标题
- `DELETE /applet/v1/chat/conversations/all` - 删除所有对话
- `POST /applet/v1/chat/upload_image` - 上传聊天图片
- `POST /applet/v1/chat/speech_to_text` - 语音转文字
- `POST /applet/v1/trans/tts` - 文本转语音
- `GET /applet/v1/trans/tts/languages` - 获取TTS支持的语言列表

#### 5. 医生列表页面 (DoctorList) - 2个接口
- `GET /applet/v1/doctors` - 获取医生列表
- `GET /applet/v1/doctors/with-interaction` - 获取包含互动状态的医生列表

#### 6. 医生详情页面 (DoctorDetail) - 7个接口
- `GET /applet/v1/doctors/{doctorId}` - 获取单个医生详情
- `POST /applet/v1/doctors/{doctorId}/like` - 点赞医生
- `DELETE /applet/v1/doctors/{doctorId}/like` - 取消点赞医生
- `POST /applet/v1/doctors/{doctorId}/favorite` - 收藏医生
- `DELETE /applet/v1/doctors/{doctorId}/favorite` - 取消收藏医生
- `GET /applet/v1/doctors/{doctorId}/status` - 获取医生互动状态
- `GET /applet/v1/products/doctor/{doctorId}` - 获取医生的产品

#### 7. 我的收藏页面 (MyFavorites) - 2个接口
- `GET /applet/v1/doctors/favorites` - 获取用户收藏的医生列表
- `GET /applet/v1/doctors/likes` - 获取用户点赞的医生列表

#### 8. 医生产品管理页面 (DoctorProductManagement) - 12个接口
- `POST /applet/v1/doctor/products` - 创建产品
- `GET /applet/v1/doctor/products` - 获取产品列表
- `GET /applet/v1/doctor/products/{productId}` - 获取产品详情
- `PUT /applet/v1/doctor/products/{productId}` - 更新产品
- `DELETE /applet/v1/doctor/products/{productId}` - 删除产品
- `GET /applet/v1/doctor/products/statistics/overview` - 获取产品统计
- `GET /applet/v1/doctor/products/orders/list` - 获取产品订单列表
- `POST /applet/v1/doctor/products/upload-image` - 单张产品图片上传
- `POST /applet/v1/doctor/products/upload-images` - 批量产品图片上传
- `GET /applet/v1/doctor/products/orders/pending-shipment` - 获待发货订单列表
- `POST /applet/v1/doctor/products/orders/{orderId}/ship` - 订单发货
- `GET /applet/v1/doctor/products/orders/{orderId}/shipping-status` - 获取订单物流状态

#### 9. 产品商城页面 (ProductMall) - 2个接口
- `GET /applet/v1/products` - 获取产品列表（用户端）
- `GET /applet/v1/products/categories` - 获取产品分类列表

#### 10. 产品详情页面 (ProductDetail) - 3个接口
- `GET /applet/v1/products/{productId}` - 获取产品详情（用户端）
- `POST /applet/v1/cart/add` - 加入购物车
- `POST /applet/v1/products/orders` - 创建订单（用户端）

#### 11. 购物车页面 (ShoppingCart) - 5个接口
- `GET /applet/v1/cart` - 获取购物车列表
- `PUT /applet/v1/cart/{cartId}` - 更新购物车商品
- `DELETE /applet/v1/cart/{cartId}` - 删除购物车商品
- `DELETE /applet/v1/cart/clear` - 清空购物车
- `DELETE /applet/v1/cart/batch` - 批量删除购物车商品

#### 12. 我的订单页面 (MyOrders) - 5个接口
- `GET /applet/v1/products/orders/my` - 获取我的订单
- `GET /applet/v1/products/orders/{orderId}` - 获取订单详情
- `POST /applet/v1/products/orders/{orderId}/cancel` - 取消订单
- `GET /applet/v1/products/orders/my/shipped` - 获取我的已发货订单
- `GET /applet/v1/products/orders/{orderId}/shipping-status` - 获取订单物流状态

#### 13. 订单支付页面 (OrderPayment) - 3个接口
- `POST /applet/v1/products/orders/{orderId}/payment` - 创建订单支付
- `GET /applet/v1/products/orders/{orderId}/payment/status` - 查询订单支付状态
- `POST /applet/v1/products/orders/{orderId}/payment/sync` - 同步订单支付状态

#### 14. 健康档案页面 (HealthProfile) - 4个接口
- `GET /applet/v1/health-profile` - 获取健康档案
- `POST /applet/v1/health-profile` - 创建/完整更新健康档案
- `PATCH /applet/v1/health-profile` - 部分更新健康档案
- `DELETE /applet/v1/health-profile` - 删除健康档案

#### 15. 地址管理页面 (AddressManagement) - 7个接口
- `POST /applet/v1/addresses` - 创建用户地址
- `GET /applet/v1/addresses` - 获取用户地址列表
- `GET /applet/v1/addresses/simple` - 获取简化地址列表
- `GET /applet/v1/addresses/{addressId}` - 获取地址详情
- `PUT /applet/v1/addresses/{addressId}` - 更新用户地址
- `DELETE /applet/v1/addresses/{addressId}` - 删除用户地址
- `POST /applet/v1/addresses/set-default` - 设置默认地址

**总计：75个API接口**

## 分类标准

1. **按功能模块分类**：根据小程序的页面功能将接口进行归类
2. **按用户角色分类**：区分普通用户、医生、管理员等不同角色的接口
3. **按业务流程分类**：将相关的业务流程接口归类到同一页面

## 分类优势

1. **便于前端开发**：开发者可以快速找到特定页面需要的所有接口
2. **提高开发效率**：减少接口查找时间，提高开发效率
3. **便于维护管理**：接口按页面组织，便于后续维护和更新
4. **清晰的文档结构**：文档结构清晰，易于阅读和理解

## 文件说明

- **原文件**：`complete-api-reference.md` - 已按页面重新组织
- **包含内容**：
  - 目录索引表
  - 16个页面模块的详细接口文档
  - 接口使用说明（认证、错误码、多语言支持）

## 使用建议

1. **前端开发时**：根据页面名称快速定位所需接口
2. **接口测试时**：按页面功能进行接口测试
3. **文档维护时**：新增接口时按页面功能进行归类
