<template>
	<!--
		👨‍⚕️ 医生资料轮播组件模板
		这是一个可滑动的医生信息展示组件，用户可以左右滑动查看不同医生的详细信息
		包含：医生头像、基本信息、专长介绍、工作经验、评分等
	-->

	<!--
		📄 轮播容器
		- class="swiper-container": 基础轮播容器样式
		- :class="fontClass": 动态字体样式类，支持多语言字体
	-->
	<view class="swiper-container" :class="fontClass">
		<!--
			🔄 轮播组件
			uni-app的swiper组件，支持左右滑动切换医生
			- :current: 当前显示的医生索引
			- @change: 滑动切换时的回调函数
			- circular: 启用循环滑动（滑到最后一个后可以继续滑到第一个）
		-->
		<swiper class="doctor-swiper" :current="currentSwiperIndex" @change="handleSwiperChange" circular>
			<!--
				🔄 轮播项目
				v-for: 循环创建每个医生的轮播项
				:key: 每个轮播项的唯一标识（使用索引）
			-->
			<swiper-item v-for="(doctor, index) in doctors" :key="index">
				<!--
					📜 整个医生卡片的滚动容器
					因为医生信息可能很长，所以需要垂直滚动
					- scroll-y="true": 启用垂直滚动
					- :show-scrollbar="false": 隐藏滚动条
					- :enhanced="true": 启用增强模式，提供更好的滚动体验
					- :bounces="true": 启用回弹效果
					- :scroll-with-animation="true": 滚动时启用动画
				-->
				<scroll-view
					class="doctor-card-scroll"
					scroll-y="true"
					:show-scrollbar="false"
					:enhanced="true"
					:bounces="true"
					:scroll-with-animation="true">

					<!-- 👨‍⚕️ 医生卡片主体 -->
					<view class="doctor-card">
						<!--
							👤 医生头像和基本信息区域
							显示医生的头像、姓名、科室等基本信息
						-->
						<view class="doctor-card-header">
							<!--
								医生头像
								:src: 医生头像图片路径，来自doctor对象的avatar属性
							-->
							<image :src="processAvatarUrl(doctor.avatar)" class="doctor-card-avatar"></image>

							<!-- 医生基本信息 -->
							<view class="doctor-card-info">
								<!-- 医生姓名行 -->
								<view class="doctor-card-name-row">
									<!-- 医生姓名 -->
									<text class="doctor-card-name">{{ doctor.name }}</text>
									<!-- AI标签，表示这是AI医生 -->
									<text class="doctor-card-tag ai-tag">AI</text>
								</view>
								<!-- 医生科室 -->
								<text class="doctor-card-title">{{ doctor.department }}</text>
							</view>
						</view>

						<!--
							📋 医生详细信息区域
							包含医生介绍、专长、经验等详细信息
						-->
						<view class="doctor-details">
							<!--
								📝 医生介绍部分
								显示医生的个人介绍和从业经历
							-->
							<view class="doctor-section">
								<!--
									部分标题
									$t('doctorSwiper.introduction'): 从语言包获取"个人介绍"文字
								-->
								<text class="section-title">{{ $t('doctorSwiper.introduction') }}</text>
								<!--
									介绍内容
									doctor.introduction: 医生的详细介绍文字
								-->
								<text class="section-content">{{ doctor.introduction }}</text>
							</view>

							<!--
								🏷️ 医生专长部分
								以标签形式显示医生的专业领域
							-->
							<view class="doctor-section">
								<!--
									部分标题
									$t('doctorSwiper.specialties'): 从语言包获取"专业领域"文字
								-->
								<text class="section-title">{{ $t('doctorSwiper.specialties') }}</text>

								<!-- 专长标签容器 -->
								<view class="tags-container">
									<!--
										🔄 循环显示专长标签
										v-for: 遍历医生的专长数组
										:key: 每个标签的唯一标识（使用索引）
									-->
									<text class="tag" v-for="(tag, i) in doctor.specialties"
										:key="i">{{ tag }}</text>
								</view>
							</view>

							<!--
								📊 医生统计数据
								显示医生的工作经验和评分
							-->
							<view class="doctor-metrics">
								<!-- 工作经验项 -->
								<view class="metric-item">
									<!-- 经验数值 -->
									<text class="metric-value">{{ doctor.experience }}</text>
									<!--
										经验标签
										$t('doctorSwiper.experience'): 从语言包获取"工作经验"文字
									-->
									<text class="metric-label">{{ $t('doctorSwiper.experience') }}</text>
								</view>
								<!-- 评分项 -->
								<view class="metric-item">
									<!-- 评分数值 -->
									<text class="metric-value">{{ doctor.rating }}</text>
									<!--
										评分标签
										$t('doctorSwiper.rating'): 从语言包获取"用户评分"文字
									-->
									<text class="metric-label">{{ $t('doctorSwiper.rating') }}</text>
								</view>
							</view>


						</view>
					</view>
				</scroll-view>
			</swiper-item>
		</swiper>
	</view>

	<!-- 轮播指示器 -->
	<view class="swiper-navigation">
		<!-- 左箭头 -->
		<view class="nav-arrow nav-arrow-left" @tap="prevSwiper">
			<text class="arrow-icon">‹</text>
		</view>

		<!-- 中间区域：医生名字和指示器 -->
		<view class="nav-center">
			<text class="doctor-name">{{ doctors[currentSwiperIndex].name }}</text>
			<view class="indicators">
				<view v-for="(item, index) in doctors" :key="index"
					:class="['indicator-dot', currentSwiperIndex === index ? 'active' : '']"
					@tap="changeSwiper(index)"></view>
			</view>
		</view>

		<!-- 右箭头 -->
		<view class="nav-arrow nav-arrow-right" @tap="nextSwiper">
			<text class="arrow-icon">›</text>
		</view>
	</view>
</template>

<script setup>
	import { ref, watch, computed } from 'vue';
	import { t } from '@/locale/index.js';
	import { useAppStore } from '@/store/app.js';
	import { processAvatarUrl } from '@/request/avatar.js';

	// 定义props
	const props = defineProps({
		doctors: {
			type: Array,
			required: true,
			default: () => []
		},
		currentSwiper: {
			type: Number,
			default: 0
		}
	});

	// 定义emits
	const emit = defineEmits(['swiperChange']);

	// 翻译函数
	const $t = (key) => t(key);

	// 字体类
	const appStore = useAppStore();
	const fontClass = computed(() => ({
		'ug': appStore.isUyghur,
		[`lang-${appStore.lang}`]: true
	}));

	// 内部状态
	const currentSwiperIndex = ref(props.currentSwiper);

	// 监听外部传入的currentSwiper变化
	watch(() => props.currentSwiper, (newVal) => {
		currentSwiperIndex.value = newVal;
	});

	// 处理轮播变化
	const handleSwiperChange = (e) => {
		currentSwiperIndex.value = e.detail.current;
		emit('swiperChange', e.detail.current);
	};

	// 切换到指定医生
	const changeSwiper = (index) => {
		currentSwiperIndex.value = index;
		emit('swiperChange', index);
	};

	// 上一个医生
	const prevSwiper = () => {
		const newIndex = currentSwiperIndex.value > 0 ? currentSwiperIndex.value - 1 : props.doctors.length - 1;
		changeSwiper(newIndex);
	};

	// 下一个医生
	const nextSwiper = () => {
		const newIndex = currentSwiperIndex.value < props.doctors.length - 1 ? currentSwiperIndex.value + 1 : 0;
		changeSwiper(newIndex);
	};
</script>

<style lang="scss" scoped>
	/* 轮播容器样式 */
	.swiper-container {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		padding: 60rpx 0 0 0;
	}

	/* 医生轮播样式 */
	.doctor-swiper {
		height: 600rpx;
		margin-bottom: 40rpx;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	/* 轮播项样式 */
	.doctor-swiper swiper-item {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 100%;
	}

	/* 整个医生卡片的滚动容器 */
	.doctor-card-scroll {
		height: 100%; /* 占满swiper-item的高度 */
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: flex-start; /* 从顶部开始对齐 */
	}

	.doctor-card {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
		min-height: 650rpx; /* 调整最小高度，适应基本内容 */
		box-sizing: border-box;
		margin: 0 auto;
		position: relative;
		width: 580rpx;
		max-width: calc(100vw - 60rpx);
		padding-bottom: 40rpx; /* 底部留白 */
	}

	.doctor-card::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 4rpx;
		// background: linear-gradient(90deg, #4cd964, #5dd974);
	}

	/* 医生卡片头部样式 */
	.doctor-card-header {
		display: flex;
		align-items: center;
		margin-bottom: 32rpx;
		padding-bottom: 24rpx;
		border-bottom: 2rpx solid #f5f5f5;
	}

	.doctor-card-avatar {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		margin-right: 24rpx;
		border: 4rpx solid transparent;
		background: linear-gradient(white, white) padding-box,
			linear-gradient(135deg, #4cd964, #5dd974) border-box;
		box-shadow: 0 4rpx 16rpx rgba(76, 217, 100, 0.15);
	}

	.doctor-card-info {
		flex: 1;
	}

	.doctor-card-name-row {
		display: flex;
		align-items: center;
		gap: 16rpx;
		margin-bottom: 8rpx;
	}

	.doctor-card-name {
		font-size: 32rpx;
		font-weight: 600;
		color: #2c3e50;
		letter-spacing: 0.3px;
	}

	.doctor-card-tag {
		&.ai-tag {
			background: linear-gradient(135deg, #4cd964, #5dd974);
			color: white;
			padding: 4rpx 12rpx;
			border-radius: 16rpx;
			font-size: 18rpx;
			font-weight: 600;
			box-shadow: 0 2rpx 8rpx rgba(76, 217, 100, 0.25);
			letter-spacing: 0.3px;
		}
	}

	.doctor-card-title {
		color: #4cd964;
		font-size: 24rpx;
		font-weight: 500;
		letter-spacing: 0.2px;
	}

	.doctor-section {
		margin-bottom: 32rpx;
	}

	.section-title {
		font-weight: 600;
		margin-bottom: 12rpx;
		color: #333333;
		font-size: 28rpx;
		letter-spacing: 0px;
	}

	.section-content {
		color: #666666;
		line-height: 1.4;
		font-size: 26rpx;
		display: block;
		word-wrap: break-word;
		white-space: normal;
		margin-top: 20rpx;
		max-width: 100%;
		letter-spacing: 0px;
		margin-bottom: 32rpx;
		gap: 40rpx;
	}

	.tags-container {
		display: flex;
		flex-wrap: wrap;
		gap: 12rpx;
	}

	.tag {
		background-color: #e8f5e8;
		color: #4cd964;
		padding: 8rpx 16rpx;
		border-radius: 24rpx;
		font-size: 20rpx;
		font-weight: 500;
		margin-top: 20rpx;
	}

	.doctor-metrics {
		display: flex;
		justify-content: flex-start;
		gap: 60rpx;
		margin-top: 28rpx;
		padding-top: 24rpx;
		border-top: 2rpx solid #f0f0f0;
	}

	.metric-item {
		display: flex;
		align-items: center;
		gap: 12rpx;
	}

	.metric-value {
		font-size: 28rpx;
		font-weight: 600;
		color: #333;
	}

	.metric-label {
		color: #999;
		font-size: 24rpx;
	}

	/* 轮播导航样式 */
	.swiper-navigation {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 30rpx;
		padding: 0 30rpx;
		margin-bottom: 40rpx;
		height: 100rpx;
		padding-top: 40rpx;
	}

	/* 箭头样式 */
	.nav-arrow {
		width: 80rpx;
		height: 80rpx;
		margin-top: 80rpx;
		border-radius: 24rpx;
		background-color: #e8f5e8;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: all 0.3s ease;
		border: 2rpx solid #bbdecd;
	}

	.nav-arrow:hover {
		background-color: #d4f0d4;
	}

	.arrow-icon {
		font-size: 40rpx;
		color: #4cd964;
		font-weight: bold;
	}

	/* 中间区域样式 */
	.nav-center {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 16rpx;
		margin: 0 20rpx;
	}

	/* 医生名字样式 */
	.doctor-name {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
		margin-bottom: 40rpx;
	}

	/* 指示器容器 */
	.indicators {
		display: flex;
		gap: 12rpx;
	}

	/* 指示器点样式 */
	.indicator-dot {
		width: 16rpx;
		height: 16rpx;
		border-radius: 50%; /* 未选中时为圆形 */
		background-color: #b4deca; /* 未选中时的浅绿色 */
		transition: all 0.3s ease;
		cursor: pointer;
	}

	.indicator-dot.active {
		width: 32rpx; /* 选中时变宽，形成椭圆 */
		height: 16rpx; /* 高度保持不变 */
		border-radius: 8rpx; /* 椭圆形圆角 */
		background-color: #109d58; /* 选中时的深绿色 */
		transform: none; /* 移除缩放效果 */
	}

	/* RTL布局支持 */
	:deep(.rtl-container) {
		.doctor-card-header {
			flex-direction: row-reverse;
		}

		.doctor-card-avatar {
			margin-right: 0;
			margin-left: 24rpx;
		}

		.doctor-card-info {
			text-align: right;
		}

		.doctor-card-name-row {
			flex-direction: row-reverse;
		}

		.tags-container {
			justify-content: flex-end;
		}

		.doctor-metrics {
			justify-content: flex-end;
		}

		.swiper-navigation {
			flex-direction: row-reverse;
		}
	}
</style>
