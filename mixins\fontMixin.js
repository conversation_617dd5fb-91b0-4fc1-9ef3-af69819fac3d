// 字体混入 - 用于自动应用维吾尔文字体
import { useAppStore } from '@/store/app.js'
import { fontManager, applyUyghurFont } from '@/utils/fontManager.js'

export default {
  data() {
    return {
      currentLanguage: '',
      isUyghur: false
    }
  },

  computed: {
    // 计算当前页面应该使用的字体类
    fontClass() {
      const appStore = useAppStore()
      return {
        'ug': appStore.isUyghur,
        [`lang-${appStore.lang}`]: true
      }
    },

    // 计算字体样式
    fontStyle() {
      const appStore = useAppStore()
      return appStore.isUyghur ? { fontFamily: 'uy' } : {}
    }
  },

  created() {
    // 初始化字体状态
    this.initFontState()
    
    // 监听语言字体变化事件
    uni.$on('languageFontChanged', this.handleLanguageFontChange)
  },

  mounted() {
    // 页面挂载后应用字体
    this.applyPageFont()
  },

  beforeDestroy() {
    // 移除事件监听
    uni.$off('languageFontChanged', this.handleLanguageFontChange)
  },

  methods: {
    // 初始化字体状态
    initFontState() {
      const appStore = useAppStore()
      this.currentLanguage = appStore.lang
      this.isUyghur = appStore.isUyghur
    },

    // 处理语言字体变化
    handleLanguageFontChange(data) {
      this.currentLanguage = data.language
      this.isUyghur = data.isUyghur
      
      // 重新应用字体
      this.$nextTick(() => {
        this.applyPageFont()
      })
    },

    // 应用页面字体
    applyPageFont() {
      if (this.isUyghur) {
        // 应用维吾尔文字体到当前页面
        this.applyUyghurFontToPage()
      }
    },

    // 应用维吾尔文字体到页面
    applyUyghurFontToPage() {
      try {
        // 使用选择器查询页面中的所有文本元素
        const query = uni.createSelectorQuery().in(this)
        
        // 需要应用字体的选择器
        const selectors = [
          'text', 'view', 'button', 'input', 'textarea', 'label',
          '.uni-input-input', '.uni-textarea-textarea', '.uni-button-text',
          '.ug' // 已经标记为维吾尔文的元素
        ]
        
        selectors.forEach(selector => {
          query.selectAll(selector).exec((res) => {
            if (res && res[0]) {
              res[0].forEach(element => {
                applyUyghurFont(element)
              })
            }
          })
        })
      } catch (error) {
        console.warn('应用维吾尔文字体到页面失败:', error)
      }
    },

    // 为特定元素应用维吾尔文字体
    applyUyghurFontToElement(element) {
      if (this.isUyghur && element) {
        applyUyghurFont(element)
      }
    },

    // 获取当前字体状态
    getFontState() {
      return {
        language: this.currentLanguage,
        isUyghur: this.isUyghur,
        fontClass: this.fontClass,
        fontStyle: this.fontStyle
      }
    }
  }
}
