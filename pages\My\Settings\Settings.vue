<template>
	<!--
		⚙️ 设置页面模板
		这是应用的设置页面，包含用户信息、系统设置等功能
	-->

	<!--
		📄 设置页面容器
		- class="settings-container": 基础设置页面样式
		- :class: 动态样式类，包括字体大小和字体类型
		- :style: 动态样式，主要用于RTL布局
		- :key: 强制重新渲染的键，当字体变化时重新渲染
	-->
	<view
		class="settings-container"
		:class="[fontSizeClass, fontClass]"
		:style="pageStyle"
		:key="fontSizeUpdateKey"
	>
		<!--
			👤 用户信息卡片
			显示用户的基本信息和登录状态
		-->
		<view class="user-card">
			<!--
				✅ 已登录状态
				v-if="isLoggedIn": 只有当用户已登录时才显示
			-->
			<view v-if="isLoggedIn" class="user-info">
				<!-- 用户头像区域 -->
				<view class="user-avatar">
					<!--
						真实用户头像
						v-if: 当用户有头像且不是默认头像时显示
						@error: 头像加载失败时的处理函数
					-->
					<image
						v-if="userInfo.avatar && userInfo.avatar !== '/static/icon/user.svg'"
						class="user-avatar-image"
						:src="processAvatarUrl(userInfo.avatar)"
						mode="aspectFill"
						@error="handleAvatarError"
					></image>
					<!--
						默认头像图标
						v-else: 当用户没有头像或头像是默认头像时显示
					-->
					<fui-icon v-else name="my" :size="80" color="#ffffff"></fui-icon>
				</view>

				<!-- 用户详细信息 -->
				<view class="user-details">
					<!--
						用户姓名
						如果用户没有设置姓名，显示"未设置昵称"
					-->
					<text class="user-name">{{ userInfo.nickname || userInfo.name || $t('settings.notSetNickname') }}</text>
					<!--
						用户手机号
						如果用户没有绑定手机号，显示"未绑定手机"
					-->
					<text class="user-phone">{{ userInfo.phone || $t('settings.notBoundPhone') }}</text>
				</view>

				<!--
					编辑按钮
					@tap: 点击时跳转到编辑资料页面
				-->
				<view class="edit-btn" @tap="editProfile">
					<!-- 编辑图标 -->
					<fui-icon name="edit" :size="32" color="#4CAF50"></fui-icon>
					<!-- 编辑文字 -->
					<text class="edit-text">{{ $t('settings.editProfile') }}</text>
				</view>
			</view>

			<!--
				❌ 未登录状态
				v-else: 当用户未登录时显示
				@tap: 点击时跳转到登录页面
			-->
			<view v-else class="login-prompt" @tap="goToLogin">
				<!-- 默认头像 -->
				<view class="user-avatar">
					<fui-icon name="my" :size="80" color="#ffffff"></fui-icon>
				</view>
				<!-- 登录提示内容 -->
				<view class="login-content">
					<!-- 登录标题 -->
					<text class="login-title">{{ $t('user.clickToLogin') }}</text>
					<!-- 登录描述 -->
					<text class="login-desc">{{ $t('user.loginForMore') }}</text>
				</view>
			</view>

			<!--
				👑 会员信息（仅登录时显示）
				v-if="isLoggedIn": 只有当用户已登录时才显示
				显示用户的会员状态和到期时间
			-->
			<view v-if="isLoggedIn" class="membership-info">
				<!-- 会员状态 -->
				<view class="membership-status">
					<!-- 状态指示点 -->
					<view class="status-dot"></view>
					<!-- 状态文字 -->
					<text class="status-text">{{ $t('settings.normalUser') }}</text>
				</view>
				<!-- 到期信息 -->
				<view class="expiry-info">
					<!-- 到期标签 -->
					<text class="expiry-label">{{ $t('settings.expiryTime') }}</text>
					<!-- 到期日期 -->
					<text class="expiry-date">{{ $t('settings.notSet') }}</text>
				</view>
			</view>
		</view>

		<!-- ⚙️ 设置选项区域 -->
		<view class="settings-section">
			<text class="section-title">{{ $t('settings.title') }}</text>

			<!-- 统一设置项 -->
			<view class="setting-item">
				<!-- 设置项循环 -->
				<view
					v-for="setting in activeSettings"
					:key="setting.key"
					class="setting-row"
					@tap="setting.action"
				>
					<view class="setting-left">
						<view class="setting-icon" :class="setting.iconClass">
							<fui-icon :name="setting.iconName" :size="48" :color="setting.iconColor"></fui-icon>
						</view>
						<view class="setting-content">
							<text class="setting-title">{{ $t(setting.titleKey) }}</text>
							<text class="setting-desc">{{ $t(setting.descKey) }}</text>
						</view>
					</view>
					<view class="setting-right">
						<!-- 开关类型 -->
						<fui-switch
							v-if="setting.type === 'switch'"
							:value="setting.value"
							@change="setting.onChange"
						></fui-switch>
						<!-- 选择类型 -->
						<template v-else-if="setting.type === 'select'">
							<text class="setting-value">{{ setting.displayValue }}</text>
							<fui-icon name="arrowright" :size="32" color="#cccccc"></fui-icon>
						</template>
						<!-- 普通点击类型 -->
						<fui-icon
							v-else
							name="arrowright"
							:size="32"
							color="#cccccc"
						></fui-icon>
					</view>
				</view>

				<!-- 修改密码 - 功能暂时注释 -->
				<!-- <view class="setting-row" @tap="changePassword">
					<view class="setting-left">
						<view class="setting-icon password-icon">
							<fui-icon name="lock" :size="48" color="#FFA726"></fui-icon>
						</view>
						<view class="setting-content">
							<text class="setting-title">修改密码</text>
							<text class="setting-desc">修改您的登录密码</text>
						</view>
					</view>
					<view class="setting-right">
						<fui-icon name="right" :size="32" color="#cccccc"></fui-icon>
					</view>
				</view> -->

				<!-- 帮助与反馈 -->
				<view class="setting-row" @tap="showHelp">
					<view class="setting-left">
						<view class="setting-icon help-icon">
							<fui-icon name="question" :size="48" color="#4CAF50"></fui-icon>
						</view>
						<view class="setting-content">
							<text class="setting-title">{{ $t('my.help') }}</text>
							<text class="setting-desc">{{ $t('settings.helpFeedbackDesc') }}</text>
						</view>
					</view>
					<view class="setting-right">
						<fui-icon name="arrowright" :size="32" color="#cccccc"></fui-icon>
					</view>
				</view>

				<!-- 关于我们 -->
				<view class="setting-row" @tap="showAbout">
					<view class="setting-left">
						<view class="setting-icon about-icon">
							<fui-icon name="info" :size="48" color="#4CAF50"></fui-icon>
						</view>
						<view class="setting-content">
							<text class="setting-title">{{ $t('my.about') }}</text>
							<text class="setting-desc">{{ $t('settings.aboutUsDesc') }}</text>
						</view>
					</view>
					<view class="setting-right">
						<fui-icon name="arrowright" :size="32" color="#cccccc"></fui-icon>
					</view>
				</view>

				<!-- 退出登录 - 功能暂时注释 -->
				<!-- <view class="setting-row logout-item" @tap="logout">
					<view class="setting-left">
						<view class="setting-icon logout-icon">
							<fui-icon name="logout" :size="48" color="#F44336"></fui-icon>
						</view>
						<view class="setting-content">
							<text class="setting-title logout-title">退出登录</text>
							<text class="setting-desc">清除登录状态并返回登录页面</text>
						</view>
					</view>
					<view class="setting-right">
						<fui-icon name="right" :size="32" color="#cccccc"></fui-icon>
					</view>
				</view> -->

			</view>
		</view>

		<!-- 微信登录组件 -->
		<WeixinLogin ref="weixinLoginRef" @close="handleWeixinLoginClose" />

		<!-- 字体大小弹出框 -->
		<uni-popup ref="fontSizePopup" type="center" :mask-click="false">
			<view class="popup-container font-size-popup" :class="fontClass">
				<view class="popup-header">
					<text class="popup-title">{{ $t('settings.adjustFontSize') }}</text>
				</view>
				<view class="popup-content">
					<view class="font-preview">
						<text class="preview-title" :class="selectedFontSize">{{ $t('settings.fontPreview') }}</text>
						<text class="preview-subtitle" :class="selectedFontSize">{{ $t('settings.previewText') }}</text>
					</view>
					<view class="font-size-options">
						<view
							class="font-option"
							:class="{ active: selectedFontSize === 'small' }"
							@tap="selectFontSize('small')"
						>
							<text class="font-text small">Aa</text>
							<text class="font-label">{{ $t('settings.fontSizes.small') }}</text>
						</view>
						<view
							class="font-option"
							:class="{ active: selectedFontSize === 'medium' }"
							@tap="selectFontSize('medium')"
						>
							<text class="font-text medium">Aa</text>
							<text class="font-label">{{ $t('settings.fontSizes.medium') }}</text>
						</view>
						<view
							class="font-option"
							:class="{ active: selectedFontSize === 'large' }"
							@tap="selectFontSize('large')"
						>
							<text class="font-text large">Aa</text>
							<text class="font-label">{{ $t('settings.fontSizes.large') }}</text>
						</view>
					</view>
					<view class="current-size">
						<text class="current-text">{{ $t('settings.currentSize') }}: {{ getFontSizeLabel(selectedFontSize) }}</text>
					</view>
				</view>
				<view class="popup-buttons">
					<view class="btn-cancel" @tap="closeFontSizePopup">
						<text class="btn-text">{{ $t('common.cancel') }}</text>
					</view>
					<view class="btn-confirm" @tap="confirmFontSize">
						<text class="btn-text">{{ $t('common.confirm') }}</text>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 语言设置弹出框 -->
		<uni-popup ref="languagePopup" type="center" :mask-click="false">
			<view class="popup-container language-popup" :class="[fontClass, { 'rtl-layout': appStore.isUyghur }]">
				<view class="popup-header">
					<text class="popup-title">{{ $t('settings.language') }}</text>
				</view>
				<view class="popup-content">
					<view class="language-options">
						<view
							v-for="lang in languageOptions"
							:key="lang.code"
							class="language-option"
							:class="{
								active: selectedLanguage === lang.code,
								'rtl-option': lang.code === 'ug'
							}"
							@tap="selectLanguage(lang.code)"
						>
							<text class="language-text" :class="{ 'rtl-text': lang.code === 'ug' }">{{ lang.label }}</text>
							<view class="check-icon" v-if="selectedLanguage === lang.code">
								<text class="check-mark">✓</text>
							</view>
						</view>
					</view>
				</view>
				<view class="popup-buttons" :class="{ 'rtl-buttons': appStore.isUyghur }">
					<view class="btn-cancel" @tap="closeLanguagePopup">
						<text class="btn-text">{{ $t('common.cancel') }}</text>
					</view>
					<view class="btn-confirm" @tap="confirmLanguage">
						<text class="btn-text">{{ $t('common.confirm') }}</text>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 关于我们弹出框 -->
		<uni-popup ref="aboutPopup" type="center" :mask-click="false">
			<view class="popup-container about-popup" :class="fontClass">
				<view class="popup-header">
					<text class="popup-title">{{ $t('settings.aboutUs') }}</text>
				</view>
				<view class="popup-content">
					<view class="about-info">
						<text class="app-name">{{ $t('settings.appName') }}</text>
						<text class="app-desc">{{ $t('settings.appDesc') }}</text>
					</view>
				</view>
				<view class="popup-buttons single-button">
					<view class="btn-confirm full-width" @tap="closeAboutPopup">
						<text class="btn-text">{{ $t('common.confirm') }}</text>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import WeixinLogin from '@/components/Weixin/index.vue'
import { useUserStore } from '@/store/user.js'
import { useAppStore } from '@/store/app.js'
import { useFontSizePage } from '@/utils/fontSizeMixin.js'
import { getAvailableLanguages, t } from '@/locale/index.js'
import { useRTLPage } from '@/utils/rtlMixin.js'
import { processAvatarUrl } from '@/request/avatar.js'


// 使用全局用户状态
const { initUserState, loginSuccess } = useUserStore()

// 使用全局应用状态
const appStore = useAppStore()
const userInfo = computed(() => appStore.user_info)
const isLoggedIn = computed(() => {
	return appStore.user_info.auth;
})

// 字体大小功能
const { fontSizeClass, fontSizeUpdateKey } = useFontSizePage()

// RTL布局支持
const { pageClass, pageStyle, isRTL } = useRTLPage()

// 字体类计算（合并RTL类）
const fontClass = computed(() => {
	return {
		...pageClass.value,
		'ug': appStore.isUyghur,
		[`lang-${appStore.lang}`]: true
	}
})

// 微信登录组件引用
const weixinLoginRef = ref(null)

// 弹出框引用
const fontSizePopup = ref(null)
const languagePopup = ref(null)
const aboutPopup = ref(null)

// 弹出框状态
const selectedFontSize = ref(appStore.fontSize || 'medium') // 从store获取当前字体大小

// 语言设置
const languageOptions = getAvailableLanguages().map(lang => ({
	code: lang.code,
	label: lang.nativeName
}))
const selectedLanguage = ref(appStore.lang) // 使用store中的当前语言

// 设置状态 - 暂时注释
// const darkMode = ref(false) // 暗色模式
// const followSystem = ref(false) // 跟随系统主题

// 页面加载时初始化用户状态
onMounted(() => {
	initUserState()

	// 监听用户信息更新事件
	uni.$on('avatarUpdated', (newAvatarUrl) => {
		console.log('Settings页面收到头像更新事件:', newAvatarUrl)
		// 更新当前页面的用户信息
		if (userInfo.value) {
			userInfo.value.avatar = newAvatarUrl
		}
	})

	// 监听昵称更新事件
	uni.$on('nicknameUpdated', (newNickname) => {
		console.log('Settings页面收到昵称更新事件:', newNickname)
		// 更新当前页面的用户信息
		if (userInfo.value) {
			userInfo.value.nickname = newNickname
			userInfo.value.name = newNickname
		}
	})

	// 监听完整用户信息更新事件
	uni.$on('userInfoUpdated', (newUserInfo) => {
		console.log('Settings页面收到用户信息更新事件:', newUserInfo)
		// 更新当前页面的用户信息
		if (newUserInfo) {
			userInfo.value = { ...userInfo.value, ...newUserInfo }
		}
	})

	// 监听语言字体变化事件
	uni.$on('languageFontChanged', (data) => {
		console.log('Settings页面收到字体变化事件:', data)
		// 强制更新组件以应用新字体
		fontSizeUpdateKey.value++
	})

	// 监听强制刷新用户信息事件
	uni.$on('forceRefreshUserInfo', (newUserInfo) => {
		console.log('Settings页面收到强制刷新事件:', newUserInfo)
		if (newUserInfo) {
			initUserState()
			console.log('Settings页面强制刷新完成:', userInfo.value)
		}
	})

	// 监听刷新Settings页面事件
	uni.$on('refreshSettingsPage', () => {
		console.log('Settings页面收到刷新页面事件')
		initUserState()
		console.log('Settings页面刷新完成:', userInfo.value)
	})
})

// 页面卸载时移除监听
onUnmounted(() => {
	uni.$off('avatarUpdated')
	uni.$off('nicknameUpdated')
	uni.$off('userInfoUpdated')
	uni.$off('languageFontChanged')
	uni.$off('forceRefreshUserInfo')
	uni.$off('refreshSettingsPage')
})

// 显示微信登录组件
const showWeixinLogin = () => {
	console.log('显示微信登录组件')
	if (weixinLoginRef.value) {
		weixinLoginRef.value.open()
	}
}

// 跳转到登录页面（使用微信登录组件）
const goToLogin = () => {
	console.log('点击登录，打开微信登录组件')
	showWeixinLogin()
}

// 处理微信登录组件关闭事件
const handleWeixinLoginClose = (loginSuccessFlag, userData) => {
	console.log('微信登录组件关闭，登录成功:', loginSuccessFlag, '用户数据:', userData)
	if (loginSuccessFlag && userData) {
		// 使用全局状态管理的登录成功方法
		loginSuccess(userData)

		uni.showToast({
			title: $t('my.loginSuccess'),
			icon: 'success'
		})
	} else {
		// 重新检查登录状态，以防本地存储已更新
		initUserState()
	}
}

// 处理头像加载错误
const handleAvatarError = () => {
	console.log('头像加载失败，使用默认头像')
}





// 编辑资料
const editProfile = () => {
	uni.navigateTo({
		url: '/pages/My/EditProfile/EditProfile'
	})
}

// 字体大小设置
const showFontSizePicker = () => {
	// 初始化选择为当前字体大小
	selectedFontSize.value = appStore.fontSize

	// 检查弹出框引用是否存在
	if (fontSizePopup.value && fontSizePopup.value.open) {
		fontSizePopup.value.open()
	} else {
		console.error('fontSizePopup 引用不存在或open方法不可用')
		uni.showToast({
			title: '弹出框加载失败',
			icon: 'none'
		})
	}
}

// 选择字体大小
const selectFontSize = (size) => {
	selectedFontSize.value = size
}

// 获取字体大小标签
const getFontSizeLabel = (size) => {
	const labels = {
		small: $t('settings.fontSizes.small'),
		medium: $t('settings.fontSizes.medium'),
		large: $t('settings.fontSizes.large')
	}
	return labels[size] || $t('settings.fontSizes.medium')
}

// 获取当前语言显示标签
const getCurrentLanguageLabel = () => {
	const langOption = languageOptions.find(lang => lang.code === appStore.lang)
	return langOption ? langOption.label : $t('settings.languages.zh-CN')
}

// 活动设置项配置数组
const activeSettings = computed(() => {
	const settings = [
		{
			key: 'fontSize',
			type: 'select',
			iconClass: 'font-size-icon',
			iconName: 'font',
			iconColor: '#42A5F5',
			titleKey: 'settings.fontSize',
			descKey: 'settings.fontSizeDesc',
			displayValue: getFontSizeLabel(appStore.fontSize),
			action: showFontSizePicker
		},
		{
			key: 'language',
			type: 'select',
			iconClass: 'language-icon',
			iconName: 'global',
			iconColor: '#4CAF50',
			titleKey: 'settings.language',
			descKey: 'settings.languageDesc',
			displayValue: getCurrentLanguageLabel(),
			action: showLanguageSettings
		}
	]

	// 可以根据条件添加其他设置项
	// 例如：如果启用了暗色模式功能
	// if (enableDarkMode) {
	//   settings.unshift({
	//     key: 'darkMode',
	//     type: 'switch',
	//     iconClass: 'dark-mode-icon',
	//     iconName: 'moon',
	//     iconColor: '#FFA726',
	//     titleKey: 'settings.darkMode',
	//     descKey: 'settings.darkModeDesc',
	//     value: darkMode.value,
	//     onChange: toggleDarkMode
	//   })
	// }

	return settings
})

// 翻译方法
const $t = (key) => {
	return t(key)
}

// 确认字体大小
const confirmFontSize = () => {
	console.log('确认字体大小:', selectedFontSize.value)

	// 应用全局字体大小
	appStore.setFontSize(selectedFontSize.value)

	uni.showToast({
		title: `${$t('settings.fontSize')}${$t('success.updateSuccess')}`,
		icon: 'success'
	})

	// 检查弹出框引用是否存在
	if (fontSizePopup.value && fontSizePopup.value.close) {
		fontSizePopup.value.close()
	} else {
		console.error('fontSizePopup 引用不存在或close方法不可用')
	}
}

// 关闭字体大小弹出框
const closeFontSizePopup = () => {
	// 重置选择到当前实际的字体大小
	selectedFontSize.value = appStore.fontSize

	// 检查弹出框引用是否存在
	if (fontSizePopup.value && fontSizePopup.value.close) {
		fontSizePopup.value.close()
	} else {
		console.error('fontSizePopup 引用不存在或close方法不可用')
	}
}

// 语言设置
const showLanguageSettings = () => {
	// 初始化选择为当前语言
	selectedLanguage.value = appStore.lang

	// 检查弹出框引用是否存在
	if (languagePopup.value && languagePopup.value.open) {
		languagePopup.value.open()
	} else {
		console.error('languagePopup 引用不存在或open方法不可用')
		uni.showToast({
			title: t('error.operationFailed'),
			icon: 'none'
		})
	}
}

// 选择语言
const selectLanguage = (lang) => {
	selectedLanguage.value = lang
	console.log('选择语言:', lang)
}

// 确认语言选择
const confirmLanguage = () => {
	console.log('确认语言选择:', selectedLanguage.value)

	// 更新store中的语言设置
	appStore.setLanguage(selectedLanguage.value)

	uni.showToast({
		title: `${t('settings.language')}${t('success.updateSuccess')}`,
		icon: 'success'
	})

	// 检查弹出框引用是否存在
	if (languagePopup.value && languagePopup.value.close) {
		languagePopup.value.close()
	} else {
		console.error('languagePopup 引用不存在或close方法不可用')
	}
}

// 关闭语言设置弹出框
const closeLanguagePopup = () => {
	// 重置选择到当前语言
	selectedLanguage.value = appStore.lang

	// 检查弹出框引用是否存在
	if (languagePopup.value && languagePopup.value.close) {
		languagePopup.value.close()
	} else {
		console.error('languagePopup 引用不存在或close方法不可用')
	}
}

// 帮助与反馈
const showHelp = () => {
	uni.navigateTo({
	  url: '/pages/My/Settings/HelpFeedback/HelpFeedback'
	})
}



// 关于我们
const showAbout = () => {
	aboutPopup.value.open()
}

// 关闭关于我们弹出框
const closeAboutPopup = () => {
	aboutPopup.value.close()
}

// 暗色模式切换 - 功能暂时注释
// const toggleDarkMode = (value) => {
// 	console.log('暗色模式:', value)
// 	uni.showToast({
// 		title: value ? '已开启暗色模式' : '已关闭暗色模式',
// 		icon: 'none'
// 	})
// }

// 跟随系统主题切换 - 功能暂时注释
// const toggleFollowSystem = (value) => {
// 	console.log('跟随系统主题:', value)
// 	uni.showToast({
// 		title: value ? '已开启跟随系统主题' : '已关闭跟随系统主题',
// 		icon: 'none'
// 	})
// }

// DPI设置 - 功能暂时注释
// const showDPISettings = () => {
// 	uni.showToast({
// 		title: 'DPI适配功能开发中',
// 		icon: 'none'
// 	})
// }

// 修改密码 - 功能暂时注释
// const changePassword = () => {
// 	uni.showToast({
// 		title: '修改密码功能开发中',
// 		icon: 'none'
// 	})
// }

// 退出登录 - 功能暂时注释
// const logout = () => {
// 	uni.showModal({
// 		title: '提示',
// 		content: '确定要退出登录吗？',
// 		success: (res) => {
// 			if (res.confirm) {
// 				// 清除登录状态
// 				uni.removeStorageSync('userInfo')
// 				uni.removeStorageSync('auth_token')
//
// 				// 重新初始化用户状态
// 				initUserState()
//
// 				uni.showToast({
// 					title: '已退出登录',
// 					icon: 'success'
// 				})
//
// 				// 可以选择跳转到登录页面
// 				// uni.reLaunch({
// 				//     url: '/pages/login/login'
// 				// })
// 			}
// 		}
// 	})
// }
</script>

<script>
// 使用uni-app的页面生命周期
export default {
	onShow() {
		// 页面显示时重新检查登录状态
		console.log('Settings页面显示，重新检查登录状态')
		// 触发状态刷新事件
		uni.$emit('refreshSettingsPage')
	}
}
</script>

<style lang="scss">
.settings-container {
	background-color: var(--fui-bg-color-grey, #f8f8f8);
	min-height: 100vh;
}

/* 导航栏 */
.navbar {
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	height: 88rpx;
	background-color: #f8f8f8;
	padding-top: 44rpx;
}

.navbar-back {
	position: absolute;
	left: 32rpx;
	top: 50%;
	transform: translateY(-50%);
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.back-icon {
	font-size: 48rpx;
	color: #333;
	font-weight: 300;
}

.navbar-title {
	font-size: 36rpx;
	color: #333;
	font-weight: 500;
}

/* 用户信息卡片 */
.user-card {
	margin: 32rpx;
	background: linear-gradient(135deg, #B8E6B8 0%, #A8D8A8 50%, #98CCA8 100%);
	border-radius: 32rpx;
	padding: 40rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 8rpx 32rpx rgba(184, 230, 184, 0.4);
	position: relative;
	overflow: hidden;
}

.user-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
	pointer-events: none;
}

.user-info {
	display: flex;
	align-items: center;
	margin-bottom: 32rpx;
	position: relative;
	z-index: 1;
}

/* 未登录状态样式 */
.login-prompt {
	display: flex;
	align-items: center;
	cursor: pointer;
	position: relative;
	z-index: 1;
	
	
}

.login-content {
	flex: 1;
	margin-left: 32rpx;
}

.login-title {
	display: block;
	font-size: 40rpx;
	color: #2E7D32;
	font-weight: 700;
	margin-bottom: 8rpx;
	text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.5);
}

.login-desc {
	display: block;
	font-size: 28rpx;
	color: #4A7C59;
	font-weight: 400;
	
}

.user-avatar {
	width: 140rpx;
	height: 140rpx;
	background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
	border-radius: 70rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 32rpx;
	overflow: hidden;
	box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.3);
	border: 4rpx solid rgba(255, 255, 255, 0.3);
	margin: 0 0 0 20rpx;
}

.avatar-text {
	font-size: 56rpx;
	color: white;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.user-avatar-image {
	width: 100%;
	height: 100%;
	border-radius: 70rpx;
	
}

.user-details {
	flex: 1;
	
}

.user-name {
	display: block;
	font-size: 40rpx;
	color: #2E7D32;
	font-weight: 700;
	margin-bottom: 8rpx;
	text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.5);
}

.user-phone {
	display: block;
	font-size: 28rpx;
	color: #4A7C59;
	font-weight: 400;
}

.edit-btn {
	width: 200rpx;
	display: flex;
	align-items: center;
	background-color:#b4dfcb ;
	border-radius:100rpx;
	padding: 18rpx 28rpx;
	border: 2rpx solid #87cca9;
}

.edit-icon {
	font-size: 28rpx;
	margin-right: 8rpx;
}

.edit-text {
	font-size: 28rpx;
	color: #4CAF50;
	font-weight: 600;
}

.membership-info {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
	border-radius: 20rpx;
	padding: 28rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.6);
	backdrop-filter: blur(10rpx);
	position: relative;
	z-index: 1;
}

.membership-status {
	display: flex;
	align-items: center;
}

.status-dot {
	width: 10rpx;
	height: 10rpx;
	background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
	border-radius: 8rpx;
	margin-right: 16rpx;
	box-shadow: 0 2rpx 6rpx rgba(76, 175, 80, 0.3);
	margin: 0 10rpx 0 10rpx;
}

.status-text {
	font-size: 28rpx;
	color: #000;
	font-weight: 600;
}

.expiry-info {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.expiry-label {
	font-size: 24rpx;
	color: #81C784;
	margin-bottom: 4rpx;
	font-weight: 400;
}

.expiry-date {
	font-size: 28rpx;
	color: #2E7D32;
	font-weight: 600;
}

/* 设置分组 */
.settings-section {
	margin-bottom: 40rpx;
	
}

.section-title {
	display: block;
	font-size: 28rpx;
	color: var(--fui-color-subtitle, #999);
	margin: 0 32rpx 16rpx 32rpx;
}

/* 设置项 */
.setting-item {
	height: 900rpx;
	background-color: #ffffff;
	margin: 0 32rpx 0 32rpx;
	border-radius: 16rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	overflow: hidden;
}

/* 设置行 */
.setting-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx 32rpx 50rpx 32rpx;
	border-bottom: 1rpx solid #f5f5f5;
	
}

.setting-row:last-child {
	border-bottom: none;
}

.setting-left {
	display: flex;
	align-items: center;
	flex: 1;
	
}

.setting-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
	font-size: 32rpx;
}

.font-size-icon {
	background-color: #E3F2FD;
}

.language-icon {
	background-color: #E8F5E8;
}

.help-icon {
	background-color: #F3E5F5;
	
}

.about-icon {
	background-color: #E3F2FD;
}

/* 暂时注释的功能样式 */
/* .dark-mode-icon {
	background-color: #FFF3E0;
}

.system-theme-icon {
	background-color: #E3F2FD;
}

.dpi-icon {
	background-color: #E8F5E8;
}

.password-icon {
	background-color: #FFF3E0;
}

.logout-icon {
	background-color: #FFEBEE;
} */

.setting-content {
	flex: 1;
	margin: 20rpx 20rpx 20rpx 0;
}

.setting-title {
	display: block;
	font-size: 32rpx;
	color: var(--fui-color-title, #333);
	margin-bottom: 8rpx;
}



.setting-desc {
	display: block;
	font-size: 24rpx;
	color: var(--fui-color-subtitle, #999);
}

.setting-right {
	display: flex;
	align-items: center;
}

.setting-value {
	font-size: 28rpx;
	color: #179958;
	margin-right: 16rpx;
	width: 120rpx;
	height: 50rpx;
	border-radius: 20rpx;
	background-color: #e8f4ee;
	display: flex;
	align-items: center;
	justify-content: center;
	
}

.arrow-icon {
	font-size: 32rpx;
	color: var(--fui-color-minor, #ccc);
	font-weight: 300;
}

/* 退出登录特殊样式 - 暂时注释 */
/* .logout-item {
	margin-top: 32rpx;
}

.logout-title {
	color: #F44336 !important;
} */

/* 弹出框样式 */
.popup-container {
	background: #ffffff;
	border-radius: 32rpx;
	width: 640rpx;
	box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
	overflow: hidden;
	position: relative;
	
}

.popup-header {
	padding: 48rpx 40rpx 32rpx 40rpx;
	// text-align: center;
}

.popup-title {

	font-size: 24rpx;
	font-weight: 600;
	color: #333333;
	
	
}

.popup-content {
	padding: 0 40rpx 40rpx 40rpx;
}

.popup-buttons {
	display: flex;
	padding: 32rpx 40rpx;
	gap: 30rpx;
	width: 340rpx;
	float: right;
	
	
}

.popup-buttons.single-button {
	
	display: block;
}

.btn-cancel,
.btn-confirm {
	
	flex: 1;
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: background-color 0.3s ease;
	border-radius: 12rpx;
	border: none;
}

.btn-cancel {
	// background: #f5f5f5;
	
	
}

.btn-cancel:active {
	background: #e0e0e0;
	
}

.btn-cancel.full-width {
	border-right: none;
	
}

.btn-confirm {
	background: #5DADE2;
	width: 140rpx;
	float: right;
}

.btn-confirm:active {
	background: #3498DB;
}

.btn-text {
	font-size: 32rpx;
	font-weight: 500;
}

.btn-cancel .btn-text {
	color: #666666;
}

.btn-confirm .btn-text {
	color: #ffffff;
}

/* 字体大小弹出框特殊样式 */
.font-size-popup {
	width: 680rpx;
}

.font-preview {
	text-align: center;
	margin-bottom: 48rpx;
	padding: 24rpx 0;
	background: #f8f9fa;
	border-radius: 16rpx;
}

.preview-title {
	font-size: 28rpx;
	color: #666666;
	font-weight: 500;
	transition: font-size 0.3s ease;
}

/* 预览文字的字体大小变化 */
.preview-title.small {
	font-size: 24rpx;
}

.preview-title.medium {
	font-size: 32rpx;
}

.preview-title.large {
	font-size: 40rpx;
}

.preview-subtitle {
	font-size: 24rpx;
	color: #999999;
	margin-top: 12rpx;
	transition: font-size 0.3s ease;
	display: block;
}

.preview-subtitle.small {
	font-size: 20rpx;
}

.preview-subtitle.medium {
	font-size: 24rpx;
}

.preview-subtitle.large {
	font-size: 28rpx;
}

.font-size-options {
	display: flex;
	justify-content: space-around;
	margin-bottom: 48rpx;
	gap: 24rpx;
}

.font-option {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 32rpx 24rpx;
	border-radius: 20rpx;
	border: 4rpx solid #e0e0e0;
	cursor: pointer;
	transition: all 0.3s ease;
	flex: 1;
	background: #ffffff;
}

.font-option.active {
	border-color: #42A5F5;
	background: rgba(66, 165, 245, 0.08);
	box-shadow: 0 4rpx 12rpx rgba(66, 165, 245, 0.2);
}

.font-text {
	margin-bottom: 16rpx;
	font-weight: 600;
	color: #333333;
}

.font-text.small {
	font-size: 40rpx;
}

.font-text.medium {
	font-size: 52rpx;
}

.font-text.large {
	font-size: 64rpx;
}

.font-label {
	font-size: 28rpx;
	color: #666666;
	font-weight: 500;
}

.current-size {
	text-align: center;
	padding: 24rpx 0;
	background: #f8f9fa;
	border-radius: 12rpx;
}

.current-text {
	font-size: 28rpx;
	color: #666666;
}

/* 语言设置弹出框特殊样式 */
.language-popup {
	width: 600rpx;
}

.language-options {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.language-option {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 36rpx 24rpx;
	border-radius: 16rpx;
	cursor: pointer;
	transition: all 0.3s ease;
	background: #ffffff;
	border: 2rpx solid transparent;
}

.language-option:hover {
	background: #f8f9fa;
}

.language-option.active {
	background: rgba(76, 175, 80, 0.08);
	border-color: rgba(76, 175, 80, 0.2);
}

.language-text {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
}

.check-icon {
	width: 48rpx;
	height: 48rpx;
	background: #4CAF50;
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
}

.check-mark {
	color: #ffffff;
	font-size: 28rpx;
	font-weight: bold;
}

/* RTL布局支持 */
.language-popup.rtl-layout {
	direction: rtl;
}

.language-option.rtl-option {
	direction: rtl;
	text-align: right;
}

.language-option.rtl-option .language-text {
	direction: rtl;
	text-align: right;
	font-family: 'uy', Arial, sans-serif;
}

.language-option.rtl-option .check-icon {
	order: -1;
	margin-right: 0;
	margin-left: 16rpx;
}

.popup-buttons.rtl-buttons {
	direction: rtl;
}

.popup-buttons.rtl-buttons .btn-cancel {
	order: 2;
}

.popup-buttons.rtl-buttons .btn-confirm {
	order: 1;
}

/* 关于我们弹出框特殊样式 */
.about-popup {
	width: 600rpx;
}

.about-info {
	// text-align: center;
	padding: 24rpx 0;
}

.app-name {
	font-size: 30rpx;
	// font-weight: 600;
	color: #333333;
	margin-bottom: 24rpx;
	display: block;
	line-height: 1.3;
}

.app-desc {
	font-size: 30rpx;
	color: #666666;
	line-height: 1.6;
	display: block;
	padding: 0 16rpx;
}

</style>
