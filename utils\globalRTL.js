/**
 * 全局RTL布局应用工具
 * 自动为整个应用添加RTL支持
 */

import { useAppStore } from '@/store/app.js'

/**
 * 初始化全局RTL支持
 */
export function initGlobalRTL() {
  const appStore = useAppStore()
  
  // 监听语言变化
  appStore.$subscribe((mutation, state) => {
    if (mutation.events && mutation.events.key === 'lang') {
      applyGlobalRTL(state.isUyghur)
    }
  })
  
  // 初始应用RTL
  applyGlobalRTL(appStore.isUyghur)
}

/**
 * 应用全局RTL样式
 * @param {boolean} isRTL 是否为RTL语言
 */
export function applyGlobalRTL(isRTL) {
  try {
    // 获取页面根元素
    const pages = getCurrentPages()
    if (pages.length === 0) return
    
    const currentPage = pages[pages.length - 1]
    if (!currentPage) return
    
    // 应用到页面根元素
    applyRTLToElement(currentPage.$el, isRTL)
    
    // 应用到body元素（如果存在）
    if (typeof document !== 'undefined') {
      applyRTLToElement(document.body, isRTL)
      applyRTLToElement(document.documentElement, isRTL)
    }
    
    console.log('全局RTL样式已应用:', isRTL ? 'RTL' : 'LTR')
  } catch (error) {
    console.warn('应用全局RTL样式失败:', error)
  }
}

/**
 * 为元素应用RTL样式
 * @param {Element} element 目标元素
 * @param {boolean} isRTL 是否为RTL
 */
function applyRTLToElement(element, isRTL) {
  if (!element || !element.style) return
  
  // 设置文本方向
  element.style.direction = isRTL ? 'rtl' : 'ltr'
  
  // 设置文本对齐（仅对根元素）
  if (element === document.body || element === document.documentElement) {
    element.style.textAlign = isRTL ? 'right' : 'left'
  }
  
  // 添加RTL类名
  if (element.classList) {
    if (isRTL) {
      element.classList.add('rtl-layout')
      element.classList.remove('ltr-layout')
    } else {
      element.classList.add('ltr-layout')
      element.classList.remove('rtl-layout')
    }
  }
}

/**
 * 为页面组件添加RTL支持
 * @param {Object} pageInstance 页面实例
 */
export function addRTLToPage(pageInstance) {
  const appStore = useAppStore()
  
  // 添加RTL相关的计算属性
  if (pageInstance && typeof pageInstance === 'object') {
    // 添加RTL状态
    pageInstance.isRTL = appStore.isUyghur
    pageInstance.textDirection = appStore.isUyghur ? 'rtl' : 'ltr'
    pageInstance.textAlign = appStore.isUyghur ? 'right' : 'left'
    
    // 添加RTL类名
    pageInstance.rtlClass = {
      'rtl-layout': appStore.isUyghur,
      'ltr-layout': !appStore.isUyghur,
      'ug': appStore.isUyghur,
      [`lang-${appStore.lang}`]: true
    }
    
    // 添加RTL样式
    pageInstance.rtlStyle = {
      direction: appStore.isUyghur ? 'rtl' : 'ltr',
      textAlign: appStore.isUyghur ? 'right' : 'left'
    }
  }
}

/**
 * 自动为所有页面添加RTL支持的混入
 */
export const globalRTLMixin = {
  data() {
    const appStore = useAppStore()
    return {
      isRTL: appStore.isUyghur,
      textDirection: appStore.isUyghur ? 'rtl' : 'ltr',
      textAlign: appStore.isUyghur ? 'right' : 'left'
    }
  },
  computed: {
    rtlClass() {
      const appStore = useAppStore()
      return {
        'rtl-layout': appStore.isUyghur,
        'ltr-layout': !appStore.isUyghur,
        'ug': appStore.isUyghur,
        [`lang-${appStore.lang}`]: true
      }
    },
    rtlStyle() {
      const appStore = useAppStore()
      return {
        direction: appStore.isUyghur ? 'rtl' : 'ltr',
        textAlign: appStore.isUyghur ? 'right' : 'left'
      }
    }
  },
  mounted() {
    // 应用RTL到当前页面
    applyGlobalRTL(this.isRTL)
  }
}

/**
 * 为uni-app页面添加RTL支持
 */
export function setupPageRTL() {
  // 监听页面显示事件
  uni.$on('pageShow', () => {
    const appStore = useAppStore()
    applyGlobalRTL(appStore.isUyghur)
  })
  
  // 监听语言变化事件
  uni.$on('languageChanged', (data) => {
    applyGlobalRTL(data.isUyghur)
  })
}

/**
 * 获取RTL相关的CSS变量
 * @param {boolean} isRTL 是否为RTL
 * @returns {Object} CSS变量对象
 */
export function getRTLCSSVars(isRTL) {
  return {
    '--text-direction': isRTL ? 'rtl' : 'ltr',
    '--text-align': isRTL ? 'right' : 'left',
    '--flex-direction': isRTL ? 'row-reverse' : 'row',
    '--margin-start': isRTL ? 'margin-right' : 'margin-left',
    '--margin-end': isRTL ? 'margin-left' : 'margin-right',
    '--padding-start': isRTL ? 'padding-right' : 'padding-left',
    '--padding-end': isRTL ? 'padding-left' : 'padding-right',
    '--border-start': isRTL ? 'border-right' : 'border-left',
    '--border-end': isRTL ? 'border-left' : 'border-right',
    '--position-start': isRTL ? 'right' : 'left',
    '--position-end': isRTL ? 'left' : 'right'
  }
}

/**
 * 应用RTL CSS变量到元素
 * @param {Element} element 目标元素
 * @param {boolean} isRTL 是否为RTL
 */
export function applyRTLCSSVars(element, isRTL) {
  if (!element || !element.style) return
  
  const vars = getRTLCSSVars(isRTL)
  Object.keys(vars).forEach(key => {
    element.style.setProperty(key, vars[key])
  })
}

/**
 * 为组件添加RTL支持的装饰器
 * @param {Object} component 组件选项
 * @returns {Object} 增强后的组件选项
 */
export function withRTL(component) {
  const originalMounted = component.mounted
  const originalData = component.data
  const originalComputed = component.computed
  
  return {
    ...component,
    data() {
      const appStore = useAppStore()
      const originalDataResult = originalData ? originalData.call(this) : {}
      
      return {
        ...originalDataResult,
        isRTL: appStore.isUyghur,
        textDirection: appStore.isUyghur ? 'rtl' : 'ltr',
        textAlign: appStore.isUyghur ? 'right' : 'left'
      }
    },
    computed: {
      ...originalComputed,
      rtlClass() {
        const appStore = useAppStore()
        return {
          'rtl-layout': appStore.isUyghur,
          'ltr-layout': !appStore.isUyghur,
          'ug': appStore.isUyghur,
          [`lang-${appStore.lang}`]: true
        }
      },
      rtlStyle() {
        const appStore = useAppStore()
        return {
          direction: appStore.isUyghur ? 'rtl' : 'ltr',
          textAlign: appStore.isUyghur ? 'right' : 'left'
        }
      }
    },
    mounted() {
      // 调用原始的mounted
      if (originalMounted) {
        originalMounted.call(this)
      }
      
      // 应用RTL
      const appStore = useAppStore()
      applyGlobalRTL(appStore.isUyghur)
    }
  }
}

/**
 * 检测当前是否为RTL环境
 * @returns {boolean} 是否为RTL
 */
export function isRTLEnvironment() {
  const appStore = useAppStore()
  return appStore.isUyghur
}

/**
 * 获取当前文本方向
 * @returns {string} 'rtl' 或 'ltr'
 */
export function getCurrentTextDirection() {
  return isRTLEnvironment() ? 'rtl' : 'ltr'
}

/**
 * 获取当前文本对齐方式
 * @returns {string} 'right' 或 'left'
 */
export function getCurrentTextAlign() {
  return isRTLEnvironment() ? 'right' : 'left'
}

export default {
  initGlobalRTL,
  applyGlobalRTL,
  addRTLToPage,
  globalRTLMixin,
  setupPageRTL,
  getRTLCSSVars,
  applyRTLCSSVars,
  withRTL,
  isRTLEnvironment,
  getCurrentTextDirection,
  getCurrentTextAlign
}
