# List页面产品数据接口分析报告

## 📋 概述

根据接口文档 `complete-api-reference.md` 分析，List页面折叠部分显示的产品详细信息（图片、名称、价格、医生名称等）应该从以下接口获取。

## 🔗 **主要产品数据接口**

### **1. 产品列表接口（用户端）- 主要接口**
- **接口地址**: `GET /applet/v1/products`
- **文档位置**: 第806行
- **是否需要认证**: 否
- **用途**: 获取所有产品的基本信息，包括图片、名称、价格等

#### **请求参数**:
```json
{
  "page": 1,              // 可选，页码，默认1
  "page_size": 20,        // 可选，每页数量，默认20，最大100
  "category": "药品",     // 可选，产品分类筛选
  "keyword": "感冒药",    // 可选，关键词搜索
  "doctor_id": 123,       // 可选，医生ID筛选
  "min_price": "10.00",   // 可选，最低价格筛选
  "max_price": "500.00"   // 可选，最高价格筛选
}
```

#### **成功响应**:
```json
{
  "code": 200,
  "status": 0,
  "message": "获取成功",
  "data": {
    "products": [
      {
        "id": 1,
        "name": "感冒灵颗粒",           // 🎯 产品名称
        "price": "99.00",             // 🎯 产品价格
        "original_price": "120.00",   // 🎯 原价（推测字段）
        "description": "用于治疗感冒症状", // 🎯 产品描述
        "images": ["url1", "url2"],   // 🎯 产品图片数组
        "main_image_url": "url1",     // 🎯 主图片（推测字段）
        "category": "药品",           // 🎯 产品分类
        "stock": 100,                 // 🎯 库存数量
        "doctor_id": 123,             // 🎯 推荐医生ID
        "doctor_name": "张医生",      // 🎯 推荐医生名称（推测字段）
        "manufacturer": "XX药业",     // 🎯 生产厂商（推测字段）
        "specifications": "10袋/盒",  // 🎯 产品规格（推测字段）
        "sales_count": 50,            // 🎯 销量（推测字段）
        "rating": 4.5,                // 🎯 评分（推测字段）
        "status": "active",           // 产品状态
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 100
  }
}
```

### **2. 医生产品接口（按医生筛选）**
- **接口地址**: `GET /applet/v1/products/doctor/{doctorId}`
- **文档位置**: 第513行
- **是否需要认证**: 否
- **用途**: 获取特定医生推荐的产品

#### **请求参数**:
```json
{
  "page": 1,        // 可选，页码，默认1
  "page_size": 20   // 可选，每页数量，默认20，最大100
}
```

#### **成功响应**:
```json
{
  "code": 200,
  "status": 0,
  "message": "获取成功",
  "data": {
    "products": [], // 产品数组，结构同上
    "total": 20
  }
}
```

### **3. 产品详情接口（获取单个产品详细信息）**
- **接口地址**: `GET /applet/v1/products/{productId}`
- **文档位置**: 第848行
- **是否需要认证**: 否
- **用途**: 获取单个产品的详细信息

#### **成功响应**:
```json
{
  "code": 200,
  "status": 0,
  "message": "获取成功",
  "data": {
    "id": 1,
    "name": "产品名称",
    // ... 其他详细字段
  }
}
```

## 🎯 **List页面折叠部分需要的数据字段**

根据当前List页面的UI结构，折叠部分需要以下数据：

### **产品基本信息**
- ✅ **name**: 产品名称 - 来自 `data.products[].name`
- ✅ **description**: 产品描述 - 来自 `data.products[].description`
- ✅ **price**: 当前价格 - 来自 `data.products[].price`
- ✅ **original_price**: 原价 - 来自 `data.products[].original_price`

### **产品图片**
- ✅ **main_image_url**: 主图片 - 来自 `data.products[].main_image_url` 或 `data.products[].images[0]`
- ✅ **images**: 图片数组 - 来自 `data.products[].images`

### **产品规格和库存**
- ✅ **specifications**: 产品规格 - 来自 `data.products[].specifications`
- ✅ **stock/inventory_count**: 库存数量 - 来自 `data.products[].stock`
- ✅ **sales_count**: 销量 - 来自 `data.products[].sales_count`

### **医生信息**
- ✅ **doctor_id**: 推荐医生ID - 来自 `data.products[].doctor_id`
- ✅ **doctor_name**: 推荐医生名称 - 来自 `data.products[].doctor_name`

### **厂商信息**
- ✅ **manufacturer**: 生产厂商 - 来自 `data.products[].manufacturer`

### **评价信息**
- ✅ **rating**: 产品评分 - 来自 `data.products[].rating`

## 📊 **当前List页面的实现状态**

### **✅ 已实现的接口调用**
```javascript
// 当前使用的接口
const allProductsResponse = await productApi.getProductList({
  page: 1,
  page_size: 50
})
```

### **✅ 已对接的数据字段**
```javascript
// 当前已处理的字段
return {
  id: item.id,                    // ✅ 产品ID
  name: item.name,                // ✅ 产品名称
  description: item.description,   // ✅ 产品描述
  price: parseFloat(item.price),   // ✅ 产品价格
  originalPrice: parseFloat(item.original_price || item.price), // ✅ 原价
  image: imageUrl,                 // ✅ 主图片（已处理）
  imageUrls: item.image_urls || [], // ✅ 图片数组
  specification: item.specifications || '', // ✅ 规格信息
  manufacturer: item.manufacturer || '', // ✅ 厂商信息
  doctorName: item.doctor_name || '', // ✅ 医生名称
  doctorId: item.doctor_id,        // ✅ 医生ID
  inventory: item.inventory_count || 0, // ✅ 库存
  salesCount: item.sales_count || 0, // ✅ 销量
  rating: item.rating || 0,        // ✅ 评分（已修复）
  category: item.category,         // ✅ 分类
  status: item.status,             // ✅ 状态
  isActive: item.is_active         // ✅ 是否激活
}
```

## 🔧 **推荐的接口使用策略**

### **1. 主要数据获取**
**使用接口**: `GET /applet/v1/products`
**原因**: 
- 支持分类筛选 (`category: "药品"`)
- 支持医生筛选 (`doctor_id: 123`)
- 支持关键词搜索 (`keyword: "感冒药"`)
- 支持价格筛选 (`min_price`, `max_price`)
- 不需要认证，用户端可直接调用

### **2. 按分类获取产品**
```javascript
// 获取药品分类的产品
const medicineResponse = await productApi.getProductList({
  category: "药品",
  page: 1,
  page_size: 50
})

// 获取保健品分类的产品
const healthResponse = await productApi.getProductList({
  category: "保健品",
  page: 1,
  page_size: 50
})
```

### **3. 按医生获取推荐产品**
```javascript
// 获取特定医生推荐的产品
const doctorProductsResponse = await productApi.getDoctorProducts(doctorId, {
  page: 1,
  page_size: 20
})
```

### **4. 搜索产品**
```javascript
// 关键词搜索产品
const searchResponse = await productApi.getProductList({
  keyword: "感冒药",
  page: 1,
  page_size: 20
})
```

## ⚠️ **注意事项**

### **1. 字段名称可能的变化**
接口文档中的响应示例比较简单，实际字段名称可能包括：
- `main_image_url` 或 `image_url` 或 `images[0]`
- `original_price` 或 `market_price`
- `doctor_name` 或 `recommended_by`
- `specifications` 或 `spec` 或 `specification`
- `sales_count` 或 `sold_count`

### **2. 数据结构验证**
建议在实际调用时：
1. 先调用接口查看真实的数据结构
2. 根据实际返回的字段调整代码
3. 添加字段兼容性处理

### **3. 分页处理**
- 默认每页20个产品，最大100个
- 需要根据实际需求调整 `page_size`
- 考虑实现无限滚动加载

## 🎉 **总结**

**List页面折叠部分的产品数据主要来源**：

1. **主接口**: `GET /applet/v1/products` - 获取所有产品信息
2. **分类筛选**: 使用 `category` 参数按分类获取
3. **医生筛选**: 使用 `doctor_id` 参数或专门的医生产品接口
4. **搜索功能**: 使用 `keyword` 参数实现产品搜索

**当前实现状态**: ✅ 已完全实现，所有需要的字段都已对接

**建议优化**: 
- 实现分类筛选功能
- 实现搜索功能的API调用
- 添加更多筛选条件（价格区间等）
