import App from './App'

import './main.scss'

// 根域名
export const domain = "https://appdava.sulmas.com.cn";
export const BASE_URL=`${domain}/applet/`
export const FILE_URL=`${BASE_URL}index/file/`

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import { createPinia } from 'pinia'
import { uyghurFont, autoUyghurFont, conditionalUyghurFont } from '@/directives/uyghurFont.js'
import { i18nDirective } from '@/plugins/i18n.js'

export function createApp() {
  const app = createSSRApp(App)
  const pinia = createPinia()
  app.use(pinia)

  // 注册维吾尔文字体指令
  app.directive('uyghur-font', uyghurFont)
  app.directive('auto-uyghur-font', autoUyghurFont)
  app.directive('conditional-uyghur-font', conditionalUyghurFont)

  // 注册国际化指令
  app.directive('i18n', i18nDirective)

  return {
    app,
    pinia
  }
}
// #endif