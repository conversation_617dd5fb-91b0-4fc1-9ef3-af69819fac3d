/**
 * 🔧 统一的工具函数库
 *
 * 这个文件整合了项目中所有的通用工具函数
 * 目的是提供统一、可复用的工具方法，避免重复代码
 *
 * 📋 包含的工具类型：
 * - 字符串处理工具
 * - 数组操作工具
 * - 对象处理工具
 * - 日期时间工具
 * - 数字格式化工具
 * - 验证工具
 * - 存储工具
 * - 设备信息工具
 *
 * 💡 使用方式：
 * import { isEmpty, formatDate, debounce } from '@/utils/helpers.js'
 */

/**
 * ==================== 📝 字符串工具 ====================
 */



/**
 * 🔍 判断字符串是否为空
 *
 * 检查字符串是否为null、undefined、空字符串或只包含空白字符
 *
 * @param {string} str - 要检查的字符串
 * @returns {boolean} 如果字符串为空返回true，否则返回false
 *
 * 💡 使用示例：
 * isEmpty('')        // true
 * isEmpty('  ')      // true
 * isEmpty('hello')   // false
 * isEmpty(null)      // true
 */
export function isEmpty(str) {
  return !str || str.toString().trim() === ''
}

/**
 * 🔤 首字母大写
 *
 * 将字符串的第一个字母转换为大写，其余保持不变
 *
 * @param {string} str - 要处理的字符串
 * @returns {string} 首字母大写的字符串
 *
 * 💡 使用示例：
 * capitalize('hello')     // 'Hello'
 * capitalize('WORLD')     // 'WORLD'
 * capitalize('')          // ''
 */
export function capitalize(str) {
  if (!str) return ''
  return str.charAt(0).toUpperCase() + str.slice(1)
}

/**
 * 🐪➡️🍢 驼峰转短横线
 *
 * 将驼峰命名法的字符串转换为短横线分隔的字符串
 *
 * @param {string} str - 驼峰格式的字符串
 * @returns {string} 短横线分隔的字符串
 *
 * 💡 使用示例：
 * kebabCase('userName')      // 'user-name'
 * kebabCase('firstName')     // 'first-name'
 * kebabCase('myComponent')   // 'my-component'
 */
export function kebabCase(str) {
  return str.replace(/([A-Z])/g, '-$1').toLowerCase()
}

/**
 * 🍢➡️🐪 短横线转驼峰
 *
 * 将短横线分隔的字符串转换为驼峰命名法
 *
 * @param {string} str - 短横线分隔的字符串
 * @returns {string} 驼峰格式的字符串
 *
 * 💡 使用示例：
 * camelCase('user-name')      // 'userName'
 * camelCase('first-name')     // 'firstName'
 * camelCase('my-component')   // 'myComponent'
 */
export function camelCase(str) {
  return str.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase())
}

/**
 * ✂️ 截取字符串
 *
 * 截取指定长度的字符串，超出部分用后缀替代
 *
 * @param {string} str - 原字符串
 * @param {number} length - 截取长度
 * @param {string} suffix - 后缀（默认为'...'）
 * @returns {string} 截取后的字符串
 *
 * 💡 使用示例：
 * truncate('这是一个很长的标题', 5)           // '这是一个很...'
 * truncate('短标题', 10)                    // '短标题'
 * truncate('长文本', 3, '***')              // '长文本***'
 */
export function truncate(str, length = 50, suffix = '...') {
  if (!str || str.length <= length) return str
  return str.substring(0, length) + suffix
}

/**
 * ==================== 数字工具 ====================
 */

/**
 * 格式化价格
 * @param {number|string} price 价格
 * @param {number} decimals 小数位数
 * @returns {string} 格式化后的价格
 */
export function formatPrice(price, decimals = 2) {
  const num = parseFloat(price)
  if (isNaN(num)) return '0.00'
  return num.toFixed(decimals)
}

/**
 * 格式化数字（千分位）
 * @param {number|string} num 数字
 * @returns {string} 格式化后的数字
 */
export function formatNumber(num) {
  const number = parseFloat(num)
  if (isNaN(number)) return '0'
  return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

/**
 * 生成随机数
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {number} 随机数
 */
export function randomNumber(min = 0, max = 100) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * ==================== 数组工具 ====================
 */

/**
 * 数组去重
 * @param {array} arr 数组
 * @param {string} key 去重键名（对象数组）
 * @returns {array} 去重后的数组
 */
export function unique(arr, key = null) {
  if (!Array.isArray(arr)) return []
  
  if (key) {
    const seen = new Set()
    return arr.filter(item => {
      const value = item[key]
      if (seen.has(value)) return false
      seen.add(value)
      return true
    })
  }
  
  return [...new Set(arr)]
}

/**
 * 数组分组
 * @param {array} arr 数组
 * @param {string|function} key 分组键名或函数
 * @returns {object} 分组后的对象
 */
export function groupBy(arr, key) {
  if (!Array.isArray(arr)) return {}
  
  return arr.reduce((groups, item) => {
    const groupKey = typeof key === 'function' ? key(item) : item[key]
    if (!groups[groupKey]) {
      groups[groupKey] = []
    }
    groups[groupKey].push(item)
    return groups
  }, {})
}

/**
 * 数组排序
 * @param {array} arr 数组
 * @param {string} key 排序键名
 * @param {string} order 排序方向 asc|desc
 * @returns {array} 排序后的数组
 */
export function sortBy(arr, key, order = 'asc') {
  if (!Array.isArray(arr)) return []
  
  return [...arr].sort((a, b) => {
    const aVal = a[key]
    const bVal = b[key]
    
    if (aVal < bVal) return order === 'asc' ? -1 : 1
    if (aVal > bVal) return order === 'asc' ? 1 : -1
    return 0
  })
}

/**
 * ==================== 对象工具 ====================
 */

/**
 * 深拷贝
 * @param {any} obj 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 对象合并
 * @param {object} target 目标对象
 * @param {...object} sources 源对象
 * @returns {object} 合并后的对象
 */
export function merge(target, ...sources) {
  if (!target) target = {}
  
  sources.forEach(source => {
    if (source) {
      Object.keys(source).forEach(key => {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          target[key] = merge(target[key] || {}, source[key])
        } else {
          target[key] = source[key]
        }
      })
    }
  })
  
  return target
}

/**
 * 获取对象属性值
 * @param {object} obj 对象
 * @param {string} path 属性路径
 * @param {any} defaultValue 默认值
 * @returns {any} 属性值
 */
export function get(obj, path, defaultValue = undefined) {
  if (!obj || !path) return defaultValue
  
  const keys = path.split('.')
  let result = obj
  
  for (const key of keys) {
    if (result === null || result === undefined || !(key in result)) {
      return defaultValue
    }
    result = result[key]
  }
  
  return result
}

/**
 * ==================== 日期工具 ====================
 */

/**
 * 格式化日期
 * @param {Date|string|number} date 日期
 * @param {string} format 格式
 * @returns {string} 格式化后的日期
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 获取相对时间
 * @param {Date|string|number} date 日期
 * @returns {string} 相对时间
 */
export function getRelativeTime(date) {
  const now = new Date()
  const target = new Date(date)
  const diff = now.getTime() - target.getTime()
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day
  
  if (diff < minute) return '刚刚'
  if (diff < hour) return `${Math.floor(diff / minute)}分钟前`
  if (diff < day) return `${Math.floor(diff / hour)}小时前`
  if (diff < week) return `${Math.floor(diff / day)}天前`
  if (diff < month) return `${Math.floor(diff / week)}周前`
  if (diff < year) return `${Math.floor(diff / month)}个月前`
  return `${Math.floor(diff / year)}年前`
}

/**
 * ==================== 验证工具 ====================
 */

/**
 * 验证手机号
 * @param {string} phone 手机号
 * @returns {boolean} 是否有效
 */
export function isValidPhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证邮箱
 * @param {string} email 邮箱
 * @returns {boolean} 是否有效
 */
export function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证身份证号
 * @param {string} idCard 身份证号
 * @returns {boolean} 是否有效
 */
export function isValidIdCard(idCard) {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardRegex.test(idCard)
}

/**
 * ==================== 存储工具 ====================
 */

/**
 * 设置本地存储
 * @param {string} key 键名
 * @param {any} value 值
 */
export function setStorage(key, value) {
  try {
    uni.setStorageSync(key, JSON.stringify(value))
  } catch (error) {
    console.error('设置存储失败:', error)
  }
}

/**
 * 获取本地存储
 * @param {string} key 键名
 * @param {any} defaultValue 默认值
 * @returns {any} 存储值
 */
export function getStorage(key, defaultValue = null) {
  try {
    const value = uni.getStorageSync(key)
    return value ? JSON.parse(value) : defaultValue
  } catch (error) {
    console.error('获取存储失败:', error)
    return defaultValue
  }
}

/**
 * 删除本地存储
 * @param {string} key 键名
 */
export function removeStorage(key) {
  try {
    uni.removeStorageSync(key)
  } catch (error) {
    console.error('删除存储失败:', error)
  }
}

/**
 * ==================== URL工具 ====================
 */

/**
 * 解析URL参数
 * @param {string} url URL字符串
 * @returns {object} 参数对象
 */
export function parseUrlParams(url) {
  const params = {}

  try {
    // 处理相对URL和绝对URL
    const urlObj = url.startsWith('http') ? new URL(url) : new URL(url, 'https://example.com')

    for (const [key, value] of urlObj.searchParams) {
      params[key] = value
    }
  } catch (error) {
    console.warn('URL解析失败:', url, error)
  }

  return params
}

/**
 * 构建URL参数
 * @param {object} params 参数对象
 * @returns {string} 参数字符串
 */
export function buildUrlParams(params) {
  if (!params || typeof params !== 'object') return ''
  
  const searchParams = new URLSearchParams()
  
  Object.keys(params).forEach(key => {
    if (params[key] !== null && params[key] !== undefined) {
      searchParams.append(key, params[key])
    }
  })
  
  return searchParams.toString()
}

/**
 * ==================== 防抖节流 ====================
 */

/**
 * 防抖函数
 * @param {function} func 要防抖的函数
 * @param {number} delay 延迟时间
 * @returns {function} 防抖后的函数
 */
export function debounce(func, delay = 300) {
  let timeoutId
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 节流函数
 * @param {function} func 要节流的函数
 * @param {number} delay 延迟时间
 * @returns {function} 节流后的函数
 */
export function throttle(func, delay = 300) {
  let lastTime = 0
  return function (...args) {
    const now = Date.now()
    if (now - lastTime >= delay) {
      lastTime = now
      func.apply(this, args)
    }
  }
}
