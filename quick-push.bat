@echo off
chcp 65001 >nul
echo 🚀 快速上传到Gitee...

REM 添加所有更改
git add .

REM 检查是否有更改
git diff --staged --quiet
if %errorlevel% equ 0 (
    echo ℹ️  没有检测到代码更改
    pause
    exit /b 0
)

REM 生成提交信息
for /f "tokens=1-3 delims=/ " %%i in ('date /t') do set mydate=%%i-%%j-%%k
for /f "tokens=1-2 delims=: " %%i in ('time /t') do set mytime=%%i:%%j
set commit_msg=更新代码: %mydate% %mytime%

REM 提交并推送
git commit -m "%commit_msg%"
git push origin master

if %errorlevel% equ 0 (
    echo ✅ 代码已成功上传到Gitee!
) else (
    echo ❌ 上传失败，请检查网络连接
)

pause
