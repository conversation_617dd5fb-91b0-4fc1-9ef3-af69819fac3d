import {BASE_URL} from '@/main.js'

// 获取token
const getToken = () => {
  return uni.getStorageSync('auth_token') || uni.getStorageSync('session_key');
};

// Token刷新状态管理
let isRefreshing = false;
let requestQueue = [];

// 统一请求方法
export const request = (config = {}) => {
  let {
    url,
    method = "GET",
    data = {},
    header = {}
  } = config;

  // 拼接完整的API URL
  url = BASE_URL + url;

  // 设置请求头
  const token = getToken();
  const requestHeader = {
    'Content-Type': 'application/json',
    ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
    ...header
  };

  console.log('📡 API请求:', method, url);
  if (token) {
    console.log('🔑 携带Token:', token.substring(0, 10) + '...');
  } else {
    console.log('⚠️ 未携带Token');
  }

  return new Promise((resolve, reject) => {
    uni.request({
      url,
      method,
      data,
      header: requestHeader,
      timeout: 30000,
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else if (res.statusCode === 401) {
          // Token过期处理
          if (!isRefreshing) {
            isRefreshing = true;
            uni.showToast({ title: 'Token过期，请重新登录', icon: 'none' });

            // 清除token并跳转登录
            uni.removeStorageSync('auth_token');
            uni.removeStorageSync('session_key');
            uni.removeStorageSync('user_info');
            uni.removeStorageSync('auth');

            setTimeout(() => {
              uni.navigateTo({ url: '/pages/login/login' });
            }, 1500);

            isRefreshing = false;
          }
          reject(res.data);
        } else {
          uni.showToast({
            title: res.data.message || `请求失败[${res.statusCode}]`,
            icon: 'none',
          });
          reject(res.data);
        }
      },
      fail: (err) => {
        console.error("请求失败:", err);
        uni.showToast({
          title: '网络请求失败',
          icon: 'none',
        });
        reject(err);
      }
    });
  });
};

// API接口已移至 request/index.js 文件中统一管理