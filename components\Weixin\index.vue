<template>
	<uni-popup ref="popupRef" type="center" :mask-click="true">
		<view class="wx-popup-content">
			<!-- 关闭按钮 -->
			<view class="wx-popup-close" @click="handleClose">×</view>
			<!-- 头像获取按钮 -->
			<view>
				<button open-type="chooseAvatar" class="wx-login-box-avatar" @chooseavatar="getUserAvatar"
					style="padding-left: 0; padding-right: 0; background-color: #d5d5d5">
					<image :src="avatarUrl || '/static/icon/user.svg'" mode="aspectFill" @error="handleImageError" />
					<view class="avatar-overlay">
						<text
							class="avatar-text ug">{{ avatarUrl ? $t('login.changeAvatar') : $t('login.selectAvatar') }}</text>
					</view>
				</button>
				<view v-if="!avatarUrl" class="avatar-tip">
					<text class="tip-text ug">{{ $t('login.avatarTip') }}</text>
				</view>
			</view>
			<!-- 用户名输入框 -->
			<input class="wx-nickname-input ug" type="nickname" :placeholder="$t('login.nicknamePlaceholder')"
				v-model="nickName" @change="handleNickName" />
			<view class="wx-popup-desc">
				<text class="ug">{{ $t('login.experienceTip') }}</text>
				<text v-if="isDevTool" class="dev-tip ug">{{ $t('login.devTip') }}</text>
			</view>

			<button class="wx-popup-login-btn ug" @click="handleLogin" :disabled="isLoading">
				<text v-if="isLoading">{{ $t('login.logging') || '登录中...' }}</text>
				<text v-else>{{ $t('login.loginNow') }}</text>
			</button>
		</view>
	</uni-popup>
</template>

<script setup>
	// 这里可以添加登录逻辑
	import {
		ref,
		defineExpose,
		defineEmits
	} from 'vue';
	import {
		useAppStore
	} from '@/store/app.js'
	import {
		FILE_URL
	} from '@/main.js'
import { processAvatarUrl } from '@/request/avatar.js'
	import {
		t
	} from '@/locale/index.js';
	import {
		userinfo,
		wechat_Authenticator,
		useravatr
	} from '@/request/index.js'
	import {
		useUserStore
	} from '@/store/user.js'

	const appStores = useAppStore()

	const avatarUrl = ref(''); //用户头像
	const nickName = ref(''); //用户名称
	const isDevTool = ref(false); //是否为开发者工具环境
	const isLoading = ref(false); //登录加载状态

	const popupRef = ref(null); //弹出框

	// 使用用户store
	const {
		loginSuccess
	} = useUserStore()

	// 翻译方法
	const $t = (key) => t(key);

	// 检测环境
	const checkEnvironment = () => {
		const systemInfo = uni.getSystemInfoSync();
		isDevTool.value = systemInfo.platform === 'devtools';
		console.log('当前环境:', isDevTool.value ? '开发者工具' : '真机/模拟器');
	}

	// 检查网络状态
	const checkNetworkStatus = () => {
		return new Promise((resolve, reject) => {
			uni.getNetworkType({
				success: (res) => {
					if (res.networkType === 'none') {
						uni.showToast({
							title: '网络连接不可用，请检查网络设置',
							icon: 'none',
							duration: 3000
						});
						reject(new Error('网络不可用'));
					} else {
						resolve(res.networkType);
					}
				},
				fail: () => {
					// 如果获取网络状态失败，假设网络可用
					resolve('unknown');
				}
			});
		});
	}

	// 重置表单数据
	const resetForm = () => {
		avatarUrl.value = '';
		nickName.value = '';
		isLoading.value = false;
	};

	// 打开弹窗
	const open = () => {
		popupRef.value && popupRef.value.open();
		// 重置表单数据
		resetForm();
		// 检测环境
		checkEnvironment();
	};

	// 关闭弹窗
	const close = () => {
		popupRef.value && popupRef.value.close();
	};
	const emits = defineEmits(['close'])

	// 获取用户头像
	const getUserAvatar = (e) => {
		console.log('头像选择事件:', e);
		const {
			avatarUrl: newAvatarUrl
		} = e.detail;

		if (newAvatarUrl) {
			console.log('临时头像路径:', newAvatarUrl);

			if (isDevTool.value) {
				// 开发者工具环境：使用特殊处理
				handleDevToolAvatar(newAvatarUrl);
			} else {
				// 真机环境：正常保存
				saveAvatarToLocal(newAvatarUrl);
			}
		} else {
			console.error('头像获取失败，avatarUrl为空');
			uni.showToast({
				title: '头像获取失败，请重试',
				icon: 'none'
			});
		}
	}

	// 开发者工具环境的头像处理
	const handleDevToolAvatar = (tempPath) => {
		console.log('开发者工具环境，使用特殊处理');
		console.log('临时文件路径:', tempPath);

		// 先检查文件是否存在
		uni.getFileInfo({
			filePath: tempPath,
			success: (fileInfo) => {
				console.log('文件存在，大小:', fileInfo.size);
				// 文件存在，直接使用
				avatarUrl.value = tempPath;
				uni.showToast({
					title: '头像获取成功',
					icon: 'success'
				});
			},
			fail: (err) => {
				console.error('文件不存在或无法访问:', err);
				// 文件不存在，尝试强制刷新获取
				handleFileNotFound(tempPath);
			}
		});
	}

	// 处理文件不存在的情况
	const handleFileNotFound = (tempPath) => {
		console.log('尝试处理文件不存在的问题');

		// 方案1：尝试使用getImageInfo验证
		uni.getImageInfo({
			src: tempPath,
			success: (res) => {
				console.log('图片信息获取成功，可能是getFileInfo的问题:', res);
				avatarUrl.value = tempPath;
				uni.showToast({
					title: '头像获取成功',
					icon: 'success'
				});
			},
			fail: (err) => {
				console.error('图片信息也获取失败:', err);
				// 方案2：提示用户重新选择
				showRetryDialog();
			}
		});
	}

	// 显示重试对话框
	const showRetryDialog = () => {
		uni.showModal({
			title: '头像获取异常',
			content: '检测到开发环境文件系统异常，建议：\n1. 重新选择头像\n2. 或重启开发者工具\n\n是否重新选择头像？',
			confirmText: '重新选择',
			cancelText: '稍后再试',
			success: (res) => {
				if (res.confirm) {
					// 重置头像，让用户重新选择
					avatarUrl.value = '';
					uni.showToast({
						title: '请重新选择头像',
						icon: 'none'
					});
				} else {
					// 用户选择稍后再试，使用默认头像
					avatarUrl.value = '/static/icon/user.svg';
					uni.showToast({
						title: '已使用默认头像',
						icon: 'none'
					});
				}
			}
		});
	}

	// 保存头像到本地存储（解决临时文件路径问题）
	const saveAvatarToLocal = (tempPath) => {
		uni.showLoading({
			title: '处理头像中...'
		});

		// 使用 saveFile 保存到本地
		uni.saveFile({
			tempFilePath: tempPath,
			success: (res) => {
				console.log('头像保存成功:', res.savedFilePath);
				avatarUrl.value = res.savedFilePath;
				uni.hideLoading();
				uni.showToast({
					title: '头像获取成功',
					icon: 'success'
				});
			},
			fail: (err) => {
				console.error('头像保存失败:', err);
				// 备用方案：使用文件系统管理器
				copyAvatarWithFileManager(tempPath);
			}
		});
	}

	// 备用方案：使用文件系统管理器复制文件
	const copyAvatarWithFileManager = (tempPath) => {
		try {
			const fs = uni.getFileSystemManager();
			const timestamp = Date.now();
			const localPath = `${uni.env.USER_DATA_PATH}/avatar_${timestamp}.jpg`;

			fs.copyFile({
				srcPath: tempPath,
				destPath: localPath,
				success: () => {
					console.log('头像复制成功:', localPath);
					avatarUrl.value = localPath;
					uni.hideLoading();
					uni.showToast({
						title: '头像获取成功',
						icon: 'success'
					});
				},
				fail: (err) => {
					console.error('头像复制失败:', err);
					// 最后的备用方案：直接使用临时路径
					avatarUrl.value = tempPath;
					uni.hideLoading();
					uni.showToast({
						title: '头像获取成功（开发环境）',
						icon: 'success'
					});
				}
			});
		} catch (error) {
			console.error('文件系统操作失败:', error);
			// 直接使用临时路径
			avatarUrl.value = tempPath;
			uni.hideLoading();
			uni.showToast({
				title: '头像获取成功',
				icon: 'success'
			});
		}
	}

	// 获取用户昵称
	const handleNickName = (e) => {
		// 清理输入的昵称，移除特殊空白字符
		let cleanValue = e.detail.value
			.replace(/[\u200B-\u200D\uFEFF\u3000\u2000-\u200A\u2028\u2029\u3164]/g, '') // 移除各种空白字符
			.replace(/\s+/g, ' ') // 将多个空格替换为单个空格
			.trim(); // 去除首尾空格

		nickName.value = cleanValue;

	}

	// 处理图片加载错误
	const handleImageError = (e) => {

		// 显示错误提示但不重置头像路径，因为路径可能是正确的，只是显示有问题
		uni.showModal({
			title: '头像显示异常',
			content: '头像可能无法正常显示，但不影响登录功能。是否重新选择头像？',
			confirmText: '重新选择',
			cancelText: '继续使用',
			success: (res) => {
				if (res.confirm) {
					// 用户选择重新选择头像
					avatarUrl.value = '';
				}
				// 如果用户选择继续使用，保持当前头像路径不变
			}
		});
	}



	// 微信登录
	const handleLogin = async () => {
		// 防止重复点击
		if (isLoading.value) return;

		try {
			// 检查网络状态
			await checkNetworkStatus();

			// 验证必填项
			if (!avatarUrl.value) {
				uni.showToast({
					title: '请先选择头像',
					icon: 'none'
				});
				return;
			}

			if (!nickName.value || !nickName.value.trim()) {
				uni.showToast({
					title: '请输入昵称',
					icon: 'none'
				});
				return;
			}

			// 设置加载状态
			isLoading.value = true;

			// 显示加载提示
			uni.showLoading({
				title: '登录中...'
			});


			// 第三步：上传头像和昵称
			console.log('🚀 开始上传头像和昵称...');
			console.log('头像路径:', avatarUrl.value);
			console.log('用户昵称:', nickName.value);

			await useravatr(avatarUrl.value, nickName.value).then((result) => {
				console.log("login :", result)
				if (result.code == 200) {
					userinfo().then((user_res) => {
						const user_info = user_res.data;
						// 🔧 使用processAvatarUrl处理头像URL，确保格式正确
						if (user_info.avatar) {
							user_info['avatar'] = processAvatarUrl(user_info.avatar)
							console.log('🖼️ 处理后的头像URL:', user_info.avatar)
						}
						appStores.setUserInfo(user_info)

						// 触发全局事件，通知其他页面更新用户信息
						console.log('📢 发送全局事件通知...');
						uni.$emit('userInfoUpdated', user_info);
						uni.$emit('loginSuccess', user_info);

						// 触发昵称更新事件
						const nickname = user_info.nickname || user_info.name || nickName.value;
						console.log('📢 发送昵称更新事件:', nickname);
						uni.$emit('nicknameUpdated', nickname);

						// 强制刷新页面状态
						setTimeout(() => {
							console.log('🔄 强制刷新页面状态...');
							uni.$emit('forceRefreshUserInfo', user_info);

							// 检查当前页面并刷新
							const pages = getCurrentPages();
							const currentPage = pages[pages.length - 1];
							console.log('📄 当前页面:', currentPage.route);

							if (currentPage.route === 'pages/My/My') {
								console.log('🔄 刷新My页面...');
								uni.$emit('refreshMyPage');
							} else if (currentPage.route === 'pages/My/Settings/Settings') {
								console.log('🔄 刷新Settings页面...');
								uni.$emit('refreshSettingsPage');
							} else if (currentPage.route === 'pages/index/index') {
								console.log('🔄 刷新Index页面...');
								uni.$emit('refreshIndexPage');
							}
						}, 500);
					})
				}
			})
	
			// 隐藏加载提示
			uni.hideLoading();
			isLoading.value = false;

			// 显示成功提示
			uni.showToast({
				title: '登录成功',
				icon: 'success'
			});
			// 关闭弹窗
			handleClose();

		} catch (error) {
			console.error('❌ 登录失败:', error);

			// 隐藏加载提示并重置状态
			uni.hideLoading();
			isLoading.value = false;

			// 根据错误类型显示不同的提示
			let errorMessage = '登录失败，请重试';

			if (error.message) {
				if (error.message.includes('网络')) {
					errorMessage = '网络连接异常，请检查网络后重试';
				} else if (error.message.includes('token') || error.message.includes('认证')) {
					errorMessage = '登录认证失败，请重新尝试';
				} else if (error.message.includes('头像') || error.message.includes('上传')) {
					errorMessage = '头像上传失败，请重新选择头像';
				} else {
					errorMessage = error.message;
				}
			}

			// 显示错误提示
			uni.showToast({
				title: errorMessage,
				icon: 'none',
				duration: 3000
			});
		}
	}


	const handleClose = () => {
		close();
		emits('close');
	}

	defineExpose({
		open,
		close,
		popupRef
	})
</script>

<style lang="scss" scoped>
	.wx-login-btn {
		background: #1AAD19;
		color: #fff;
		border: none;
		border-radius: 24px;
		width: 220px;
		font-size: 16px;
		margin: 40px auto 0 auto;
		display: block;
		box-shadow: 0 2px 8px rgba(26, 173, 25, 0.12);
		padding-bottom: 100rpx;
	}

	.wx-popup-content {
		width: 320px;
		background: #fff;
		border-radius: 16px;
		padding: 32px 24px 24px 24px;
		display: flex;
		flex-direction: column;
		align-items: center;
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.10);
		position: relative;
	}

	.avatar-btn {
		padding: 0;
		position: relative;
		margin-bottom: 16px;
		display: flex;
		align-items: center;
		justify-content: center;

	}

	.avatar-image {
		width: 90px;
		height: 90px;
		border-radius: 50%;
		border: 3px solid #42b983;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		transition: all 0.3s ease;
		object-fit: cover;
	}

	.avatar-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		border-radius: 50%;
		opacity: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: opacity 0.3s ease;
	}

	.avatar-text {
		color: white;
		font-size: 14px;
		font-weight: bold;
	}

	.avatar-btn:active .avatar-overlay {
		opacity: 1;
	}

	.avatar-btn:hover .avatar-image {
		transform: scale(1.05);
	}

	.wx-nickname-input {
		width: 80%;
		height: 38px;
		border: 1px solid #eee;
		border-radius: 8px;
		margin-bottom: 20px;
		padding: 0 12px;
		font-size: 16px;
		margin-top: 40rpx;
		text-align: center;
	}

	.wx-popup-desc {
		font-size: 14px;
		color: #888;
		margin-bottom: 24px;
		text-align: center;
	}

	.dev-tip {
		display: block;
		font-size: 12px;
		color: #999;
		margin-top: 8px;
	}



	.wx-popup-login-btn {
		background: linear-gradient(90deg, #1AAD19 0%, #22C35E 100%);
		color: #fff;
		border: none;
		border-radius: 24px;
		width: 100%;
		font-size: 18px;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 2px 8px rgba(26, 173, 25, 0.12);
		font-weight: bold;
		margin-top: 10px;
		height: 80rpx;
	}

	.wx-popup-login-btn:active {
		opacity: 0.85;
	}

	.wx-popup-login-btn:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.wx-popup-close {
		position: absolute;
		top: 12px;
		right: 16px;
		font-size: 24px;
		color: #888;
		cursor: pointer;
		z-index: 10;
	}

	.wx-login-box-avatar {
		width: 186rpx;
		height: 186rpx;
		background: #d5d5d5;
		border-radius: 100%;
		margin: auto;
		color: #fff;
		text-align: center;
		line-height: 186rpx;
		font-size: 40rpx;
		position: relative;
		overflow: hidden;

		image {
			width: 100%;
			height: 100%;
			object-fit: cover;
			border-radius: 100%;
		}
	}

	.avatar-tip {
		text-align: center;
		margin-top: 10rpx;
	}

	.tip-text {
		font-size: 24rpx;
		color: #999;
	}
</style>