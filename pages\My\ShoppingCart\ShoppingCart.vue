<template>
	<view class="cart-container" :class="[fontClass, pageClass]" :style="pageStyle">
		<!-- 空状态 -->
		<view v-if="cartItems.length === 0" class="empty-state">
			<view class="empty-icon">
				<view class="cart-icon-bg">
					<fui-icon name="cart" :size="80" color="#52c41a"></fui-icon>
				</view>
			</view>
			<text class="empty-title">{{ $t('shoppingCart.empty') }}</text>
			<text class="empty-subtitle">{{ $t('shoppingCart.emptySubtitle') }}</text>
			<view class="go-shopping-btn" @tap="goShopping">
				<text class="btn-text">{{ $t('shoppingCart.goShopping') }}</text>
			</view>
		</view>

		<!-- 有商品状态 -->
		<view v-else class="cart-content">
			<!-- 全选区域 -->
			<view class="select-all-section">
				<view class="select-all-item" @tap="toggleSelectAll">
					<view class="checkbox" :class="{ 'checked': isAllSelected }">
						<fui-icon v-if="isAllSelected" name="checkbox" :size="32" color="#52c41a"></fui-icon>
					</view>
					<text class="select-text">{{ $t('shoppingCart.selectAll') }}</text>
				</view>
				<text class="total-count">{{ $t('shoppingCart.totalItems', { count: cartItems.length }) }}</text>
			</view>

			<!-- 商品列表 - 可滚动区域 -->
			<scroll-view class="cart-list" scroll-y>
				<fui-swipe-action
					v-for="(item, index) in cartItems"
					:key="item.id"
					:options="swipeOptions"
					@click="handleSwipeClick($event, item, index)"
					class="custom-swipe-action"
				>
					<view class="cart-item">
						<!-- 选择框 -->
						<view class="item-checkbox" @tap="toggleItemSelect(item.id)">
							<view class="checkbox" :class="{ 'checked': item.selected }">
								<fui-icon v-if="item.selected" name="checkbox" :size="32" color="#52c41a"></fui-icon>
							</view>
						</view>

						<!-- 商品图片 -->
						<view class="item-image">
							<image :src="item.image" class="product-image" mode="aspectFill"></image>
						</view>

						<!-- 商品信息 -->
						<view class="item-info">
							<text class="product-name">{{ item.name }}</text>
							<text class="product-desc">{{ item.description }}</text>
							<view class="price-quantity">
								<view class="price-section">
									<text class="current-price">¥{{ item.price }}</text>
									<text v-if="item.originalPrice" class="original-price">¥{{ item.originalPrice }}</text>
								</view>
							</view>
							<text class="subtotal">{{ $t('shoppingCart.subtotal') }}: ¥{{ (item.price * item.quantity).toFixed(2) }}</text>
							<!-- 滑动提示图标 -->
							<view class="swipe-hint-icon">
								<image class="slide-icon" src="/static/icon/slideIcon.svg"></image>
								
							</view>
						</view>

						<!-- 数量控制 -->
						<view class="quantity-controls-wrapper">
							<view class="quantity-controls">
								<view class="quantity-btn" @tap="decreaseQuantity(item.id)">
									<fui-icon name="minus" :size="24" color="#999"></fui-icon>
								</view>
								<text class="quantity-text">{{ item.quantity }}</text>
								<view class="quantity-btn" @tap="increaseQuantity(item.id)">
									<fui-icon name="plus" :size="24" color="#999"></fui-icon>
								</view>
							</view>
						
						</view>
					</view>
				</fui-swipe-action>
			</scroll-view>
		</view>

		<!-- 固定删除按钮 - 全选时显示 -->
		<view
			v-if="cartItems.length > 0 && isAllSelected"
			class="fixed-delete-btn"
			:class="{ 'show': isAllSelected }"
			@tap="deleteAllSelected"
		>
			<view class="delete-btn-content">
				<view class="delete-icon">
					<fui-icon name="trash" :size="32" color="#ffffff"></fui-icon>
				</view>
				<text class="delete-text">{{ $t('shoppingCart.deleteAll') }}</text>
			</view>
		</view>

		<!-- 底部结算栏 - 固定位置 -->
		<view v-if="cartItems.length > 0" class="bottom-bar">
			<view class="selected-info">
				<text class="selected-count">{{ $t('shoppingCart.selectedItems', { count: selectedCount }) }}</text>
				<view class="total-price">
					<text class="total-label">{{ $t('shoppingCart.total') }}: </text>
					<text class="total-amount">¥{{ totalPrice }}</text>
				</view>
			</view>
			<view class="checkout-btn" @tap="checkout">
				<text class="checkout-text">{{ $t('shoppingCart.checkout', { count: selectedCount }) }}</text>
			</view>
		</view>
	</view>
</template>
<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { useAppStore } from '@/store/app.js'
import { t } from '@/locale/index.js'
import { useRTLPage } from '@/utils/rtlMixin.js'

// 应用状态管理
const appStore = useAppStore()

// RTL布局支持
const { pageClass, pageStyle, isRTL } = useRTLPage()

// 计算字体类（合并RTL类）
const fontClass = computed(() => ({
	...pageClass.value,
	'ug': appStore.isUyghur,
	[`lang-${appStore.lang}`]: true
}))

// 翻译方法
const $t = (key, params) => t(key, params)

// 购物车商品列表
const cartItems = ref([])
// 编辑模式
const isEditMode = ref(false)

// 滑动菜单配置
const swipeOptions = computed(() => [
	{
		text: $t('shoppingCart.delete'),
		style: {
			backgroundColor: '#FF3B30',
			color: '#FFFFFF',
			fontSize: '28rpx'
		}
	}
])

// 计算属性
const selectedCount = computed(() => {
	return cartItems.value.filter(item => item.selected).length
})

const isAllSelected = computed(() => {
	return cartItems.value.length > 0 && cartItems.value.every(item => item.selected)
})

const totalPrice = computed(() => {
	return cartItems.value
		.filter(item => item.selected)
		.reduce((total, item) => total + (item.price * item.quantity), 0)
		.toFixed(2)
})

// 获取购物车数据
const getCartItems = () => {
	const items = uni.getStorageSync('cartItems') || []
	cartItems.value = items
}

// 保存购物车数据
const saveCartItems = () => {
	uni.setStorageSync('cartItems', cartItems.value)
}

// 去购物 - 跳转到产品页面
const goShopping = () => {
	uni.switchTab({
		url: '/pages/List/List'
	})
	// 切换到产品tab
	setTimeout(() => {
		uni.$emit('switchToProductTab')
	}, 100)
}

// 切换编辑模式
const toggleEditMode = () => {
	isEditMode.value = !isEditMode.value
}

// 切换全选
const toggleSelectAll = () => {
	const newSelectState = !isAllSelected.value
	cartItems.value.forEach(item => {
		item.selected = newSelectState
	})
	saveCartItems()
}

// 切换单个商品选择
const toggleItemSelect = (itemId) => {
	const item = cartItems.value.find(item => item.id === itemId)
	if (item) {
		item.selected = !item.selected
		saveCartItems()
	}
}

// 增加数量
const increaseQuantity = (itemId) => {
	const item = cartItems.value.find(item => item.id === itemId)
	if (item) {
		item.quantity++
		saveCartItems()
	}
}

// 减少数量
const decreaseQuantity = (itemId) => {
	const item = cartItems.value.find(item => item.id === itemId)
	if (item && item.quantity > 1) {
		item.quantity--
		saveCartItems()
	}
}

// 处理滑动菜单点击
const handleSwipeClick = (e, item, index) => {
	console.log('滑动菜单点击:', e, item, index)

	if (e.index === 0) { // 删除按钮
		deleteCartItem(item, index)
	}
}

// 删除购物车商品
const deleteCartItem = (item, index) => {
	uni.showModal({
		title: $t('shoppingCart.deleteConfirm'),
		content: $t('shoppingCart.deleteItemConfirm', { name: item.name }),
		confirmColor: '#FF3B30',
		success: (res) => {
			if (res.confirm) {
				// 从购物车列表中删除
				cartItems.value.splice(index, 1)

				// 保存到本地存储
				saveCartItems()

				uni.showToast({
					title: $t('shoppingCart.deleteSuccess'),
					icon: 'success'
				})

				console.log('商品删除成功，剩余商品数量:', cartItems.value.length)
			}
		}
	})
}

// 删除全部选中商品
const deleteAllSelected = () => {
	const selectedItems = cartItems.value.filter(item => item.selected)
	if (selectedItems.length === 0) {
		uni.showToast({
			title: $t('shoppingCart.selectItemsToDelete'),
			icon: 'none'
		})
		return
	}

	uni.showModal({
		title: $t('shoppingCart.deleteConfirm'),
		content: $t('shoppingCart.deleteAllConfirm', { count: selectedItems.length }),
		confirmColor: '#FF3B30',
		success: (res) => {
			if (res.confirm) {
				// 删除所有选中的商品
				cartItems.value = cartItems.value.filter(item => !item.selected)

				// 保存到本地存储
				saveCartItems()

				uni.showToast({
					title: $t('shoppingCart.deleteAllSuccess', { count: selectedItems.length }),
					icon: 'success'
				})

				console.log('批量删除成功，剩余商品数量:', cartItems.value.length)
			}
		}
	})
}

// 结算
const checkout = () => {
	const selectedItems = cartItems.value.filter(item => item.selected)
	if (selectedItems.length === 0) {
		uni.showToast({
			title: $t('shoppingCart.selectItemsToCheckout'),
			icon: 'none'
		})
		return
	}

	uni.showToast({
		title: $t('shoppingCart.checkoutItems', { count: selectedItems.length }),
		icon: 'success'
	})
}

// 页面生命周期
onMounted(() => {
	getCartItems()
})

onShow(() => {
	getCartItems()
})

// 监听购物车更新
uni.$on('refreshCart', () => {
	getCartItems()
})

onUnmounted(() => {
	uni.$off('refreshCart')
})
</script>
<style lang="scss">
.cart-container {
	min-height: 100vh;
	background: #f5f5f5;
	display: flex;
	flex-direction: column;
}

/* 空状态样式 */
.empty-state {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 200rpx 60rpx;
	text-align: center;
}

.empty-icon {
	margin-bottom: 60rpx;
}

.cart-icon-bg {
	width: 200rpx;
	height: 200rpx;
	background: #e8f5e8;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.empty-title {
	font-size: 32rpx;
	font-weight: 400;
	color: #333;
	margin-bottom: 20rpx;
}

.empty-subtitle {
	font-size: 26rpx;
	color: #999;
	margin-bottom: 60rpx;
}

.go-shopping-btn {
	background: #52c41a;
	color: white;
	padding: 24rpx 80rpx;
	border-radius: 50rpx;
	font-size: 28rpx;
}

.btn-text {
	color: white;
	font-weight: 500;
}

/* 有商品状态 */
.cart-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.header-actions {
	padding: 20rpx 32rpx;
	background: white;
	display: flex;
	justify-content: flex-end;
	border-bottom: 1rpx solid #f0f0f0;
}

.edit-btn {
	color: #52c41a;
	font-size: 28rpx;
}

/* 全选区域 */
.select-all-section {
	background: white;
	padding: 24rpx 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1rpx solid #f0f0f0;
}

.select-all-item {
	display: flex;
	align-items: center;
}

.checkbox {
	width: 40rpx;
	height: 40rpx;
	border: 2rpx solid #d9d9d9;
	border-radius: 50%;
	margin-right: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.checkbox.checked {
	border-color: #52c41a;
	background: #52c41a;
}

.select-text {
	font-size: 28rpx;
	color: #333;
}

.total-count {
	font-size: 26rpx;
	color: #999;
}

/* 商品列表 */
.cart-list {
	flex: 1;
	background: white;
}

.cart-item {
	display: flex;
	align-items: flex-start;
	padding: 24rpx 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.item-checkbox {
	margin-right: 20rpx;
	padding-top: 20rpx;
}

.item-image {
	width: 120rpx;
	height: 120rpx;
	margin-right: 20rpx;
	border-radius: 12rpx;
	overflow: hidden;
	background: #f5f5f5;
}

.product-image {
	width: 100%;
	height: 100%;
}

.item-info {
	flex: 1;
	margin-right: 20rpx;
}

.product-name {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
}

.product-desc {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 12rpx;
	display: block;
}

.price-section {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;
}

.current-price {
	font-size: 28rpx;
	font-weight: 600;
	color: #52c41a;
	margin-right: 16rpx;
}

.original-price {
	font-size: 24rpx;
	color: #999;
	text-decoration: line-through;
}

.subtotal {
	font-size: 24rpx;
	color: #666;
	display: block;
}

/* 数量控制 */
.quantity-controls-wrapper {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.quantity-controls {
	display: flex;
	align-items: center;
	background: #f5f5f5;
	border-radius: 20rpx;
	padding: 8rpx;
}

.quantity-btn {
	width: 48rpx;
	height: 48rpx;
	background: white;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.quantity-text {
	font-size: 26rpx;
	color: #333;
	margin: 0 20rpx;
	min-width: 40rpx;
	text-align: center;
}

/* 滑动提示图标 */
.swipe-hint-icon {
	position: absolute;
	bottom: 30rpx;
	right: 50rpx;
	z-index: 10;
}

.swipe-hint-icon .slide-icon {
	width: 50rpx;
	height: 50rpx;
	opacity: 0.6;
	filter: brightness(0.5);
}

/* 固定删除按钮 */
.fixed-delete-btn {
	position: fixed;
	right: 32rpx;
	bottom: 200rpx;
	z-index: 999;
	transform: translateY(100rpx);
	opacity: 0;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fixed-delete-btn.show {
	transform: translateY(0);
	opacity: 1;
}

.delete-btn-content {
	background: rgba(255, 59, 48, 0.9);
	backdrop-filter: blur(10rpx);
	border-radius: 50rpx;
	padding: 20rpx 32rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 8rpx 24rpx rgba(255, 59, 48, 0.3);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.delete-icon {
	margin-right: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.delete-text {
	color: #ffffff;
	font-size: 26rpx;
	font-weight: 500;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 底部结算栏 */
.bottom-bar {
	background: white;
	padding: 24rpx 32rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-top: 1rpx solid #f0f0f0;
	box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.selected-info {
	flex: 1;
}

.selected-count {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.total-price {
	display: flex;
	align-items: center;
}

.total-label {
	font-size: 26rpx;
	color: #333;
}

.total-amount {
	font-size: 32rpx;
	font-weight: 600;
	color: #52c41a;
}

.checkout-btn {
	background: #52c41a;
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 50rpx;
	margin-left: 32rpx;
}

.checkout-text {
	color: white;
	font-size: 28rpx;
	font-weight: 500;
}

/* 滑动删除样式 */
.custom-swipe-action {
	margin-bottom: 0;
}

/* 滑动删除按钮样式 */
::v-deep .fui-swipe__btn {
	height: 100% !important;
	min-height: 100% !important;
	border-radius: 0 !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
}

/* 更通用的选择器适配不同版本的组件 */
::v-deep [class*="swipe"] [class*="btn"] {
	height: 100% !important;
	min-height: 100% !important;
	border-radius: 0 !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
}
</style>
