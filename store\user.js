import { ref, computed } from 'vue'
import { processAvatarUrl } from '@/request/avatar.js'

// 全局用户状态管理
const isLoggedIn = ref(false)
const userInfo = ref({
  name: '',
  phone: '',
  avatar: '/static/icon/user.svg'
})

// 初始化用户状态
const initUserState = () => {
  const auth = uni.getStorageSync('auth') || uni.getStorageSync('is_logged_in')
  const savedUserInfo = uni.getStorageSync('userInfo')
  const sessionKey = uni.getStorageSync('auth_token') || uni.getStorageSync('session_key')

  // 检查是否有有效的登录凭证
  if ((auth || sessionKey) && savedUserInfo) {
    // 使用通用函数处理头像URL
    const avatarUrl = processAvatarUrl(savedUserInfo.avatar)

    isLoggedIn.value = true
    userInfo.value = {
      name: savedUserInfo.nickname || savedUserInfo.name || '微信用户',
      phone: savedUserInfo.phone || '未绑定手机号',
      avatar: avatarUrl
    }
  } else {
    isLoggedIn.value = false
    userInfo.value = {
      name: '微信用户',
      phone: '未绑定手机号',
      avatar: '/static/icon/user.svg'
    }
  }
}

// 登录成功
const loginSuccess = (userData) => {
  console.log('🔄 loginSuccess 被调用，接收到的数据:', userData)

  // 使用通用函数处理头像URL
  const avatarUrl = processAvatarUrl(userData.avatar)
  console.log('🖼️ 处理后的头像URL:', avatarUrl)

  // 处理昵称
  const nickname = userData.nickname || userData.name || '微信用户'
  console.log('👤 处理后的昵称:', nickname)

  userInfo.value = {
    name: nickname,
    phone: userData.phone || '未绑定手机号',
    avatar: avatarUrl
  }

  isLoggedIn.value = true

  console.log('📋 更新后的userInfo.value:', userInfo.value)
  console.log('🔐 更新后的isLoggedIn.value:', isLoggedIn.value)

  // 保存到本地存储
  uni.setStorageSync('auth', true)
  uni.setStorageSync('is_logged_in', true)
  uni.setStorageSync('userInfo', {
    id: userData.id,
    nickname: nickname,
    name: nickname,
    phone: userData.phone || '未绑定手机号',
    avatar: userData.avatar, // 保存原始avatar字段
    auth: userData.auth,
    vip: userData.vip,
    money: userData.money,
    integral: userData.integral
  })

  console.log('✅ 用户登录成功，最终userInfo:', userInfo.value)
  console.log('📋 保存到本地存储的数据:', {
    nickname: nickname,
    avatar: userData.avatar,
    phone: userData.phone
  })

  // 触发响应式更新
  console.log('🔄 触发响应式更新...')
}

// 退出登录
const logout = () => {
  isLoggedIn.value = false
  userInfo.value = {
    name: '',
    phone: '',
    avatar: '/static/icon/user.svg'
  }
  
  // 清除本地存储
  uni.removeStorageSync('auth')
  uni.removeStorageSync('userInfo')
  
  console.log('用户已退出登录')
}

// 更新用户信息
const updateUserInfo = (newUserInfo) => {
  userInfo.value = { ...userInfo.value, ...newUserInfo }
  
  // 更新本地存储
  if (isLoggedIn.value) {
    uni.setStorageSync('userInfo', {
      nickname: userInfo.value.name,
      phone: userInfo.value.phone,
      avatar: userInfo.value.avatar
    })
  }
}

// 导出状态和方法
export const useUserStore = () => {
  return {
    // 状态
    isLoggedIn: computed(() => isLoggedIn.value),
    userInfo: computed(() => userInfo.value),
    
    // 方法
    initUserState,
    loginSuccess,
    logout,
    updateUserInfo
  }
}

// 直接导出响应式状态（用于非组合式API）
export {
  isLoggedIn,
  userInfo,
  initUserState,
  loginSuccess,
  logout,
  updateUserInfo
}
