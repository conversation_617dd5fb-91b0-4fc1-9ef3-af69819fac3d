// 🔐 用户认证管理模块
// 用于保存和管理用户token、session_key等认证信息

/**
 * 保存用户认证信息
 * @param {Object} authData - 认证数据
 * @param {string} authData.session_key - 会话密钥
 * @param {Object} authData.data - 用户数据
 */
export const saveAuthInfo = (authData) => {
  try {
    console.log('💾 保存用户认证信息:', authData)
    
    // 保存auth_token
    if (authData.session_key) {
      uni.setStorageSync('auth_token', authData.session_key)
      uni.setStorageSync('session_key', authData.session_key) // 保持兼容性
      console.log('✅ auth_token已保存:', authData.session_key)
    }
    
    // 保存用户数据
    if (authData.data) {
      uni.setStorageSync('user_info', authData.data)
      console.log('✅ 用户信息已保存:', authData.data)
    }
    
    // 保存完整的认证响应
    uni.setStorageSync('auth_response', authData)
    
    // 设置登录状态
    uni.setStorageSync('is_logged_in', true)
    
    // 保存登录时间
    uni.setStorageSync('login_time', new Date().toISOString())
    
    console.log('🎉 用户认证信息保存完成')
    return true
    
  } catch (error) {
    console.error('❌ 保存认证信息失败:', error)
    return false
  }
}

/**
 * 获取auth_token
 * @returns {string|null} auth_token
 */
export const getSessionKey = () => {
  try {
    const authToken = uni.getStorageSync('auth_token') || uni.getStorageSync('session_key')
    console.log('🔑 获取auth_token:', authToken ? '已获取' : '未找到')
    return authToken || null
  } catch (error) {
    console.error('❌ 获取auth_token失败:', error)
    return null
  }
}

/**
 * 获取用户信息
 * @returns {Object|null} 用户信息
 */
export const getUserInfo = () => {
  try {
    const userInfo = uni.getStorageSync('user_info')
    console.log('👤 获取用户信息:', userInfo ? `用户ID: ${userInfo.id}` : '未找到')
    return userInfo || null
  } catch (error) {
    console.error('❌ 获取用户信息失败:', error)
    return null
  }
}

/**
 * 检查是否已登录
 * @returns {boolean} 是否已登录
 */
export const isLoggedIn = () => {
  try {
    const loggedIn = uni.getStorageSync('is_logged_in')
    const sessionKey = getSessionKey()
    const userInfo = getUserInfo()
    
    const isValid = loggedIn && sessionKey && userInfo
    console.log('🔍 登录状态检查:', isValid ? '已登录' : '未登录')
    return isValid
  } catch (error) {
    console.error('❌ 检查登录状态失败:', error)
    return false
  }
}

/**
 * 获取认证头信息
 * @returns {Object} 认证头
 */
export const getAuthHeaders = () => {
  const sessionKey = getSessionKey()
  const userInfo = getUserInfo()
  
  const headers = {
    'Content-Type': 'application/json'
  }
  
  if (sessionKey) {
    headers['Authorization'] = `Bearer ${sessionKey}`
    headers['Session-Key'] = sessionKey
  }
  
  if (userInfo && userInfo.id) {
    headers['User-ID'] = userInfo.id.toString()
  }
  
  console.log('📋 生成认证头:', headers)
  return headers
}

/**
 * 清除认证信息（退出登录）
 */
export const clearAuthInfo = () => {
  try {
    console.log('🧹 清除用户认证信息')
    
    uni.removeStorageSync('auth_token')
    uni.removeStorageSync('session_key')
    uni.removeStorageSync('user_info')
    uni.removeStorageSync('auth_response')
    uni.removeStorageSync('is_logged_in')
    uni.removeStorageSync('login_time')
    
    console.log('✅ 认证信息已清除')
    return true
    
  } catch (error) {
    console.error('❌ 清除认证信息失败:', error)
    return false
  }
}

/**
 * 更新用户信息
 * @param {Object} newUserInfo - 新的用户信息
 */
export const updateUserInfo = (newUserInfo) => {
  try {
    console.log('🔄 更新用户信息:', newUserInfo)
    
    const currentUserInfo = getUserInfo() || {}
    const updatedUserInfo = { ...currentUserInfo, ...newUserInfo }
    
    uni.setStorageSync('user_info', updatedUserInfo)
    console.log('✅ 用户信息已更新')
    
    return updatedUserInfo
  } catch (error) {
    console.error('❌ 更新用户信息失败:', error)
    return null
  }
}

/**
 * 检查token是否过期
 * @returns {boolean} 是否过期
 */
export const isTokenExpired = () => {
  try {
    const loginTime = uni.getStorageSync('login_time')
    if (!loginTime) return true
    
    const loginDate = new Date(loginTime)
    const now = new Date()
    const diffHours = (now - loginDate) / (1000 * 60 * 60)
    
    // 假设token有效期为24小时
    const isExpired = diffHours > 24
    console.log('⏰ Token过期检查:', isExpired ? '已过期' : '有效')
    
    return isExpired
  } catch (error) {
    console.error('❌ 检查token过期失败:', error)
    return true
  }
}

/**
 * 获取用户角色信息
 * @returns {Object} 用户角色
 */
export const getUserRole = () => {
  const userInfo = getUserInfo()
  if (!userInfo) return { role: 'guest', permissions: [] }
  
  return {
    role: userInfo.is_admin ? 'admin' : 
          userInfo.is_doctor ? 'doctor' : 
          userInfo.is_referrer ? 'referrer' : 'user',
    isAdmin: userInfo.is_admin || false,
    isDoctor: userInfo.is_doctor || false,
    isReferrer: userInfo.is_referrer || false,
    isVip: userInfo.vip || false,
    level: userInfo.level_name || '普通用户'
  }
}

// 默认导出所有函数
export default {
  saveAuthInfo,
  getSessionKey,
  getUserInfo,
  isLoggedIn,
  getAuthHeaders,
  clearAuthInfo,
  updateUserInfo,
  isTokenExpired,
  getUserRole
}
