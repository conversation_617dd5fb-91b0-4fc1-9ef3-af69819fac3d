<template>
	<!--
		💊 健康档案页面模板
		这是用户查看和管理个人健康档案的页面
		包含：基本健康信息、用药记录、体检数据、健康指标等
		支持空状态显示和数据状态显示两种模式
	-->

	<!--
		📄 健康档案页面容器
		- class="health-records-container": 基础页面样式
		- :class: 动态样式类，包括：
		  * fontSizeClass: 字体大小样式类
		  * fontClass: 字体类型样式类（支持维吾尔文等）
		  * pageClass: 页面布局样式类（RTL支持）
		- :style: 动态样式，主要用于RTL布局
		- :key: 强制重新渲染的键，当字体变化时重新渲染
	-->
	<view class="health-records-container" :class="[fontSizeClass, fontClass, pageClass]" :style="pageStyle" :key="fontSizeUpdateKey">
		<!--
			🈳 空状态显示
			v-if="!hasHealthData": 当用户没有健康档案数据时显示
			提示用户创建健康档案
		-->
		<view v-if="!hasHealthData" class="empty-state">
			<!-- 空状态图标区域 -->
			<view class="empty-icon">
				<!-- 盾牌图标容器 -->
				<view class="shield-icon">
					<!--
						设置图标
						使用FirstUI的setup图标表示需要设置健康档案
						:size="120": 图标大小120px
						color="#999": 灰色图标
					-->
					<fui-icon name="setup" :size="120" color="#999"></fui-icon>
				</view>
			</view>

			<!--
				空状态标题
				$t('healthRecords.noHealthData'): 从语言包获取"暂无健康数据"文字
			-->
			<text class="empty-title">{{ $t('healthRecords.noHealthData') }}</text>

			<!--
				创建健康档案按钮
				@click: 点击时创建新的健康档案
			-->
			<view class="create-btn" @click="createHealthRecord">
				<!--
					按钮文字
					$t('healthRecords.createHealthRecord'): 从语言包获取"创建健康档案"文字
				-->
				<text class="btn-text">{{ $t('healthRecords.createHealthRecord') }}</text>
			</view>
		</view>

		<!--
			📊 有数据状态显示
			v-else: 当用户有健康档案数据时显示
			显示各种健康数据卡片
		-->
		<view v-else>
			<!--
				🔄 健康卡片循环
				v-for: 遍历healthCards数组，显示各种健康数据卡片
				:key: 每个卡片的唯一标识
			-->
			<view
				v-for="card in healthCards"
				:key="card.key"
				class="health-card"
			>
				<!-- 📋 卡片头部 -->
				<view class="card-header">
					<!--
						卡片图标
						:class: 动态样式类，不同卡片有不同的图标样式
					-->
					<view class="card-icon" :class="card.iconClass">
						<!--
							FirstUI图标
							:name: 图标名称，来自卡片配置
							:size: 图标大小32px
							:color: 图标颜色，来自卡片配置
						-->
						<fui-icon :name="card.iconName" :size="32" :color="card.iconColor"></fui-icon>
					</view>

					<!--
						卡片标题
						$t(card.titleKey): 从语言包获取卡片标题文字
					-->
					<text class="card-title">{{ $t(card.titleKey) }}</text>
				</view>

				<!-- 📊 卡片内容 -->
				<view class="card-content">
					<!--
						🔄 信息行循环
						v-for: 遍历当前卡片的信息项目
						:key: 每个信息项的唯一标识
					-->
					<view
						v-for="info in card.infoItems"
						:key="info.key"
						class="info-row"
					>
						<!--
							信息标签
							$t(info.labelKey): 从语言包获取信息标签文字，如"血压"、"心率"等
						-->
						<text class="info-label">{{ $t(info.labelKey) }}</text>

						<!--
							信息数值
							info.value: 具体的健康数据值，如"120/80"、"72次/分"等
						-->
						<text class="info-value">{{ info.value }}</text>
					</view>
				</view>
			</view>

		<!--
			💊 当前用药卡片
			显示用户当前正在服用的药物信息
		-->
		<view class="health-card">
			<!-- 用药卡片头部 -->
			<view class="card-header">
				<!-- 用药图标 -->
				<view class="card-icon medication-icon">
					<!--
						药品图标
						使用FirstUI的goods图标表示药品
						color="#2196F3": 蓝色图标
					-->
					<fui-icon name="goods" :size="32" color="#2196F3"></fui-icon>
				</view>

				<!--
					用药卡片标题
					$t('healthRecords.currentMedication'): 从语言包获取"当前用药"文字
				-->
				<text class="card-title">{{ $t('healthRecords.currentMedication') }}</text>
			</view>

			<!-- 用药卡片内容 -->
			<view class="card-content">
				<view class="info-row">
					<text class="info-label">{{ $t('healthRecords.hasMedicationDesc') }}</text>
					<text class="info-value">{{ displayData.hasMedication }}</text>
				</view>
			</view>
		</view>

		<!-- 生活方式卡片 -->
		<view class="health-card">
			<view class="card-header">
				<view class="card-icon lifestyle-icon">
					<fui-icon name="star" :size="32" color="#9C27B0"></fui-icon>
				</view>
				<text class="card-title">{{ $t('healthRecords.lifestyle') }}</text>
			</view>
			<view class="card-content">
				<view class="info-row">
					<text class="info-label">{{ $t('healthRecords.exerciseFrequencyLabel') }}</text>
					<text class="info-value">{{ displayData.exerciseFrequency }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">{{ $t('healthRecords.smokingStatusLabel') }}</text>
					<text class="info-value">{{ displayData.smokingHistory }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">{{ $t('healthRecords.drinkingStatusLabel') }}</text>
					<text class="info-value">{{ displayData.drinkingHistory }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">{{ $t('healthRecords.sleepDurationLabel') }}</text>
					<text class="info-value">{{ displayData.sleepDuration }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">{{ $t('healthRecords.sleepQualityLabel') }}</text>
					<text class="info-value">{{ displayData.sleepQuality }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">{{ $t('healthRecords.stressLevelLabel') }}</text>
					<text class="info-value">{{ displayData.stressLevel }}</text>
				</view>
			</view>
		</view>
	</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useFontSizePage } from '@/utils/fontSizeMixin.js'
import { getCurrentUserInfo, onUserInfoUpdated, offUserInfoUpdated } from '@/request/userSync.js'
import { useAppStore } from '@/store/app.js'
import { t } from '@/locale/index.js'
import { useRTLPage } from '@/utils/rtlMixin.js'

// 应用状态管理
const appStore = useAppStore()

// RTL布局支持
const { pageClass, pageStyle, isRTL } = useRTLPage()

// 计算字体类（合并RTL类）
const fontClass = computed(() => ({
	...pageClass.value,
	'ug': appStore.isUyghur,
	[`lang-${appStore.lang}`]: true
}))

// 翻译方法
const $t = (key, params) => t(key, params)

// 字体大小功能
const { fontSizeClass, fontSizeUpdateKey } = useFontSizePage()

// 用户健康数据
const healthData = ref({})

// 选项配置（与EditProfile保持一致）
const bloodTypeOptions = ['不清楚', 'A型', 'B型', 'AB型', 'O型']
const exerciseOptions = ['久坐(基本不运动)', '轻度运动', '中度运动', '高强度运动']
const smokingOptions = ['从不吸烟', '偶尔吸烟', '经常吸烟', '已戒烟']
const drinkingOptions = ['从不饮酒', '偶尔饮酒', '经常饮酒', '已戒酒']
const sleepOptions = ['7-8小时', '6-7小时', '8-9小时', '少于6小时', '超过9小时']
const sleepQualityOptions = ['很好(很少入睡困难)', '一般', '较差', '很差']
const stressOptions = ['很小', '一般', '较大', '很大']

// 计算属性 - 动态显示数据
const hasHealthData = computed(() => {
	return healthData.value && (
		healthData.value.height ||
		healthData.value.weight ||
		healthData.value.bloodType ||
		healthData.value.hasAllergy !== undefined ||
		healthData.value.hasChronicDisease !== undefined ||
		healthData.value.hasMedication !== undefined ||
		healthData.value.exerciseFrequency !== undefined
	)
})

// 格式化显示的数据
const displayData = computed(() => {
	const data = healthData.value
	return {
		// 基本信息
		height: data.height ? `${data.height} cm` : '--',
		weight: data.weight ? `${data.weight} kg` : '--',
		bloodType: bloodTypeOptions[data.bloodType] || '--',

		// 健康状态
		hasAllergy: data.hasAllergy ? '是' : '否',
		hasChronicDisease: data.hasChronicDisease ? '是' : '否',
		hasMedication: data.hasMedication ? '是' : '否',

		// 生活方式
		exerciseFrequency: exerciseOptions[data.exerciseFrequency] || '--',
		smokingHistory: smokingOptions[data.smokingHistory] || '--',
		drinkingHistory: drinkingOptions[data.drinkingHistory] || '--',
		sleepDuration: sleepOptions[data.sleepDuration] || '--',
		sleepQuality: sleepQualityOptions[data.sleepQuality] || '--',
		stressLevel: stressOptions[data.stressLevel] || '--'
	}
})

// 健康卡片配置数组
const healthCards = computed(() => [
	{
		key: 'basicInfo',
		iconClass: 'basic-info-icon',
		iconName: 'my',
		iconColor: '#4CAF50',
		titleKey: 'healthRecords.basicInfo',
		infoItems: [
			{ key: 'height', labelKey: 'user.height', value: displayData.value.height },
			{ key: 'weight', labelKey: 'user.weight', value: displayData.value.weight },
			{ key: 'bloodType', labelKey: 'user.bloodType', value: displayData.value.bloodType }
		]
	},
	{
		key: 'allergyHistory',
		iconClass: 'allergy-icon',
		iconName: 'warning',
		iconColor: '#FF9800',
		titleKey: 'healthRecords.allergyHistory',
		infoItems: [
			{ key: 'hasAllergy', labelKey: 'healthRecords.hasAllergyDesc', value: displayData.value.hasAllergy }
		]
	},
	{
		key: 'chronicDiseaseHistory',
		iconClass: 'chronic-icon',
		iconName: 'help',
		iconColor: '#E91E63',
		titleKey: 'healthRecords.chronicDiseaseHistory',
		infoItems: [
			{ key: 'hasChronicDisease', labelKey: 'healthRecords.hasChronicDiseaseDesc', value: displayData.value.hasChronicDisease }
		]
	}
])

// 加载健康数据
const loadHealthData = () => {
	try {
		const userInfo = getCurrentUserInfo()
		healthData.value = userInfo
		console.log('加载健康数据:', userInfo)
	} catch (error) {
		console.error('加载健康数据失败:', error)
		healthData.value = {}
	}
}

// 创建健康档案 - 跳转到编辑页面
const createHealthRecord = () => {
	uni.navigateTo({
		url: '/pages/My/EditProfile/EditProfile'
	})
}

// 用户信息更新回调
const handleUserInfoUpdate = (newUserInfo) => {
	console.log('健康档案页面接收到用户信息更新:', newUserInfo)
	healthData.value = newUserInfo
}

// 页面生命周期
onMounted(() => {
	loadHealthData()
	onUserInfoUpdated(handleUserInfoUpdate)

	// 监听页面显示时的重新加载事件
	uni.$on('reloadHealthData', loadHealthData)
})

// 页面卸载时清理监听
onUnmounted(() => {
	offUserInfoUpdated(handleUserInfoUpdate)
	uni.$off('reloadHealthData', loadHealthData)
})
</script>

<script>
// 使用uni-app的页面生命周期
export default {
	onShow() {
		// 页面显示时重新加载数据
		console.log('页面显示，重新加载健康数据')
		// 由于setup中的函数无法直接访问，我们使用事件通信
		uni.$emit('reloadHealthData')
	}
}
</script>

<style lang="scss">
/* 页面容器 */
.health-records-container {
	padding: 32rpx 32rpx 180rpx 32rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 空状态样式 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	min-height: 60vh;
	padding: 80rpx 40rpx;
}

.empty-icon {
	margin-bottom: 40rpx;
}

.shield-icon {
	width: 160rpx;
	height: 160rpx;
	background-color: #f0f0f0;
	border-radius: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.empty-title {
	font-size: 32rpx;
	color: #999;
	margin-bottom: 60rpx;
	text-align: center;
}

.create-btn {
	background-color: #4CAF50;
	border-radius: 48rpx;
	padding: 24rpx 48rpx;
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.btn-text {
	font-size: 32rpx;
	color: white;
	font-weight: 500;
}

/* 健康卡片样式 */
.health-card {
	background: white;
	border-radius: 24rpx;
	padding: 32rpx;
	margin-bottom: 24rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

/* 卡片头部 */
.card-header {
	display: flex;
	align-items: center;
	margin-bottom: 32rpx;
}

/* 卡片图标容器 */
.card-icon {
	width: 64rpx;
	height: 64rpx;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

/* 不同卡片的图标背景色 */
.basic-info-icon {
	background-color: rgba(76, 175, 80, 0.1);
}

.allergy-icon {
	background-color: rgba(255, 152, 0, 0.1);
}

.chronic-icon {
	background-color: rgba(233, 30, 99, 0.1);
}

.medication-icon {
	background-color: rgba(33, 150, 243, 0.1);
}

.lifestyle-icon {
	background-color: rgba(156, 39, 176, 0.1);
}

/* 卡片标题 */
.card-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

/* 卡片内容区域 */
.card-content {
	padding-left: 0;
}

/* 信息行 - 使用相对定位确保完美对齐 */
.info-row {
	position: relative;
	margin-bottom: 36rpx;
	min-height: 48rpx;
	padding-right: 10rpx;
}

.info-row:last-child {
	margin-bottom: 0;
}

/* 信息标签 - 左侧标签，可以换行 */
.info-label {
	font-size: 28rpx;
	color: #666;
	line-height: 48rpx;
	// word-wrap: break-word;
	// word-break: break-all;
}

/* 信息值 - 绝对定位右对齐，所有值在同一垂直线 */
.info-value {
	position: absolute;
	right: 0;
	top: 0;
	font-size: 28rpx;
	color: #333;
	font-weight: 400;
	line-height: 48rpx;
	// text-align: right;
	width: 140rpx;
}

/* 字体大小响应式样式 */
.font-size-small {
	.card-title {
		font-size: 28rpx !important;
	}

	.info-label {
		font-size: 24rpx !important;
	}

	.info-value {
		font-size: 24rpx !important;
	}

	.empty-title {
		font-size: 28rpx !important;
	}

	.btn-text {
		font-size: 28rpx !important;
	}
}

.font-size-medium {
	.card-title {
		font-size: 32rpx !important;
	}

	.info-label {
		font-size: 28rpx !important;
	}

	.info-value {
		font-size: 28rpx !important;
	}

	.empty-title {
		font-size: 32rpx !important;
	}

	.btn-text {
		font-size: 32rpx !important;
	}
}

.font-size-large {
	.card-title {
		font-size: 36rpx !important;
	}

	.info-label {
		font-size: 32rpx !important;
	}

	.info-value {
		font-size: 32rpx !important;
	}

	.empty-title {
		font-size: 36rpx !important;
	}

	.btn-text {
		font-size: 36rpx !important;
	}
}
</style>