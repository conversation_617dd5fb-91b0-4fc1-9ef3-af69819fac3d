/**
 * 数据模型定义
 * 用于统一管理应用中的数据结构
 */

// 🏥 医生数据模型
export class Doctor {
  constructor(data = {}) {
    this.id = data.id || ''
    this.name = data.name || ''
    this.avatar = data.avatar || '/static/images/doctor-avatar.png'
    this.title = data.title || ''
    this.hospital = data.hospital || ''
    this.department = data.department || ''
    this.experience = data.experience || ''
    this.specialty = data.specialty || []
    this.phone = data.phone || ''
    this.address = data.address || ''
    this.rating = data.rating || 0
    this.consultationFee = data.consultationFee || 0
    this.description = data.description || ''
    this.isDoctor = data.is_doctor || false
    this.doctorId = data.doctor_id || null
  }

  // 获取专长显示文本
  getSpecialtyText() {
    return Array.isArray(this.specialty) ? this.specialty.join('、') : this.specialty
  }

  // 获取评分星级
  getStarRating() {
    return Math.floor(this.rating)
  }
}

// 🛒 产品数据模型
export class Product {
  constructor(data = {}) {
    this.id = data.id || ''
    this.name = data.name || ''
    this.description = data.description || ''
    this.detailedDescription = data.detailed_description || data.detailedDescription || ''
    this.price = data.price || 0
    this.originalPrice = data.original_price || data.originalPrice || 0
    this.manufacturer = data.manufacturer || ''
    this.mainImageUrl = data.main_image_url || data.mainImageUrl || '/static/images/default-product.png'
    this.imageUrls = data.image_urls || data.imageUrls || []
    this.category = data.category || ''
    this.specifications = data.specifications || {}
    this.inventoryCount = data.inventory_count || data.inventoryCount || 0
    this.salesCount = data.sales_count || data.salesCount || 0
    this.status = data.status || 0
    this.isActive = data.is_active || data.isActive || true
    this.doctorId = data.doctor_id || data.doctorId || null
    this.doctorName = data.doctor_name || data.doctorName || ''
    this.createdAt = data.created_at || data.createdAt || ''
    this.updatedAt = data.updated_at || data.updatedAt || ''
  }

  // 获取折扣百分比
  getDiscountPercent() {
    if (this.originalPrice && this.originalPrice > this.price) {
      return Math.round((1 - this.price / this.originalPrice) * 100)
    }
    return 0
  }

  // 获取状态文本
  getStatusText() {
    const statusMap = {
      0: '待审核',
      1: '审核通过',
      2: '审核拒绝',
      3: '下架'
    }
    return statusMap[this.status] || '未知'
  }

  // 是否有库存
  hasStock() {
    return this.inventoryCount > 0
  }

  // 获取规格显示文本
  getSpecificationsText() {
    if (!this.specifications || typeof this.specifications !== 'object') {
      return ''
    }
    return Object.entries(this.specifications)
      .map(([key, value]) => `${key}: ${value}`)
      .join(' | ')
  }
}

// 👤 用户数据模型
export class User {
  constructor(data = {}) {
    this.id = data.id || ''
    this.nickname = data.nickname || data.username || ''
    this.phone = data.phone || ''
    this.avatar = data.avatar || '/static/images/default-avatar.png'
    this.email = data.email || ''
    this.sex = data.sex || 0
    this.birthday = data.birthday || ''
    this.money = data.money || 0
    this.integral = data.integral || 0
    this.sessionKey = data.session_key || data.sessionKey || ''
    this.auth = data.auth || false
    this.vip = data.vip || false
    this.isDoctor = data.is_doctor || data.isDoctor || false
    this.doctorId = data.doctor_id || data.doctorId || null
    this.token = data.token || ''
    this.loginTime = data.loginTime || new Date().toISOString()
  }

  // 获取性别文本
  getSexText() {
    const sexMap = {
      0: '未知',
      1: '男',
      2: '女'
    }
    return sexMap[this.sex] || '未知'
  }

  // 是否为VIP用户
  isVipUser() {
    return this.vip === true || this.vip === 1
  }

  // 是否为医生用户
  isDoctorUser() {
    return this.isDoctor === true || this.isDoctor === 1
  }
}

// 📦 订单数据模型
export class Order {
  constructor(data = {}) {
    this.id = data.id || ''
    this.orderSn = data.order_sn || data.orderSn || ''
    this.productId = data.product_id || data.productId || ''
    this.productName = data.product_name || data.productName || ''
    this.productImage = data.product_image || data.productImage || ''
    this.quantity = data.quantity || 1
    this.unitPrice = data.unit_price || data.unitPrice || 0
    this.totalAmount = data.total_amount || data.totalAmount || 0
    this.payStatus = data.pay_status || data.payStatus || 0
    this.orderStatus = data.order_status || data.orderStatus || 0
    this.shippingAddress = data.shipping_address || data.shippingAddress || ''
    this.shippingPhone = data.shipping_phone || data.shippingPhone || ''
    this.shippingName = data.shipping_name || data.shippingName || ''
    this.trackingNumber = data.tracking_number || data.trackingNumber || ''
    this.payTime = data.pay_time || data.payTime || ''
    this.shipTime = data.ship_time || data.shipTime || ''
    this.completeTime = data.complete_time || data.completeTime || ''
    this.createdAt = data.created_at || data.createdAt || ''
    this.doctorId = data.doctor_id || data.doctorId || null
    this.doctorName = data.doctor_name || data.doctorName || ''
    this.userId = data.user_id || data.userId || ''
    this.userNickname = data.user_nickname || data.userNickname || ''
  }

  // 获取支付状态文本
  getPayStatusText() {
    const statusMap = {
      0: '未支付',
      1: '已支付',
      2: '已退款'
    }
    return statusMap[this.payStatus] || '未知'
  }

  // 获取订单状态文本
  getOrderStatusText() {
    const statusMap = {
      0: '待支付',
      1: '待发货',
      2: '已发货',
      3: '已完成',
      4: '已取消'
    }
    return statusMap[this.orderStatus] || '未知'
  }

  // 是否可以取消
  canCancel() {
    return this.orderStatus === 0 || this.orderStatus === 1
  }

  // 是否可以支付
  canPay() {
    return this.orderStatus === 0 && this.payStatus === 0
  }
}

// 📍 地址数据模型
export class Address {
  constructor(data = {}) {
    this.id = data.id || ''
    this.name = data.name || ''
    this.phone = data.phone || ''
    this.province = data.province || ''
    this.city = data.city || ''
    this.district = data.district || ''
    this.detail = data.detail || ''
    this.isDefault = data.is_default || data.isDefault || false
    this.createdAt = data.created_at || data.createdAt || ''
  }

  // 获取完整地址
  getFullAddress() {
    return `${this.province}${this.city}${this.district}${this.detail}`
  }

  // 获取地区信息
  getRegionText() {
    return `${this.province} ${this.city} ${this.district}`
  }
}

// 🛒 购物车数据模型
export class CartItem {
  constructor(data = {}) {
    this.id = data.id || ''
    this.productId = data.product_id || data.productId || ''
    this.productName = data.product_name || data.productName || ''
    this.productImage = data.product_image || data.productImage || ''
    this.price = data.price || 0
    this.quantity = data.quantity || 1
    this.selected = data.selected || false
    this.doctorId = data.doctor_id || data.doctorId || null
    this.doctorName = data.doctor_name || data.doctorName || ''
    this.createdAt = data.created_at || data.createdAt || ''
  }

  // 获取小计金额
  getSubtotal() {
    return this.price * this.quantity
  }
}

// 🏥 健康档案数据模型 - 已移除

// 📊 统计数据模型
export class Statistics {
  constructor(data = {}) {
    this.totalProducts = data.total_products || data.totalProducts || 0
    this.pendingReview = data.pending_review || data.pendingReview || 0
    this.approvedProducts = data.approved_products || data.approvedProducts || 0
    this.rejectedProducts = data.rejected_products || data.rejectedProducts || 0
    this.offlineProducts = data.offline_products || data.offlineProducts || 0
    this.totalSales = data.total_sales || data.totalSales || 0
    this.totalOrders = data.total_orders || data.totalOrders || 0
  }
}

// 🔧 工具函数
export const ModelUtils = {
  /**
   * 将API数据转换为对应的模型实例
   * @param {string} modelType - 模型类型
   * @param {Object|Array} data - 数据
   * @returns {Object|Array} 模型实例
   */
  createModel(modelType, data) {
    const modelMap = {
      'doctor': Doctor,
      'product': Product,
      'user': User,
      'order': Order,
      'address': Address,
      'cartItem': CartItem,
      // 'healthProfile': HealthProfile, // 已移除
      'statistics': Statistics
    }

    const ModelClass = modelMap[modelType]
    if (!ModelClass) {
      console.warn(`未知的模型类型: ${modelType}`)
      return data
    }

    if (Array.isArray(data)) {
      return data.map(item => new ModelClass(item))
    } else {
      return new ModelClass(data)
    }
  },

  /**
   * 格式化价格显示
   * @param {number} price - 价格
   * @returns {string} 格式化后的价格
   */
  formatPrice(price) {
    return `¥${parseFloat(price).toFixed(2)}`
  },

  /**
   * 格式化日期显示
   * @param {string} dateString - 日期字符串
   * @returns {string} 格式化后的日期
   */
  formatDate(dateString) {
    if (!dateString) return ''
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}
