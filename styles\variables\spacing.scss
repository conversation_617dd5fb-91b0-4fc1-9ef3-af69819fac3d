// 统一的间距变量系统
// 基于8rpx的间距系统，确保设计一致性

// ==================== 基础间距单位 ====================
$spacing-unit: 8rpx;

// ==================== 间距变量 ====================
$spacing-0: 0;
$spacing-1: #{$spacing-unit * 1};    // 8rpx
$spacing-2: #{$spacing-unit * 2};    // 16rpx
$spacing-3: #{$spacing-unit * 3};    // 24rpx
$spacing-4: #{$spacing-unit * 4};    // 32rpx
$spacing-5: #{$spacing-unit * 5};    // 40rpx
$spacing-6: #{$spacing-unit * 6};    // 48rpx
$spacing-8: #{$spacing-unit * 8};    // 64rpx
$spacing-10: #{$spacing-unit * 10};  // 80rpx
$spacing-12: #{$spacing-unit * 12};  // 96rpx
$spacing-16: #{$spacing-unit * 16};  // 128rpx
$spacing-20: #{$spacing-unit * 20};  // 160rpx
$spacing-24: #{$spacing-unit * 24};  // 192rpx
$spacing-32: #{$spacing-unit * 32};  // 256rpx

// ==================== 语义化间距 ====================
$spacing-xs: $spacing-1;     // 8rpx - 极小间距
$spacing-sm: $spacing-2;     // 16rpx - 小间距
$spacing-md: $spacing-4;     // 32rpx - 中等间距
$spacing-lg: $spacing-6;     // 48rpx - 大间距
$spacing-xl: $spacing-8;     // 64rpx - 特大间距
$spacing-xxl: $spacing-12;   // 96rpx - 超大间距

// ==================== 组件间距 ====================
$component-padding-sm: $spacing-2;   // 16rpx
$component-padding-md: $spacing-4;   // 32rpx
$component-padding-lg: $spacing-6;   // 48rpx

$component-margin-sm: $spacing-2;    // 16rpx
$component-margin-md: $spacing-4;    // 32rpx
$component-margin-lg: $spacing-6;    // 48rpx

// ==================== 页面间距 ====================
$page-padding-horizontal: $spacing-4;  // 32rpx - 页面水平内边距
$page-padding-vertical: $spacing-3;    // 24rpx - 页面垂直内边距

$section-margin-bottom: $spacing-5;    // 40rpx - 区块间距
$card-padding: $spacing-4;             // 32rpx - 卡片内边距
$card-margin: $spacing-3;              // 24rpx - 卡片外边距

// ==================== 表单间距 ====================
$form-item-margin-bottom: $spacing-4;  // 32rpx
$form-label-margin-right: $spacing-2;  // 16rpx
$form-input-padding: $spacing-3;       // 24rpx

// ==================== 按钮间距 ====================
$button-padding-sm: #{$spacing-2} #{$spacing-3};    // 16rpx 24rpx
$button-padding-md: #{$spacing-3} #{$spacing-4};    // 24rpx 32rpx
$button-padding-lg: #{$spacing-4} #{$spacing-6};    // 32rpx 48rpx

$button-margin-right: $spacing-2;      // 16rpx

// ==================== 列表间距 ====================
$list-item-padding: #{$spacing-4} #{$spacing-4};   // 32rpx 32rpx
$list-item-margin-bottom: $spacing-1;               // 8rpx

// ==================== CSS变量映射 ====================
:root {
  // 基础间距
  --spacing-0: #{$spacing-0};
  --spacing-1: #{$spacing-1};
  --spacing-2: #{$spacing-2};
  --spacing-3: #{$spacing-3};
  --spacing-4: #{$spacing-4};
  --spacing-5: #{$spacing-5};
  --spacing-6: #{$spacing-6};
  --spacing-8: #{$spacing-8};
  --spacing-10: #{$spacing-10};
  --spacing-12: #{$spacing-12};
  --spacing-16: #{$spacing-16};
  --spacing-20: #{$spacing-20};
  --spacing-24: #{$spacing-24};
  --spacing-32: #{$spacing-32};
  
  // 语义化间距
  --spacing-xs: #{$spacing-xs};
  --spacing-sm: #{$spacing-sm};
  --spacing-md: #{$spacing-md};
  --spacing-lg: #{$spacing-lg};
  --spacing-xl: #{$spacing-xl};
  --spacing-xxl: #{$spacing-xxl};
  
  // 组件间距
  --component-padding-sm: #{$component-padding-sm};
  --component-padding-md: #{$component-padding-md};
  --component-padding-lg: #{$component-padding-lg};
  
  --component-margin-sm: #{$component-margin-sm};
  --component-margin-md: #{$component-margin-md};
  --component-margin-lg: #{$component-margin-lg};
  
  // 页面间距
  --page-padding-horizontal: #{$page-padding-horizontal};
  --page-padding-vertical: #{$page-padding-vertical};
  --section-margin-bottom: #{$section-margin-bottom};
  --card-padding: #{$card-padding};
  --card-margin: #{$card-margin};
}

// ==================== 间距工具类 ====================
// Margin工具类
.m-0 { margin: var(--spacing-0); }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }
.m-5 { margin: var(--spacing-5); }
.m-6 { margin: var(--spacing-6); }
.m-8 { margin: var(--spacing-8); }

// Margin Top
.mt-0 { margin-top: var(--spacing-0); }
.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-3 { margin-top: var(--spacing-3); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-5 { margin-top: var(--spacing-5); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }

// Margin Bottom
.mb-0 { margin-bottom: var(--spacing-0); }
.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-5 { margin-bottom: var(--spacing-5); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }

// Margin Left
.ml-0 { margin-left: var(--spacing-0); }
.ml-1 { margin-left: var(--spacing-1); }
.ml-2 { margin-left: var(--spacing-2); }
.ml-3 { margin-left: var(--spacing-3); }
.ml-4 { margin-left: var(--spacing-4); }
.ml-5 { margin-left: var(--spacing-5); }
.ml-6 { margin-left: var(--spacing-6); }
.ml-8 { margin-left: var(--spacing-8); }

// Margin Right
.mr-0 { margin-right: var(--spacing-0); }
.mr-1 { margin-right: var(--spacing-1); }
.mr-2 { margin-right: var(--spacing-2); }
.mr-3 { margin-right: var(--spacing-3); }
.mr-4 { margin-right: var(--spacing-4); }
.mr-5 { margin-right: var(--spacing-5); }
.mr-6 { margin-right: var(--spacing-6); }
.mr-8 { margin-right: var(--spacing-8); }

// Margin Horizontal
.mx-0 { margin-left: var(--spacing-0); margin-right: var(--spacing-0); }
.mx-1 { margin-left: var(--spacing-1); margin-right: var(--spacing-1); }
.mx-2 { margin-left: var(--spacing-2); margin-right: var(--spacing-2); }
.mx-3 { margin-left: var(--spacing-3); margin-right: var(--spacing-3); }
.mx-4 { margin-left: var(--spacing-4); margin-right: var(--spacing-4); }
.mx-auto { margin-left: auto; margin-right: auto; }

// Margin Vertical
.my-0 { margin-top: var(--spacing-0); margin-bottom: var(--spacing-0); }
.my-1 { margin-top: var(--spacing-1); margin-bottom: var(--spacing-1); }
.my-2 { margin-top: var(--spacing-2); margin-bottom: var(--spacing-2); }
.my-3 { margin-top: var(--spacing-3); margin-bottom: var(--spacing-3); }
.my-4 { margin-top: var(--spacing-4); margin-bottom: var(--spacing-4); }

// Padding工具类
.p-0 { padding: var(--spacing-0); }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-5 { padding: var(--spacing-5); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }

// Padding Top
.pt-0 { padding-top: var(--spacing-0); }
.pt-1 { padding-top: var(--spacing-1); }
.pt-2 { padding-top: var(--spacing-2); }
.pt-3 { padding-top: var(--spacing-3); }
.pt-4 { padding-top: var(--spacing-4); }
.pt-5 { padding-top: var(--spacing-5); }
.pt-6 { padding-top: var(--spacing-6); }
.pt-8 { padding-top: var(--spacing-8); }

// Padding Bottom
.pb-0 { padding-bottom: var(--spacing-0); }
.pb-1 { padding-bottom: var(--spacing-1); }
.pb-2 { padding-bottom: var(--spacing-2); }
.pb-3 { padding-bottom: var(--spacing-3); }
.pb-4 { padding-bottom: var(--spacing-4); }
.pb-5 { padding-bottom: var(--spacing-5); }
.pb-6 { padding-bottom: var(--spacing-6); }
.pb-8 { padding-bottom: var(--spacing-8); }

// Padding Horizontal
.px-0 { padding-left: var(--spacing-0); padding-right: var(--spacing-0); }
.px-1 { padding-left: var(--spacing-1); padding-right: var(--spacing-1); }
.px-2 { padding-left: var(--spacing-2); padding-right: var(--spacing-2); }
.px-3 { padding-left: var(--spacing-3); padding-right: var(--spacing-3); }
.px-4 { padding-left: var(--spacing-4); padding-right: var(--spacing-4); }

// Padding Vertical
.py-0 { padding-top: var(--spacing-0); padding-bottom: var(--spacing-0); }
.py-1 { padding-top: var(--spacing-1); padding-bottom: var(--spacing-1); }
.py-2 { padding-top: var(--spacing-2); padding-bottom: var(--spacing-2); }
.py-3 { padding-top: var(--spacing-3); padding-bottom: var(--spacing-3); }
.py-4 { padding-top: var(--spacing-4); padding-bottom: var(--spacing-4); }
