# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# uni-app
unpackage/
.hbuilderx/

# 微信小程序
project.private.config.json

# 编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp

# 环境配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 构建输出
dist/
build/

# 缓存
.cache/
.parcel-cache/

# 测试覆盖率
coverage/

# 其他
*.tgz
*.tar.gz
