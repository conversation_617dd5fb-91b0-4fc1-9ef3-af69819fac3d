// 中文语言包
export default {
	// 通用
	common: {
		confirm: '确定',
		cancel: '取消',
		save: '保存',
		themeSwitch: '主题切换',
		confirmThemeSwitch: '是否切换到新的主题模式？',
		delete: '删除',
		edit: '编辑',
		add: '添加',
		search: '搜索',
		loading: '加载中...',
		noData: '暂无数据',
		retry: '重试',
		back: '返回',
		next: '下一步',
		previous: '上一步',
		submit: '提交',
		reset: '重置',
		close: '关闭',
		open: '打开',
		select: '选择',
		selectAll: '全选',
		clear: '清空',
		refresh: '刷新',
		more: '更多',
		less: '收起',
		expand: '展开',
		collapse: '折叠',
		// 新增通用词汇
		copy: '复制',
		share: '分享',
		play: '播放',
		pause: '暂停',
		error: '错误',
		networkError: '网络连接失败，请检查网络设置',
		permissionDenied: '权限被拒绝',
		cameraPermissionRequired: '需要相机权限',
		microphonePermissionRequired: '需要麦克风权限',
		processing: '处理中...',
		listening: '正在听...',
		clearHistory: '清空历史',
		restart: '重启',
		workingHours: '周一至周五，上午 9:00-12:00',
		productSafetyNote: '。本产品经过严格质量检测，安全有效。请按照说明书使用，如有不适请及时咨询医生。'
	},

	// 导航和标签
	nav: {
		home: '首页',
		list: '列表',
		my: '我的',
		settings: '设置',
		profile: '个人资料',
		about: '关于',
		help: '帮助',
		feedback: '反馈'
	},

	// 首页
	home: {
		title: '健康助手',
		welcome: '欢迎使用健康助手',
		welcomeTo: '欢迎来到',
		smartGuide: '智能导览',
		searchPlaceholder: '请输入您的健康问题...',
		inputPlaceholder: '请输入您的问题...',
		voiceInput: '语音输入',
		textInput: '文字输入',
		send: '发送',
		sendMessage: '发送消息',
		aiConsultation: 'AI咨询',
		doctorList: '医生列表',
		healthRecords: '健康档案',
		quickConsult: '快速咨询',
		emergencyCall: '紧急呼叫',
		healthTips: '健康小贴士',
		newChat: '新建',
		holdToSpeak: '按住说话',
		releaseToSend: '松开发送',
		recording: '正在录音',
		releaseToCancel: '松开取消录音',
		slideUpToCancel: '上滑取消',
		continueSlideToCancel: '继续向上滑动取消录音',
		recordingCancelled: '录音已取消',
		recognizingVoice: '正在识别语音...',
		recognitionSuccess: '识别成功，正在发送...',
		recordingFailed: '录音失败',
		unknownError: '未知错误',
		permissionRequest: '权限申请',
		recordPermissionNeeded: '需要录音权限才能使用语音输入功能',
		switchedToDoctor: '已切换到{name}',
		addedDoctor: '已添加{name}',
		newChatStarted: '已开始新对话',
		loadedChatWith: '已加载与{name}的对话',
		editFeatureInDevelopment: '编辑功能开发中'
	},

	// 医生列表
	doctorList: {
		title: '医生',
		searchDoctor: '搜索医生',
		searchHint: '搜索医生或产品...',
		doctorTab: '医生',
		specialty: '专科',
		experience: '经验',
		rating: '评分',
		consultation: '咨询',
		appointment: '预约',
		online: '在线',
		offline: '离线',
		busy: '忙碌',
		noDoctorsAvailable: '暂无医生信息',
		noSearchResults: '未找到相关结果',
		cardiology: '心血管内科',
		neurology: '神经内科',
		gastroenterology: '消化内科',
		endocrinology: '内分泌科',
		orthopedics: '骨科',
		dermatology: '皮肤科',
		pediatrics: '儿科',
		gynecology: '妇科',
		respiratoryMedicine: '呼吸内科',
		nephrology: '肾内科',
		obstetrics: '妇产科',
		defaultDoctor: '李娜医生',
		defaultTitle: '医师',
		defaultDepartment: '消化内科'
	},

	// 医生轮播组件
	doctorSwiper: {
		introduction: '专业介绍',
		specialties: '擅长领域',
		experience: '从业年限',
		rating: '评分'
	},

	// 产品列表
	productList: {
		title: '产品',
		productTab: '产品',
		medicine: '药品',
		healthProducts: '保健品',
		medicalDevices: '医疗器械',
		price: '价格',
		addToCart: '加入购物车',
		buyNow: '立即购买',
		outOfStock: '缺货',
		inStock: '有库存',
		prescription: '处方药',
		otc: '非处方药',
		searchProduct: '搜索产品',
		noProductsAvailable: '暂无产品信息',
		productsCount: '个产品',
		manufacturer: '医生',
		originalPrice: '原价'
	},

	// List页面
	list: {
		// 搜索
		searchDoctor: '搜索医生',
		searchProduct: '搜索产品',

		// 医生相关
		doctor: '医师',
		years: '年',
		consult: '咨询',
		appointment: '预约',
		doctorInfo: '医生信息',
		doctorName: '医生姓名',
		contactPhone: '联系电话',
		workAddress: '工作地址',
		callPhone: '拨打',
		close: '关闭',
		callFailed: '拨打失败',

		// 产品分类
		medicineCategory: '药品',
		healthCategory: '保健品',

		// 弹窗
		popupClose: '✕'
	},

	// 产品详情页面
	productDetails: {
		// 基本信息
		manufacturer: '制造商：',
		doctor: '医生',
		price: '价格：',
		detailDescription: '详细说明',

		// 底部操作
		buyNow: '立即购买',

		// 购买确认弹窗
		buyConfirm: '购买确认',
		productName: '产品名称：',
		unitPrice: '单价：',
		quantity: '数量：',
		total: '合计：',
		confirmTip: '请确认您的购买信息，点击确认后将跳转到订单确认页面。',
		cancel: '取消',
		confirmBuy: '确认购买',

		// 购物车弹窗
		selectQuantity: '选择数量',
		stock: '库存：',
		confirm: '确认',
		addToCartSuccess: '已添加{count}件到购物车',

		// 产品信息
		products: {
			// 药品
			kidneyTonic: {
				name: '肾宝胶囊',
				description: '补肾益精，强身健体',
				detailDescription: '采用人参、鹿茸、枸杞等名贵中药材，具有补肾壮阳、益精填髓的功效。适用于肾虚引起的腰膝酸软、精神疲乏、性功能减退等症状。能够改善肾功能，增强体质。每日2次，每次3粒，温水送服。'
			},
			ibuprofen: {
				name: '布洛芬缓释胶囊',
				description: '用于缓解轻至中度疼痛',
				detailDescription: '布洛芬缓释胶囊是一种非甾体抗炎药，主要用于缓解轻至中度疼痛，如头痛、关节痛、偏头痛、牙痛、肌肉痛、神经痛、痛经。同时也用于普通感冒或流行性感冒引起的发热。'
			},
			licorice: {
				name: '复方甘草片',
				description: '用于镇咳祛痰',
				detailDescription: '复方甘草片为镇咳祛痰类非处方药药品。用于镇咳祛痰。适用于上呼吸道感染、支气管炎和感冒时所产生的咳嗽及咳痰不爽。'
			},
			vitaminC: {
				name: '维生素C片',
				description: '用于预防坏血病',
				detailDescription: '维生素C片用于预防坏血病，也可用于各种急慢性传染疾病及紫癜等的辅助治疗。维生素C参与氨基酸代谢、神经递质的合成、胶原蛋白和组织细胞间质的合成。'
			},
			// 保健品
			calcium: {
				name: '钙片',
				description: '补充钙质，强健骨骼',
				detailDescription: '钙是人体骨骼和牙齿的主要组成成分，许多生理功能也需要钙的参与。本品每片含钙600毫克，采用碳酸钙为钙源，吸收率高，适合各年龄段人群补钙需要。'
			},
			protein: {
				name: '蛋白粉',
				description: '优质蛋白质补充',
				detailDescription: '采用优质大豆蛋白和乳清蛋白，蛋白质含量高达80%，含有人体必需的9种氨基酸，易于消化吸收。适合运动人群、老年人、儿童等需要补充蛋白质的人群。'
			},
			fishOil: {
				name: '鱼油胶囊',
				description: '富含Omega-3脂肪酸',
				detailDescription: '深海鱼油胶囊富含EPA和DHA，有助于维护心血管健康，促进大脑发育，改善记忆力。采用挪威深海鱼类提取，纯度高，无重金属污染。'
			}
		}
	},

	// 订单确认页面
	confirmOrder: {
		// 页面标题
		title: '确认订单',

		// 商品信息
		productInfo: '商品信息',
		doctorName: '医生姓名：',
		quantity: '数量：',
		subtotal: '小计：',

		// 物流信息
		shippingInfo: '物流信息',
		addressManage: '地址管理',
		receiverName: '收货人姓名',
		receiverPhone: '收货人电话',
		region: '所在地区',
		address: '收货地址',
		receiverNamePlaceholder: '请输入收货人姓名',
		receiverPhonePlaceholder: '请输入收货人电话',
		addressPlaceholder: '请输入收货地址',
		defaultRegion: '新疆维吾尔自治区 乌鲁木齐市 天山区',

		// 订单金额
		orderAmount: '订单金额',
		productAmount: '商品金额',
		shippingFee: '运费',
		freeShipping: '免运费',
		totalAmount: '实付金额',
		total: '合计：',

		// 操作按钮
		submitOrder: '提交订单',

		// 验证提示
		nameRequired: '请输入收货人姓名',
		phoneRequired: '请输入收货人电话',
		addressRequired: '请输入收货地址',
		orderSubmitSuccess: '订单提交成功'
	},

	// 添加地址页面
	addAddress: {
		// 页面标题
		title: '添加地址',

		// 表单标签
		receiverName: '收货人姓名',
		contactPhone: '联系电话',
		region: '所在地区',
		detailAddress: '详细地址',
		zipCode: '邮政编码（可选）',
		addressTag: '地址标签（可选）',
		setDefault: '设为默认地址',

		// 占位符
		namePlaceholder: '请输入收货人姓名',
		phonePlaceholder: '请输入联系电话',
		regionPlaceholder: '请选择所在地区',
		addressPlaceholder: '请输入详细的收货地址（街道、门牌号等）',
		zipCodePlaceholder: '请输入邮政编码',
		tagPlaceholder: '如：家、公司、学校等',

		// 操作按钮
		saveAddress: '保存地址',

		// 验证提示
		nameRequired: '请输入收货人姓名',
		phoneRequired: '请输入联系电话',
		regionRequired: '请选择所在地区',
		addressRequired: '请输入详细地址',
		saveSuccess: '保存成功'
	},

	// 编辑地址页面
	editAddress: {
		// 页面标题
		title: '编辑地址',

		// 表单标签（复用addAddress的大部分内容）
		receiverName: '收货人姓名',
		contactPhone: '联系电话',
		region: '所在地区',
		detailAddress: '详细地址',
		zipCode: '邮政编码（可选）',
		addressTag: '地址标签（可选）',
		setDefault: '设为默认地址',

		// 占位符
		namePlaceholder: '请输入收货人姓名',
		phonePlaceholder: '请输入联系电话',
		regionPlaceholder: '请选择所在地区',
		addressPlaceholder: '请输入详细的收货地址（街道、门牌号等）',
		zipCodePlaceholder: '请输入邮政编码',
		tagPlaceholder: '如：家、公司、学校等',

		// 操作按钮
		saveChanges: '保存修改',

		// 验证提示
		nameRequired: '请输入收货人姓名',
		phoneRequired: '请输入联系电话',
		regionRequired: '请选择所在地区',
		addressRequired: '请输入详细地址',
		saveSuccess: '保存成功'
	},

	// 选择地址页面
	selectAddress: {
		// 页面标题
		title: '选择地址',

		// 空状态
		noAddress: '暂无收货地址',
		addAddressHint: '点击右下角添加地址',

		// 地址标签
		defaultTag: '默认',

		// 操作按钮
		edit: '编辑',
		delete: '删除',

		// 确认删除
		deleteConfirm: '确认删除该地址？',
		deleteSuccess: '删除成功'
	},

	// 医生详情页面
	doctorDetails: {
		// 页面标题
		title: '医生详情',

		// 医生信息
		doctorBadge: '医师',
		workTime: '周一至周五 9:00-17:00',
		consultDoctor: '咨询医生',

		// 详情部分
		doctorDetail: '医生详情',
		contactPhone: '联系电话',
		workAddress: '工作地址',
		specialtyField: '擅长领域',
		doctorRecommend: '医生推荐',

		// 操作按钮
		call: '拨打',
		copy: '复制',
		viewDetail: '查看详情',

		// 提示信息
		liked: '已点赞',
		unliked: '已取消点赞',
		favorited: '已收藏',
		unfavorited: '已取消收藏',
		callFailed: '拨打失败',
		addressCopied: '地址已复制',

		// 专科疾病
		diabetes: '糖尿病',
		thyroidDisease: '甲状腺疾病',
		endocrineDisorder: '内分泌失调',

		// 推荐产品
		heartProtectionCapsule: '心脏保护胶囊',
		heartProtectionDesc: '天然植物提取，有效保护心血管健康',
		antihypertensiveTablet: '降压片',
		antihypertensiveDesc: '中药复方制剂，温和降压，无副作用'
	},

	// 我的页面
	my: {
		title: '我的',
		profile: '个人资料',
		healthRecords: '健康档案',
		chatHistory: '聊天记录',
		shoppingCart: '购物车',
		orders: '我的订单',
		addresses: '收货地址',
		settings: '设置',
		help: '帮助与反馈',
		about: '关于我们',
		logout: '退出登录',
		login: '登录',
		register: '注册',
		// 功能卡片
		myLikes: '我的点赞',
		myFavorites: '我的收藏',
		// 健康数据
		weight: '体重',
		height: '身高',
		bloodType: '血型',
		// 功能列表
		shareApp: '分享应用',
		distributionManagement: '分销管理',
		// 登录相关
		loginInProgress: '登录中...',
		loginFailed: '登录失败，请重试',
		inputNickname: '输入昵称',
		pleaseInputNickname: '请输入您的昵称',
		nicknameRequired: '请输入昵称',
		completingLogin: '完成登录中...',
		loginSuccess: '登录成功'
	},

	// 聊天历史页面
	chatHistory: {
		title: '聊天历史',
		selectYearMonth: '选择年月',
		year: '年份',
		month: '月份',
		slideToSelect: '滑动选择',
		noChatData: '该日期暂无聊天记录',
		noHistory: '暂无聊天记录',
		clearHistory: '清空历史',
		refreshHistory: '刷新历史记录',
		confirmClear: '确认清空',
		clearWarning: '确定要清空所有聊天历史吗？此操作不可恢复。',
		historyCleared: '历史记录已清空',
		deleteHistory: '删除记录',
		exportHistory: '导出记录',
		searchHistory: '搜索记录',
		today: '今天',
		yesterday: '昨天',
		thisWeek: '本周',
		thisMonth: '本月',
		day: '日',
		selectYear: '选择年份',
		selectMonth: '选择月份',
		selectDate: '选择日期',
		// 星期
		weekdays: {
			sunday: '日',
			monday: '一',
			tuesday: '二',
			wednesday: '三',
			thursday: '四',
			friday: '五',
			saturday: '六'
		}
	},

	// 我的订单页面
	myOrder: {
		title: '我的订单',
		tabs: {
			all: '全部',
			pendingPayment: '待支付',
			pendingShipment: '待发货',
			shipped: '已发货',
			completed: '已完成'
		},
		empty: {
			noOrders: '暂无订单',
			noPendingPayment: '暂无待支付订单',
			noPendingShipment: '暂无待发货订单',
			noShipped: '暂无已发货订单',
			noCompleted: '暂无已完成订单'
		}
	},

	// 健康档案页面
	healthRecords: {
		title: '健康档案',
		noHealthData: '暂无健康档案',
		createHealthRecord: '创建健康档案',
		basicInfo: '基本信息',
		allergyHistory: '过敏史',
		chronicDiseaseHistory: '慢性病史',
		currentMedication: '当前用药',
		lifestyle: '生活方式',
		// 健康状态显示
		hasAllergyDesc: '是否有药物、食物或其它物质过敏',
		hasChronicDiseaseDesc: '是否有慢性病',
		hasMedicationDesc: '是否用药',
		exerciseFrequencyLabel: '运动频率',
		smokingStatusLabel: '吸烟情况',
		drinkingStatusLabel: '饮酒情况',
		sleepDurationLabel: '平均每晚睡眠时长',
		sleepQualityLabel: '睡眠质量',
		stressLevelLabel: '近期压力水平'
	},

	// 购物车页面
	shoppingCart: {
		title: '购物车',
		empty: '购物车空空如也',
		emptySubtitle: '快去挑选心仪的商品吧',
		goShopping: '去购物',
		selectAll: '全选',
		totalItems: '共{count}件商品',
		subtotal: '小计',
		selectedItems: '已选择 {count} 件商品',
		total: '合计',
		checkout: '结算 ({count})',
		deleteAll: '删除全部',
		deleteConfirm: '确认删除',
		deleteItemConfirm: '确定要删除商品"{name}"吗？',
		deleteAllConfirm: '确定要删除全部选中的{count}件商品吗？',
		deleteSuccess: '删除成功',
		deleteAllSuccess: '成功删除{count}件商品',
		selectItemsToDelete: '请选择要删除的商品',
		selectItemsToCheckout: '请选择要结算的商品',
		checkoutItems: '结算{count}件商品',
		delete: '删除'
	},

	// 用户相关
	user: {
		profile: '个人资料',
		nickname: '昵称',
		avatar: '头像',
		phone: '手机号',
		email: '邮箱',
		gender: '性别',
		birthday: '生日',
		address: '地址',
		male: '男',
		female: '女',
		unknown: '未知',
		clickToLogin: '点击登录',
		loginForMore: '登录后享受更多功能',
		// 编辑资料页面
		changeAvatar: '更换头像',
		clickToChangePhoto: '点击头像更换照片',
		basicInfo: '基本信息',
		height: '身高',
		weight: '体重',
		bloodType: '血型',
		location: '常住地址',
		locating: '定位中',
		locate: '定位',
		healthInfo: '健康信息',
		allergyHistory: '过敏史',
		allergyDesc: '是否有药物、食物或其它物质过敏',
		yes: '是',
		no: '否',
		commonAllergens: '常见过敏源',
		// 过敏源
		penicillin: '青霉素类药物',
		cephalosporin: '头孢类药物',
		aspirin: '阿司匹林',
		peanuts: '花生',
		seafood: '海鲜',
		milk: '牛奶',
		eggs: '鸡蛋',
		pollenMites: '花粉/尘螨',
		// 输入提示
		enterHeight: '请输入身高 (cm)',
		enterWeight: '请输入体重 (kg)',
		// 默认地址
		defaultLocation: '新疆维吾尔自治区 乌鲁木齐市 天山区',
		// 性别选项
		notSet: '未设置',
		// 血型选项
		unknownBloodType: '不清楚',
		typeA: 'A型',
		typeB: 'B型',
		typeAB: 'AB型',
		typeO: 'O型',
		// 编辑资料页面扩展
		otherAllergies: '其他过敏物质',
		otherAllergiesPlaceholder: '请补充其他过敏源',
		currentMedication: '当前用药',
		currentMedicationDesc: '目前是否在服用任何药物',
		medicationList: '药物清单',
		medicationListPlaceholder: '请列出正在服用的药物名称、剂量和频率',
		chronicDiseaseHistory: '慢性病史',
		chronicDiseaseDesc: '是否患有慢性疾病',
		specificSymptoms: '具体病症',
		hypertension: '高血压',
		diabetes: '糖尿病',
		hypertensionRange: '平常血压范围',
		hypertensionPlaceholder: '例如 130/85 mmHg',
		diabetesRange: '平常空腹血糖范围',
		diabetesPlaceholder: '例如 5.8 mmol/L',
		otherChronicDiseases: '其他慢性疾病',
		otherChronicDiseasesPlaceholder: '请补充其他慢性疾病',
		// 医疗历史
		medicalHistory: '医疗历史',
		surgeryHistory: '手术与住院史',
		surgeryHistoryDesc: '过去是否有过手术或住院经历',
		surgeryDetails: '详情说明',
		surgeryDetailsPlaceholder: '请描述手术或住院的详细情况',
		familyHistory: '家族病史',
		familyHistoryDesc: '直系亲属（父母、兄弟姐妹、子女）是否有以下疾病',
		heartDisease: '心脏病',
		stroke: '中风',
		cancer: '癌症',
		mentalHealth: '精神健康疾病',
		otherFamilyHistory: '其他家族病史补充',
		otherFamilyHistoryPlaceholder: '请补充其他家族病史',
		// 生活方式
		lifestyle: '生活方式',
		exerciseFrequency: '运动频率',
		dietPreferences: '日常饮食偏好',
		spicy: '爱吃辣的',
		sweet: '爱吃甜',
		salty: '爱吃咸',
		light: '爱吃清淡食物',
		oily: '爱吃油腻食物',
		vegetarian: '爱吃蔬菜',
		smokingStatus: '吸烟情况',
		drinkingStatus: '饮酒情况',
		sleepDuration: '平均每晚睡眠时长',
		sleepQuality: '睡眠质量',
		stressLevel: '近期压力水平',
		// 女性健康
		womenHealth: '女性健康',
		menopause: '是否已绝经',
		menstrualCycle: '月经周期是否规律',
		pregnancy: '是否曾怀孕',
		birthCount: '生育次数',
		// 选择日期
		selectDate: '选择日期',
		save: '保存',
		// 生活方式选项
		exerciseOptions: {
			sedentary: '久坐(基本不运动)',
			light: '轻度运动',
			moderate: '中度运动',
			intense: '高强度运动'
		},
		smokingOptions: {
			never: '从不吸烟',
			occasionally: '偶尔吸烟',
			frequently: '经常吸烟',
			quit: '已戒烟'
		},
		drinkingOptions: {
			never: '从不饮酒',
			occasionally: '偶尔饮酒',
			frequently: '经常饮酒',
			quit: '已戒酒'
		},
		sleepOptions: {
			normal: '7-8小时',
			short: '6-7小时',
			long: '8-9小时',
			veryShort: '少于6小时',
			veryLong: '超过9小时'
		},
		sleepQualityOptions: {
			veryGood: '很好(很少入睡困难)',
			good: '一般',
			poor: '较差',
			veryPoor: '很差'
		},
		stressOptions: {
			veryLow: '很小',
			low: '一般',
			high: '较大',
			veryHigh: '很大'
		},
		// 女性健康选项
		menstrualCycleOptions: {
			regular: '规律',
			irregular: '不规律',
			sometimes: '偶尔不规律'
		},
		birthCountOptions: {
			zero: '0次',
			one: '1次',
			two: '2次',
			three: '3次',
			fourPlus: '4次及以上'
		},
		// 提示信息
		messages: {
			regionSelectSuccess: '地区选择成功',
			locationPermissionTitle: '定位权限申请',
			locationPermissionContent: '需要获取您的位置信息来提供精准的地址定位服务，请在设置中开启定位权限',
			locationPermissionRequest: '位置权限申请',
			locationPermissionDesc: '我们需要获取您的位置信息来自动填写地址，这将帮助您快速完成个人资料设置。您的位置信息仅用于地址转换，不会被存储或上传。',
			locationPermissionDenied: '定位权限被拒绝',
			manualAddressInput: '您可以手动输入地址',
			locating: '定位中...',
			locationFailed: '定位失败',
			locationSuccess: '定位成功',
			saving: '保存中...',
			saveSuccess: '保存成功',
			saveFailed: '保存失败',
			goToSettings: '去设置',
			agree: '同意',
			refuse: '拒绝'
		}
	},

	// 设置页面
	settings: {
		title: '设置',
		language: '语言设置',
		fontSize: '字体大小',
		notifications: '通知设置',
		privacy: '隐私设置',
		security: '安全设置',
		theme: '主题设置',
		cache: '缓存管理',
		version: '版本信息',
		checkUpdate: '检查更新',
		clearCache: '清除缓存',
		// 显示设置
		display: '显示设置',
		darkMode: '暗色模式',
		darkModeDesc: '在浅色和暗色主题之间切换',
		followSystem: '跟随系统主题',
		followSystemDesc: '自动适应系统的浅色/暗色设置',
		// 用户信息相关
		notSetNickname: '未设置昵称',
		notBoundPhone: '未绑定手',
		editProfile: '编辑资料',
		normalUser: '普通用户',
		expiryTime: '到期时间',
		notSet: '--',
		// 设置项描述
		fontSizeDesc: '调整应用内字体大小',
		languageDesc: '选择应用显示语言',
		helpFeedbackDesc: '帮助与反馈',
		aboutUsDesc: '关于我们',
		selectLanguage: '选择语言',
		selectSourceLanguage: '选择源语言',
		selectTargetLanguage: '选择目标语言',
		myLanguage: '我的语言',
		theirLanguage: '他们的语言',
		sourceLanguage: '源语言',
		targetLanguage: '目标语言',
		chinese: '中文',
		english: 'English',
		uyghur: 'ئۇيغۇرچە',
		// 其他设置
		other: '其他',
		logout: '退出登录',
		logoutDesc: '清除登录状态并返回登录页面',
		shareApp: '分享应用',
		distributionManagement: '分发管理',
		aboutDialogTitle: '关于应用',
		aboutDialogContent: '健康助手 v1.0.0\n\n专业的AI健康指导应用',
		// 弹出框相关
		adjustFontSize: '调整字体大小',
		fontPreview: '字体预览 Font Preview',
		previewText: '这是预览文字的效果',
		currentSize: '当前大小',
		aboutUs: '关于我们',
		appName: '健康助手 v1.0.0',
		appDesc: '一款专业的AI健康导游应用',
		fontSizes: {
			small: '小',
			medium: '中',
			large: '大',
			extraLarge: '特大'
		},
	},

	// 帮助与反馈页面
	helpFeedback: {
		title: '帮助与反馈',
		feedbackNotice: '反馈说明',
		noticeContent: '我们会收集您的问题描述和相关应用数据（不包含敏感信息）以便更好地解决您的问题。提交后我们将通过您提供的手机号与您联系。',
		yourName: '您的称呼（可选）',
		namePlaceholder: '请输入您的称呼',
		yourPhone: '您的手机号（用于问题回访）',
		phonePlaceholder: '请输入11位手机号',
		problemDescription: '请详细描述您的问题或建议',
		descriptionPlaceholder: '请详细描述您遇到的问题或建议',
		includeLabel: '包括：',
		includeItems: {
			steps: '• 具体的操作步骤',
			expected: '• 期望的结果',
			actual: '• 实际发生的情况',
			other: '• 其他相关信息'
		},
		submitFeedback: '提交反馈',
		bottomNotice: '提示：您的隐私对我们很重要，我们不会收集您的敏感信息，仅收集必要的应用数据以帮助解决问题。',
		// 验证和提示信息
		validation: {
			phoneError: '请输入正确的手机号',
			contentRequired: '请描述您的问题或建议'
		},
		messages: {
			submitting: '提交中...',
			submitSuccess: '提交成功',
			submitFailed: '提交失败'
		}
	},



	// 购物车
	shoppingCart: {
		title: '购物车',
		empty: '购物车为空',
		selectAll: '全选',
		total: '合计',
		checkout: '结算',
		quantity: '数量',
		remove: '移除',
		addMore: '继续购物',
		selectedItems: '已选择商品',
		deleteSelected: '删除选中',
		confirmDelete: '确认删除',
		deleteAllConfirm: '确定要删除全部选中的商品吗？'
	},

	// 地址管理
	address: {
		title: '收货地址',
		addAddress: '添加地址',
		editAddress: '编辑地址',
		selectAddress: '选择地址',
		defaultAddress: '默认地址',
		setDefault: '设为默认',
		name: '收货人',
		phone: '手机号',
		region: '所在地区',
		detailAddress: '详细地址',
		postalCode: '邮政编码',
		saveAddress: '保存地址',
		deleteAddress: '删除地址',
		confirmDeleteAddress: '确定要删除这个地址吗？'
	},

	// 相机功能
	camera: {
		title: '拍照',
		takePhoto: '拍照',
		selectFromAlbum: '从相册选择',
		retake: '重新拍照',
		confirm: '确认',
		flashOn: '开启闪光灯',
		flashOff: '关闭闪光灯',
		switchCamera: '切换摄像头',
		help: '帮助',
		cameraHelp: '相机使用帮助',
		helpContent: {
			takePhoto: '点击拍照按钮进行拍照',
			selectAlbum: '点击相册按钮选择已有照片',
			switchCamera: '点击切换按钮切换前后摄像头',
			flash: '点击闪光灯按钮控制闪光灯'
		}
	},

	// 错误信息
	error: {
		networkError: '网络连接失败',
		serverError: '服务器错误',
		unknownError: '未知错误',
		permissionDenied: '权限被拒绝',
		cameraPermissionDenied: '相机权限被拒绝',
		locationPermissionDenied: '定位权限被拒绝',
		storagePermissionDenied: '存储权限被拒绝',
		operationFailed: '操作失败',
		dataLoadFailed: '数据加载失败',
		uploadFailed: '上传失败',
		downloadFailed: '下载失败'
	},

	// 成功信息
	success: {
		operationSuccess: '操作成功',
		saveSuccess: '保存成功',
		deleteSuccess: '删除成功',
		uploadSuccess: '上传成功',
		downloadSuccess: '下载成功',
		updateSuccess: '更新成功',
		loginSuccess: '登录成功',
		logoutSuccess: '退出成功',
		registerSuccess: '注册成功'
	},

	// 表单验证
	validation: {
		required: '此项为必填项',
		invalidEmail: '邮箱格式不正确',
		invalidPhone: '手机号格式不正确',
		passwordTooShort: '密码长度不能少于6位',
		passwordMismatch: '两次输入的密码不一致',
		invalidFormat: '格式不正确',
		tooLong: '内容过长',
		tooShort: '内容过短',
	},

	// 相机功能
	camera: {
		title: '拍照发送',
		takePhoto: '拍照',
		retake: '重新拍照',
		send: '发',
		selectArea: '选择发送区'
	},

	// 登录相关
	login: {
		changeAvatar: '更换头像',
		selectAvatar: '选择头像',
		nicknamePlaceholder: '请输入用户昵称',
		avatarTip: '点击上方按钮选择微信头像',
		experienceTip: '为了更好的体验，请点击头像图标获取头像',
		devTip: '（开发环境：如遇异常请重新选择或重启工具）',
		loginNow: '立即登录'
	},

	// 字体测试
	fontTest: {
		title: '字体测试页面',
		currentLanguage: '当前语言',
		uyghur: '维吾尔文',
		chinese: '中文',
		defaultFont: '默认字体',
		uyghurFont: '维吾尔文字体'
	},

	// AI咨询回复
	ai: {
		analyzing: 'AI正在分析用户问题:',
		headache: {
			response: '🔍 搜索结果：头痛症状分析\n\n根据您的症状，头痛可能由以下原因引起：\n\n1️⃣ **紧张性头痛**：最常见，通常由压力、疲劳引起\n2️⃣ **偏头痛**：一侧头部剧烈疼痛，可能伴有恶心\n3️⃣ **颈椎问题**：长期低头工作导致\n4️⃣ **睡眠不足**：休息不够引起\n\n💡 **建议处理方式**：\n• 充足休息，保持规律作息\n• 适当按摩太阳穴\n• 避免长时间用眼\n• 如持续或加重，请及时就医'
		},
		fever: {
			response: '🔍 搜索结果：发热症状处理\n\n发热是身体抵抗感染的自然反应：\n\n🌡️ **体温分级**：\n• 37.3-38°C：低热\n• 38.1-39°C：中等热\n• 39.1-41°C：高热\n\n💡 **处理建议**：\n• 多喝温开水，补充水分\n• 物理降温：温水擦拭身体\n• 充分休息，避免剧烈活动\n• 体温超过38.5°C或持续不退，请及时就医\n• 观察是否伴有其他症状'
		},
		cough: {
			response: '🔍 搜索结果：咳嗽症状分析\n\n咳嗽类型及可能原因：\n\n🔸 **干咳**：可能是感冒初期、过敏\n🔸 **有痰咳嗽**：可能是呼吸道感染\n🔸 **夜间咳嗽**：可能是哮喘、胃食管反流\n\n💡 **缓解方法**：\n• 多喝温水，保持喉咙湿润\n• 蜂蜜水有助缓解咳嗽\n• 保持室内空气湿润\n• 避免烟雾、粉尘等刺激\n• 咳嗽超过一周或伴有血痰，请就医'
		},
		stomachache: {
			response: '🔍 搜索结果：腹痛症状分析\n\n腹痛可能的原因：\n\n🔸 **胃炎/胃溃疡**：上腹部疼痛\n🔸 **消化不良**：饭后腹胀、疼痛\n🔸 **肠胃炎**：腹痛伴腹泻\n🔸 **饮食不当**：暴饮暴食、生冷食物\n\n💡 **缓解建议**：\n• 暂时禁食，让胃部休息\n• 喝温开水或淡盐水\n• 避免辛辣、油腻食物\n• 少食多餐，细嚼慢咽\n• 疼痛剧烈或持续，请及时就医'
		},
		insomnia: {
			response: '🔍 搜索结果：睡眠问题解决方案\n\n失眠常见原因：\n\n🔸 **心理因素**：压力、焦虑、抑郁\n🔸 **生活习惯**：晚睡、饮食不当\n🔸 **环境因素**：噪音、光线、温度\n🔸 **身体疾病**：疼痛、呼吸问题\n\n💡 **改善方法**：\n• 建立规律的睡眠时间\n• 睡前1小时避免电子设备\n• 保持卧室安静、黑暗、凉爽\n• 避免睡前饮用咖啡、茶\n• 尝试放松技巧：深呼吸、冥想\n• 长期失眠请咨询医生'
		},
		cold: {
			response: '🔍 搜索结果：感冒症状处理\n\n感冒常见症状及处理：\n\n🔸 **鼻塞流涕**：病毒感染引起\n🔸 **喉咙痛**：炎症反应\n🔸 **轻微发热**：身体抵抗病毒\n🔸 **全身乏力**：免疫系统工作\n\n💡 **康复建议**：\n• 充分休息，增强免疫力\n• 多喝温水，保持水分\n• 盐水漱口缓解喉咙痛\n• 蒸汽吸入缓解鼻塞\n• 避免去人群密集场所\n• 症状严重或持续超过一周请就医'
		},
		general: {
			response1: '🔍 搜索结果：健康咨询建议\n\n感谢您的咨询，根据您的描述：\n\n💡 **一般建议**：\n• 保持良好的生活习惯\n• 规律作息，充足睡眠\n• 均衡饮食，适量运动\n• 定期体检，预防为主\n• 如有不适症状持续，请及时就医\n\n如需更详细的建议，请描述具体症状。',
			response2: '🔍 搜索结果：健康管理指导\n\n根据您的健康咨询：\n\n💡 **健康维护**：\n• 保持积极乐观的心态\n• 适当进行体育锻炼\n• 注意饮食营养搭配\n• 避免过度疲劳和压力\n• 有症状变化及时关注\n\n建议您详细描述症状以获得更精准的建议。',
			response3: '🔍 搜索结果：专业健康建议\n\n感谢您信任我们的健康咨询：\n\n💡 **预防保健**：\n• 建立健康的生活方式\n• 定期进行健康检查\n• 关注身体信号变化\n• 及时处理健康问题\n• 保持良好的心理状态\n\n如有具体症状，请提供更多详细信息。'
		}
	}
}