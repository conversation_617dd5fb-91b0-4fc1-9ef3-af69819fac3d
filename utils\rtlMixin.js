/**
 * RTL (Right-to-Left) 布局混入工具
 * 为维吾尔文等RTL语言提供布局支持
 */

import { computed } from 'vue'
import { useAppStore } from '@/store/app.js'

/**
 * RTL布局混入
 * @returns {Object} RTL相关的计算属性和方法
 */
export function useRTL() {
  const appStore = useAppStore()

  // 是否为RTL语言
  const isRTL = computed(() => appStore.isUyghur)

  // 文本方向
  const textDirection = computed(() => isRTL.value ? 'rtl' : 'ltr')

  // 文本对齐
  const textAlign = computed(() => isRTL.value ? 'right' : 'left')

  // 反向文本对齐
  const reverseTextAlign = computed(() => isRTL.value ? 'left' : 'right')

  // Flex方向
  const flexDirection = computed(() => isRTL.value ? 'row-reverse' : 'row')

  // 反向Flex方向
  const reverseFlexDirection = computed(() => isRTL.value ? 'row' : 'row-reverse')

  // RTL类名
  const rtlClass = computed(() => ({
    'rtl-container': isRTL.value,
    'ltr-container': !isRTL.value,
    'ug': appStore.isUyghur,
    [`lang-${appStore.lang}`]: true
  }))

  // 获取边距样式（左边距）
  const getMarginLeft = (value = '16rpx') => {
    return isRTL.value ? { marginRight: value, marginLeft: 0 } : { marginLeft: value }
  }

  // 获取边距样式（右边距）
  const getMarginRight = (value = '16rpx') => {
    return isRTL.value ? { marginLeft: value, marginRight: 0 } : { marginRight: value }
  }

  // 获取内边距样式（左内边距）
  const getPaddingLeft = (value = '16rpx') => {
    return isRTL.value ? { paddingRight: value, paddingLeft: 0 } : { paddingLeft: value }
  }

  // 获取内边距样式（右内边距）
  const getPaddingRight = (value = '16rpx') => {
    return isRTL.value ? { paddingLeft: value, paddingRight: 0 } : { paddingRight: value }
  }

  // 获取定位样式（左定位）
  const getPositionLeft = (value = '0') => {
    return isRTL.value ? { right: value, left: 'auto' } : { left: value }
  }

  // 获取定位样式（右定位）
  const getPositionRight = (value = '0') => {
    return isRTL.value ? { left: value, right: 'auto' } : { right: value }
  }

  // 获取边框样式（左边框）
  const getBorderLeft = (style = '1rpx solid #eee') => {
    return isRTL.value ? { borderRight: style, borderLeft: 'none' } : { borderLeft: style }
  }

  // 获取边框样式（右边框）
  const getBorderRight = (style = '1rpx solid #eee') => {
    return isRTL.value ? { borderLeft: style, borderRight: 'none' } : { borderRight: style }
  }

  // 获取圆角样式（左圆角）
  const getBorderRadiusLeft = (value = '8rpx') => {
    return isRTL.value ? {
      borderTopRightRadius: value,
      borderBottomRightRadius: value,
      borderTopLeftRadius: 0,
      borderBottomLeftRadius: 0
    } : {
      borderTopLeftRadius: value,
      borderBottomLeftRadius: value
    }
  }

  // 获取圆角样式（右圆角）
  const getBorderRadiusRight = (value = '8rpx') => {
    return isRTL.value ? {
      borderTopLeftRadius: value,
      borderBottomLeftRadius: value,
      borderTopRightRadius: 0,
      borderBottomRightRadius: 0
    } : {
      borderTopRightRadius: value,
      borderBottomRightRadius: value
    }
  }

  // 获取浮动样式（左浮动）
  const getFloatLeft = () => {
    return isRTL.value ? { float: 'right' } : { float: 'left' }
  }

  // 获取浮动样式（右浮动）
  const getFloatRight = () => {
    return isRTL.value ? { float: 'left' } : { float: 'right' }
  }

  // 获取变换样式（水平翻转）
  const getTransformFlipX = () => {
    return isRTL.value ? { transform: 'scaleX(-1)' } : {}
  }

  // 获取Flex容器样式
  const getFlexContainer = (justify = 'space-between', align = 'center') => {
    return {
      display: 'flex',
      flexDirection: flexDirection.value,
      justifyContent: justify,
      alignItems: align
    }
  }

  // 获取列表项样式
  const getListItemStyle = () => {
    return {
      display: 'flex',
      flexDirection: flexDirection.value,
      alignItems: 'center'
    }
  }

  // 获取表单项样式
  const getFormItemStyle = () => {
    return {
      display: 'flex',
      flexDirection: flexDirection.value,
      alignItems: 'center'
    }
  }

  // 获取导航栏样式
  const getNavbarStyle = () => {
    return {
      display: 'flex',
      flexDirection: flexDirection.value,
      alignItems: 'center',
      justifyContent: 'space-between'
    }
  }

  // 获取卡片头部样式
  const getCardHeaderStyle = () => {
    return {
      display: 'flex',
      flexDirection: flexDirection.value,
      alignItems: 'center',
      justifyContent: 'space-between'
    }
  }

  // 获取按钮组样式
  const getButtonGroupStyle = () => {
    return {
      display: 'flex',
      flexDirection: flexDirection.value
    }
  }

  // 获取标签页样式
  const getTabsStyle = () => {
    return {
      display: 'flex',
      flexDirection: flexDirection.value
    }
  }

  // 获取步骤条样式
  const getStepsStyle = () => {
    return {
      display: 'flex',
      flexDirection: flexDirection.value
    }
  }

  // 获取输入框样式
  const getInputStyle = () => {
    return {
      textAlign: textAlign.value,
      direction: textDirection.value
    }
  }

  // 获取文本区域样式
  const getTextareaStyle = () => {
    return {
      textAlign: textAlign.value,
      direction: textDirection.value
    }
  }

  // 获取选择器样式
  const getSelectStyle = () => {
    return {
      textAlign: textAlign.value,
      direction: textDirection.value
    }
  }

  // 获取弹窗内容样式
  const getModalContentStyle = () => {
    return {
      textAlign: textAlign.value,
      direction: textDirection.value
    }
  }

  // 获取弹窗底部样式
  const getModalFooterStyle = () => {
    return {
      display: 'flex',
      flexDirection: flexDirection.value,
      justifyContent: 'space-between'
    }
  }

  // 获取消息提示样式
  const getToastStyle = () => {
    return {
      textAlign: textAlign.value,
      direction: textDirection.value
    }
  }

  // 获取进度条样式
  const getProgressStyle = () => {
    return {
      direction: textDirection.value
    }
  }

  // 获取日历样式
  const getCalendarStyle = () => {
    return {
      direction: textDirection.value
    }
  }

  // 获取轮播图样式
  const getSwiperStyle = () => {
    return {
      direction: textDirection.value
    }
  }

  return {
    // 计算属性
    isRTL,
    textDirection,
    textAlign,
    reverseTextAlign,
    flexDirection,
    reverseFlexDirection,
    rtlClass,

    // 样式方法
    getMarginLeft,
    getMarginRight,
    getPaddingLeft,
    getPaddingRight,
    getPositionLeft,
    getPositionRight,
    getBorderLeft,
    getBorderRight,
    getBorderRadiusLeft,
    getBorderRadiusRight,
    getFloatLeft,
    getFloatRight,
    getTransformFlipX,

    // 组件样式方法
    getFlexContainer,
    getListItemStyle,
    getFormItemStyle,
    getNavbarStyle,
    getCardHeaderStyle,
    getButtonGroupStyle,
    getTabsStyle,
    getStepsStyle,
    getInputStyle,
    getTextareaStyle,
    getSelectStyle,
    getModalContentStyle,
    getModalFooterStyle,
    getToastStyle,
    getProgressStyle,
    getCalendarStyle,
    getSwiperStyle
  }
}

/**
 * RTL页面混入
 * 为页面组件提供RTL支持
 */
export function useRTLPage() {
  const rtl = useRTL()
  const appStore = useAppStore()

  // 页面容器类
  const pageClass = computed(() => ({
    ...rtl.rtlClass.value,
    'rtl-page': rtl.isRTL.value
  }))

  // 页面样式
  const pageStyle = computed(() => ({
    direction: rtl.textDirection.value,
    textAlign: rtl.textAlign.value
  }))

  return {
    ...rtl,
    pageClass,
    pageStyle
  }
}

export default useRTL
