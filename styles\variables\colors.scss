// 统一的颜色变量系统
// 合并了 App.vue、uni.scss、fui-theme.css 中的颜色定义

// ==================== 主色系 ====================
$primary-color: #465CFF;
$primary-light: #6B7FFF;
$primary-dark: #2D4BFF;
$primary-disable: mix(#fff, $primary-color, 50%);

// ==================== 功能色系 ====================
$success-color: #09BE4F;
$success-light: mix(#fff, $success-color, 80%);
$success-disable: mix(#fff, $success-color, 50%);

$warning-color: #FFB703;
$warning-light: mix(#fff, $warning-color, 80%);
$warning-disable: mix(#fff, $warning-color, 50%);

$danger-color: #FF2B2B;
$danger-light: mix(#fff, $danger-color, 80%);
$danger-disable: mix(#fff, $danger-color, 50%);

$info-color: #8f939c;
$info-light: mix(#fff, $info-color, 80%);
$info-disable: mix(#fff, $info-color, 50%);

$purple-color: #6831FF;

// ==================== 文字颜色 ====================
$text-color-primary: #181818;    // 主要文字（标题）
$text-color-regular: #333333;    // 常规文字（段落）
$text-color-secondary: #7F7F7F;  // 次要文字
$text-color-placeholder: #B2B2B2; // 占位符文字
$text-color-disabled: #CCCCCC;   // 禁用文字
$text-color-inverse: #FFFFFF;    // 反色文字

// ==================== 背景颜色 ====================
$bg-color-white: #FFFFFF;
$bg-color-page: #F8F8F8;         // 页面背景
$bg-color-content: #F1F4FA;      // 内容区背景
$bg-color-grey: #F8F8F8;         // 灰色背景
$bg-color-hover: rgba(0, 0, 0, 0.1); // 悬停背景
$bg-color-mask: rgba(0, 0, 0, 0.6);  // 遮罩背景

// 功能背景色
$bg-color-success: rgba(9, 190, 79, 0.05);
$bg-color-warning: rgba(255, 183, 3, 0.1);
$bg-color-danger: rgba(255, 43, 43, 0.05);
$bg-color-purple: rgba(104, 49, 255, 0.05);

// ==================== 边框颜色 ====================
$border-color-base: #E4E7ED;     // 基础边框
$border-color-light: #EBEEF5;    // 浅色边框
$border-color-lighter: #F2F6FC;  // 更浅边框
$border-color-dark: #D3D4D6;     // 深色边框

// ==================== 阴影颜色 ====================
$shadow-light: rgba(0, 0, 0, 0.06);
$shadow-base: rgba(0, 0, 0, 0.12);
$shadow-dark: rgba(0, 0, 0, 0.18);

// ==================== 暗色主题 ====================
$dark-bg-color: #1a1a1a;
$dark-bg-color-secondary: #2d2d2d;
$dark-text-color: #ffffff;
$dark-text-color-secondary: #cccccc;
$dark-border-color: #404040;

// ==================== CSS变量映射 ====================
:root {
  // 主色系
  --primary-color: #{$primary-color};
  --primary-light: #{$primary-light};
  --primary-dark: #{$primary-dark};
  
  // 功能色系
  --success-color: #{$success-color};
  --warning-color: #{$warning-color};
  --danger-color: #{$danger-color};
  --info-color: #{$info-color};
  --purple-color: #{$purple-color};
  
  // 文字颜色
  --text-color-primary: #{$text-color-primary};
  --text-color-regular: #{$text-color-regular};
  --text-color-secondary: #{$text-color-secondary};
  --text-color-placeholder: #{$text-color-placeholder};
  --text-color-disabled: #{$text-color-disabled};
  --text-color-inverse: #{$text-color-inverse};
  
  // 背景颜色
  --bg-color-white: #{$bg-color-white};
  --bg-color-page: #{$bg-color-page};
  --bg-color-content: #{$bg-color-content};
  --bg-color-grey: #{$bg-color-grey};
  --bg-color-hover: #{$bg-color-hover};
  --bg-color-mask: #{$bg-color-mask};
  
  // 边框颜色
  --border-color-base: #{$border-color-base};
  --border-color-light: #{$border-color-light};
  --border-color-lighter: #{$border-color-lighter};
  --border-color-dark: #{$border-color-dark};
  
  // 阴影
  --shadow-light: #{$shadow-light};
  --shadow-base: #{$shadow-base};
  --shadow-dark: #{$shadow-dark};
}

// ==================== 暗色主题变量 ====================
.dark-theme {
  --bg-color-page: #{$dark-bg-color};
  --bg-color-content: #{$dark-bg-color-secondary};
  --text-color-primary: #{$dark-text-color};
  --text-color-regular: #{$dark-text-color-secondary};
  --border-color-base: #{$dark-border-color};
}

// ==================== 兼容性映射 ====================
// 为了兼容现有代码，保留一些旧的变量名
$uni-color-primary: $primary-color;
$uni-color-success: $success-color;
$uni-color-warning: $warning-color;
$uni-color-error: $danger-color;

$uni-text-color: $text-color-regular;
$uni-text-color-inverse: $text-color-inverse;
$uni-text-color-grey: $text-color-secondary;
$uni-text-color-placeholder: $text-color-placeholder;
$uni-text-color-disable: $text-color-disabled;

$uni-bg-color: $bg-color-white;
$uni-bg-color-grey: $bg-color-grey;
$uni-bg-color-hover: $bg-color-hover;
$uni-bg-color-mask: $bg-color-mask;

$uni-border-color: $border-color-base;

// FirstUI兼容性变量
$fui-color-primary: $primary-color;
$fui-color-success: $success-color;
$fui-color-warning: $warning-color;
$fui-color-danger: $danger-color;
$fui-color-purple: $purple-color;

$fui-color-title: $text-color-primary;
$fui-color-section: $text-color-regular;
$fui-color-subtitle: $text-color-secondary;
$fui-color-label: $text-color-placeholder;
$fui-color-minor: $text-color-disabled;
$fui-color-white: $text-color-inverse;
$fui-color-link: $primary-color;
