import { defineStore } from 'pinia';
import { getCurrentLanguage, setCurrentLanguage, detectSystemLanguage } from '@/locale/index.js'

export const useAppStore = defineStore('app', {
  state: () => ({
	  statusBarHeight: 0,
	  lang: getCurrentLanguage(),
	  // 字体大小设置
	  fontSize: uni.getStorageSync("fontSize") || 'medium',
	  // 当前是否为维吾尔文
	  isUyghur: getCurrentLanguage() === 'ug',
	  user_info:{}
  }),
  actions: {
	  // 设置字体大小
	  setFontSize(size) {
		  this.fontSize = size;
		  uni.setStorageSync("fontSize", size);
		  this.applyFontSize();
	  },

	  // 应用字体大小                   
	  applyFontSize() {
		  const size = this.fontSize;

		  // 通知所有页面字体大小已更改
		  uni.$emit('fontSizeChanged', { fontSize: size });

		  console.log('字体大小已应用:', size);
	  },

	  // 初始化字体大小
	  initFontSize() {
		  this.applyFontSize();
	  },

	  // 设置语言
	  setLanguage(language) {
		  this.lang = language;
		  this.isUyghur = language === 'ug';
		  setCurrentLanguage(language);

		  // 应用语言相关的字体设置
		  this.applyLanguageFont();

		  // 触发全局语言切换事件
		  uni.$emit('languageChanged', { language, isUyghur: this.isUyghur });

		  console.log('语言已设置:', language, '是否为维吾尔文:', this.isUyghur);
	  },

	  // 应用语言字体
	  applyLanguageFont() {
		  // 获取页面根元素
		  const pages = getCurrentPages();
		  if (pages.length > 0) {
			  const currentPage = pages[pages.length - 1];

			  // 通过页面实例设置字体类
			  if (currentPage && currentPage.$vm) {
				  const vm = currentPage.$vm;
				  // 设置页面根元素的class
				  this.setPageFontClass(vm);
			  }
		  }

		  // 触发字体变化事件，通知所有组件更新
		  uni.$emit('languageFontChanged', {
			  language: this.lang,
			  isUyghur: this.isUyghur
		  });
	  },

	  // 设置页面字体类
	  setPageFontClass(vm) {
		  try {
			  // 在小程序中通过选择器设置class
			  const query = uni.createSelectorQuery().in(vm);
			  query.select('page').exec((res) => {
				  if (res && res[0]) {
					  // 移除之前的语言字体类
					  const classList = ['lang-zh', 'lang-en', 'lang-ug'];
					  classList.forEach(cls => {
						  if (res[0].classList && res[0].classList.contains(cls)) {
							  res[0].classList.remove(cls);
						  }
					  });

					  // 添加当前语言的字体类
					  const langClass = `lang-${this.lang}`;
					  if (res[0].classList) {
						  res[0].classList.add(langClass);
					  }
				  }
			  });
		  } catch (error) {
			  console.warn('设置页面字体类失败:', error);
		  }
	  },

	  // 初始化语言设置
	  initLanguage() {
		  // 如果没有保存的语言设置，使用系统语言
		  if (!uni.getStorageSync("selectedLanguage")) {
			  const systemLang = detectSystemLanguage();
			  this.setLanguage(systemLang);
		  } else {
			  // 初始化时也要应用字体设置
			  this.isUyghur = this.lang === 'ug';
			  this.applyLanguageFont();
		  }
	  },
	  setUserInfo(user_info){
		  this.user_info=user_info;
	  }
  }
});