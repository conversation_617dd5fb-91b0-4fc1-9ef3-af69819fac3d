// 简化的国际化管理器
// 整合了项目中所有国际化相关的逻辑，提供统一的国际化管理

import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { fontManager } from './fontManager'

/**
 * 国际化管理器类
 * 负责管理语言切换、翻译、字体联动等
 */
export class I18nManager {
  constructor() {
    this.currentLanguage = ref('zh-CN')
    this.updateKey = ref(0)
    this.pageInstances = new Set()
    
    // 支持的语言列表
    this.supportedLanguages = [
      { code: 'zh-CN', name: '中文', nativeName: '中文' },
      { code: 'en', name: 'English', nativeName: 'English' },
      { code: 'ug', name: 'Uyghur', nativeName: 'ئۇيغۇرچە' }
    ]
  }

  /**
   * 获取当前语言
   * @returns {string} 语言代码
   */
  getCurrentLanguage() {
    return this.currentLanguage.value
  }

  /**
   * 设置语言
   * @param {string} lang 语言代码
   */
  setLanguage(lang) {
    if (!this.isLanguageSupported(lang)) {
      console.warn(`不支持的语言: ${lang}`)
      return
    }

    const oldLang = this.currentLanguage.value
    this.currentLanguage.value = lang
    
    // 更新字体管理器
    fontManager.setLanguageFont(lang)
    
    // 触发更新
    this.updateKey.value++
    
    // 通知所有页面实例
    this.notifyPageInstances(lang, oldLang)
    
    // 触发全局事件
    this.emitLanguageChange(lang, oldLang)
  }

  /**
   * 检查语言是否支持
   * @param {string} lang 语言代码
   * @returns {boolean} 是否支持
   */
  isLanguageSupported(lang) {
    return this.supportedLanguages.some(item => item.code === lang)
  }

  /**
   * 获取语言信息
   * @param {string} lang 语言代码
   * @returns {object} 语言信息
   */
  getLanguageInfo(lang) {
    return this.supportedLanguages.find(item => item.code === lang)
  }

  /**
   * 获取所有支持的语言
   * @returns {array} 语言列表
   */
  getSupportedLanguages() {
    return this.supportedLanguages
  }

  /**
   * 注册页面实例
   * @param {object} instance 页面实例
   */
  registerPageInstance(instance) {
    this.pageInstances.add(instance)
  }

  /**
   * 注销页面实例
   * @param {object} instance 页面实例
   */
  unregisterPageInstance(instance) {
    this.pageInstances.delete(instance)
  }

  /**
   * 通知所有页面实例
   * @param {string} newLang 新语言
   * @param {string} oldLang 旧语言
   */
  notifyPageInstances(newLang, oldLang) {
    this.pageInstances.forEach(instance => {
      if (instance && typeof instance.onLanguageChange === 'function') {
        try {
          instance.onLanguageChange(newLang, oldLang)
        } catch (error) {
          console.warn('页面实例语言切换失败:', error)
        }
      }
    })
  }

  /**
   * 触发语言变化事件
   * @param {string} newLang 新语言
   * @param {string} oldLang 旧语言
   */
  emitLanguageChange(newLang, oldLang) {
    try {
      // uni-app事件
      uni.$emit('languageChanged', {
        newLanguage: newLang,
        oldLanguage: oldLang,
        isUyghur: newLang === 'ug',
        fontState: fontManager.getCurrentFontState()
      })
      
      // 自定义事件（如果在浏览器环境）
      if (typeof window !== 'undefined' && window.dispatchEvent) {
        const event = new CustomEvent('i18nLanguageChange', {
          detail: { newLang, oldLang }
        })
        window.dispatchEvent(event)
      }
    } catch (error) {
      console.warn('触发语言变化事件失败:', error)
    }
  }

  /**
   * 创建翻译函数
   * @param {object} i18nInstance vue-i18n实例
   * @returns {function} 翻译函数
   */
  createTranslateFunction(i18nInstance) {
    return (key, params = {}) => {
      try {
        return i18nInstance.t(key, params)
      } catch (error) {
        console.warn(`翻译失败: ${key}`, error)
        return key
      }
    }
  }

  /**
   * 获取计算属性
   * @returns {object} 计算属性对象
   */
  getComputedProperties() {
    return {
      currentLanguage: computed(() => this.currentLanguage.value),
      updateKey: computed(() => this.updateKey.value),
      isUyghur: computed(() => this.currentLanguage.value === 'ug'),
      isRTL: computed(() => fontManager.isRTL(this.currentLanguage.value)),
      fontClass: computed(() => fontManager.generateFontClass()),
      fontStyle: computed(() => fontManager.generateFontStyle())
    }
  }

  /**
   * 格式化消息
   * @param {string} template 消息模板
   * @param {object} params 参数
   * @returns {string} 格式化后的消息
   */
  formatMessage(template, params = {}) {
    if (!template || typeof template !== 'string') return ''
    
    return template.replace(/\{(\w+)\}/g, (match, key) => {
      return params[key] !== undefined ? params[key] : match
    })
  }

  /**
   * 获取本地化的日期格式
   * @param {string} lang 语言代码
   * @returns {string} 日期格式
   */
  getDateFormat(lang = this.currentLanguage.value) {
    const formats = {
      'zh-CN': 'YYYY年MM月DD日',
      'en': 'MM/DD/YYYY',
      'ug': 'YYYY-MM-DD'
    }
    
    return formats[lang] || formats['zh-CN']
  }

  /**
   * 获取本地化的数字格式
   * @param {string} lang 语言代码
   * @returns {object} 数字格式配置
   */
  getNumberFormat(lang = this.currentLanguage.value) {
    const formats = {
      'zh-CN': { decimal: '.', thousands: ',' },
      'en': { decimal: '.', thousands: ',' },
      'ug': { decimal: '.', thousands: ',' }
    }
    
    return formats[lang] || formats['zh-CN']
  }

  /**
   * 重置到默认语言
   */
  reset() {
    this.setLanguage('zh-CN')
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.pageInstances.clear()
    this.currentLanguage.value = 'zh-CN'
    this.updateKey.value = 0
  }
}

// 创建全局国际化管理器实例
export const i18nManager = new I18nManager()

/**
 * 国际化 Composition API
 * 提供统一的国际化功能
 */
export function useI18nManager() {
  const { t, locale } = useI18n()
  
  // 获取计算属性
  const computed = i18nManager.getComputedProperties()
  
  // 翻译函数
  const $t = i18nManager.createTranslateFunction({ t })
  
  // 切换语言
  const switchLanguage = (lang) => {
    i18nManager.setLanguage(lang)
    locale.value = lang
  }
  
  // 获取语言列表
  const getLanguages = () => {
    return i18nManager.getSupportedLanguages()
  }
  
  // 格式化消息
  const formatMessage = (template, params) => {
    return i18nManager.formatMessage(template, params)
  }
  
  return {
    // 响应式状态
    ...computed,
    
    // 方法
    $t,
    switchLanguage,
    getLanguages,
    formatMessage,
    
    // 管理器实例
    i18nManager
  }
}

// 便捷方法导出
export const {
  getCurrentLanguage,
  setLanguage,
  isLanguageSupported,
  getLanguageInfo,
  getSupportedLanguages
} = i18nManager

// 默认导出
export default i18nManager
