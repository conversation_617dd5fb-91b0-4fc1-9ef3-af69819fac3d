// 国际化配置入口文件
import zhCN from './zh-CN.js'
import en from './en.js'
import ug from './ug.js'

// 语言配置
export const languages = {
  'zh-CN': {
    name: '简体中文',
    nativeName: '简体中文',
    code: 'zh-CN',
    messages: zhCN
  },
  'en': {
    name: 'English',
    nativeName: 'English', 
    code: 'en',
    messages: en
  },
  'ug': {
    name: 'ئۇيغۇرچە',
    nativeName: 'ئۇيغۇرچە',
    code: 'ug',
    messages: ug
  }
}

// 默认语言
export const defaultLanguage = 'zh-CN'

// 获取当前语言
export function getCurrentLanguage() {
  return uni.getStorageSync('selectedLanguage') || defaultLanguage
}

// 设置当前语言
export function setCurrentLanguage(lang) {
  uni.setStorageSync('selectedLanguage', lang)
}

// 获取语言消息
export function getLanguageMessages(lang) {
  return languages[lang]?.messages || languages[defaultLanguage].messages
}

// 翻译函数
export function t(key, lang = null) {
  const currentLang = lang || getCurrentLanguage()
  const messages = getLanguageMessages(currentLang)
  
  // 支持嵌套键值，如 'common.confirm'
  const keys = key.split('.')
  let result = messages
  
  for (const k of keys) {
    if (result && typeof result === 'object' && k in result) {
      result = result[k]
    } else {
      console.warn(`Translation key "${key}" not found for language "${currentLang}"`)
      return key // 返回原始key作为fallback
    }
  }
  
  return result || key
}

// 获取所有可用语言
export function getAvailableLanguages() {
  return Object.keys(languages).map(code => ({
    code,
    name: languages[code].name,
    nativeName: languages[code].nativeName
  }))
}

// 检测系统语言
export function detectSystemLanguage() {
  try {
    // 使用新的API获取系统信息
    let systemLang = 'zh-CN'

    // 优先使用新的API
    if (uni.getAppBaseInfo) {
      const appInfo = uni.getAppBaseInfo()
      systemLang = appInfo.language || 'zh-CN'
    } else {
      // 兼容旧版本
      const systemInfo = uni.getSystemInfoSync()
      systemLang = systemInfo.language || 'zh-CN'
    }

    // 语言映射
    const langMap = {
      'zh': 'zh-CN',
      'zh-CN': 'zh-CN',
      'zh-Hans': 'zh-CN',
      'en': 'en',
      'en-US': 'en',
      'ug': 'ug'
    }

    return langMap[systemLang] || defaultLanguage
  } catch (error) {
    console.error('Failed to detect system language:', error)
    return defaultLanguage
  }
}
