// 小程序专用字体管理器
import { useAppStore } from '@/store/app.js'

export class MiniProgramFontManager {
  constructor() {
    this.currentLanguage = 'zh-CN'
    this.isUyghur = false
    this.initialized = false
  }

  // 初始化字体管理器
  init() {
    if (this.initialized) return
    
    try {
      const appStore = useAppStore()
      this.currentLanguage = appStore.lang
      this.isUyghur = appStore.isUyghur
      
      // 监听语言变化事件
      uni.$on('languageChanged', this.handleLanguageChange.bind(this))
      uni.$on('languageFontChanged', this.handleFontChange.bind(this))
      
      this.initialized = true
      console.log('小程序字体管理器已初始化')
    } catch (error) {
      console.warn('初始化字体管理器失败:', error)
    }
  }

  // 处理语言变化
  handleLanguageChange(data) {
    this.currentLanguage = data.language
    this.isUyghur = data.isUyghur
    
    // 应用字体设置
    this.applyFontSettings()
  }

  // 处理字体变化
  handleFontChange(data) {
    this.currentLanguage = data.language
    this.isUyghur = data.isUyghur
    
    // 通知所有页面更新字体
    this.notifyPagesUpdate()
  }

  // 应用字体设置
  applyFontSettings() {
    try {
      // 获取当前页面
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        
        // 设置页面数据
        if (currentPage && currentPage.setData) {
          currentPage.setData({
            isUyghur: this.isUyghur,
            currentLanguage: this.currentLanguage,
            fontClass: this.getFontClass()
          })
        }
      }
    } catch (error) {
      console.warn('应用字体设置失败:', error)
    }
  }

  // 通知页面更新
  notifyPagesUpdate() {
    try {
      // 触发全局事件
      uni.$emit('miniProgramFontUpdate', {
        language: this.currentLanguage,
        isUyghur: this.isUyghur,
        fontClass: this.getFontClass()
      })
    } catch (error) {
      console.warn('通知页面更新失败:', error)
    }
  }

  // 获取字体类
  getFontClass() {
    return {
      'ug': this.isUyghur,
      [`lang-${this.currentLanguage}`]: true,
      'uyghur-font': this.isUyghur
    }
  }

  // 获取字体样式
  getFontStyle() {
    return this.isUyghur ? { fontFamily: 'uy' } : {}
  }

  // 为页面设置字体类
  setPageFontClass(pageInstance) {
    if (!pageInstance) return
    
    try {
      const fontClass = this.getFontClass()
      
      // 如果页面有setData方法（小程序）
      if (pageInstance.setData) {
        pageInstance.setData({
          fontClass: fontClass,
          isUyghur: this.isUyghur
        })
      }
      
      // 如果是Vue组件实例
      if (pageInstance.$data) {
        Object.assign(pageInstance.$data, {
          fontClass: fontClass,
          isUyghur: this.isUyghur
        })
      }
    } catch (error) {
      console.warn('设置页面字体类失败:', error)
    }
  }

  // 检查是否为维吾尔文
  isUyghurLanguage() {
    return this.isUyghur
  }

  // 获取当前字体状态
  getCurrentFontState() {
    return {
      language: this.currentLanguage,
      isUyghur: this.isUyghur,
      fontClass: this.getFontClass(),
      fontStyle: this.getFontStyle()
    }
  }

  // 销毁管理器
  destroy() {
    try {
      uni.$off('languageChanged', this.handleLanguageChange)
      uni.$off('languageFontChanged', this.handleFontChange)
      this.initialized = false
      console.log('小程序字体管理器已销毁')
    } catch (error) {
      console.warn('销毁字体管理器失败:', error)
    }
  }
}

// 创建全局实例
export const miniProgramFontManager = new MiniProgramFontManager()

// 初始化函数
export function initMiniProgramFontManager() {
  miniProgramFontManager.init()
}

// 获取字体类的工具函数
export function getMiniProgramFontClass() {
  return miniProgramFontManager.getFontClass()
}

// 获取字体样式的工具函数
export function getMiniProgramFontStyle() {
  return miniProgramFontManager.getFontStyle()
}

// 检查是否为维吾尔文的工具函数
export function isMiniProgramUyghur() {
  return miniProgramFontManager.isUyghurLanguage()
}

// 为页面混入字体管理功能
export function createFontMixin() {
  return {
    data() {
      return {
        fontClass: {},
        isUyghur: false,
        currentLanguage: 'zh-CN'
      }
    },
    
    onLoad() {
      // 初始化字体状态
      this.updateFontState()
      
      // 监听字体更新事件
      uni.$on('miniProgramFontUpdate', this.handleFontUpdate)
    },
    
    onUnload() {
      // 移除事件监听
      uni.$off('miniProgramFontUpdate', this.handleFontUpdate)
    },
    
    methods: {
      // 更新字体状态
      updateFontState() {
        const state = miniProgramFontManager.getCurrentFontState()
        this.setData({
          fontClass: state.fontClass,
          isUyghur: state.isUyghur,
          currentLanguage: state.language
        })
      },
      
      // 处理字体更新
      handleFontUpdate(data) {
        this.setData({
          fontClass: data.fontClass,
          isUyghur: data.isUyghur,
          currentLanguage: data.language
        })
      }
    }
  }
}
