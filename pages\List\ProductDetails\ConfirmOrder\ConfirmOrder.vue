<template>
	<!--
		🛒 确认订单页面模板
		这是用户确认购买产品的页面，包含商品信息、物流信息、订单金额等
		用户可以在这里填写收货地址、选择数量、确认订单等操作
	-->

	<!--
		📄 确认订单页面容器
		- class="confirm-order-page": 基础页面样式
		- :class: 动态样式类，包括字体大小和字体类型
		- :style: 动态样式，主要用于RTL布局
		- :key: 强制重新渲染的键，当字体或语言变化时重新渲染
	-->
	<view class="confirm-order-page" :class="[fontSizeClass, fontClass]" :style="pageStyle" :key="`${fontSizeUpdateKey}-${i18nUpdateKey}`">
		<!--
			📜 可滚动内容区域
			因为订单信息可能很长，所以需要支持垂直滚动
			scroll-y="true": 启用垂直滚动
		-->
		<scroll-view class="scroll-content" scroll-y="true">
			<!--
				💊 商品信息区域
				显示用户要购买的商品详细信息
			-->
			<view class="section">
				<!--
					📋 区域标题头部
					包含装饰线和标题文字
				-->
				<view class="section-header">
					<!-- 装饰线 -->
					<view class="section-line"></view>
					<!--
						区域标题
						$t('confirmOrder.productInfo'): 从语言包获取"商品信息"文字
					-->
					<text class="section-title">{{ $t('confirmOrder.productInfo') }}</text>
				</view>

				<!--
					📦 商品项目
					显示单个商品的详细信息
				-->
				<view class="product-item">
					<!-- 🖼️ 商品图片区域 -->
					<view class="product-image">
						<!-- 商品图片占位符 -->
						<view class="product-placeholder">
							<!--
								商品图标
								使用应用logo作为商品图标
								注释掉的部分是药丸emoji图标
							-->
							<!-- <text class="product-icon">💊</text> -->
							<image class="product-icon" src="/static/logo.png"></image>
						</view>
					</view>

					<!-- 📝 商品详细信息 -->
					<view class="product-details">
						<!--
							商品名称
							productName: 商品的名称，来自页面数据
						-->
						<text class="product-name">{{ productName }}</text>

						<!--
							商品规格信息
							$t('confirmOrder.doctorName'): 从语言包获取"医生："文字
							productData.doctor: 推荐该商品的医生姓名
						-->
						<text class="product-spec">{{ $t('confirmOrder.doctorName') }}{{ productData.doctor }}</text>

						<!--
							商品价格
							productData.price.toFixed(2): 商品价格，保留2位小数
						-->
						<text class="product-price">¥{{ productData.price.toFixed(2) }}</text>
					</view>
				</view>

				<!--
					🔢 数量选择行
					用户可以在这里调整购买数量
				-->
				<view class="quantity-row">
					<!--
						数量标签
						$t('confirmOrder.quantity'): 从语言包获取"数量"文字
					-->
					<text class="quantity-label">{{ $t('confirmOrder.quantity') }}</text>

					<!-- 数量控制器 -->
					<view class="quantity-controls">
						<!--
							减少数量按钮
							@tap: 点击时减少商品数量
						-->
						<view class="quantity-btn" @tap="decreaseQuantity">
							<text class="quantity-btn-text">-</text>
						</view>

						<!--
							当前数量显示
							quantity: 当前选择的商品数量
						-->
						<text class="quantity-value">{{ quantity }}</text>

						<!--
							增加数量按钮
							@tap: 点击时增加商品数量
						-->
						<view class="quantity-btn" @tap="increaseQuantity">
							<text class="quantity-btn-text">+</text>
						</view>
					</view>
				</view>

				<!--
					💰 小计行
					显示当前商品的小计金额
				-->
				<view class="subtotal-row">
					<!--
						小计标签
						$t('confirmOrder.subtotal'): 从语言包获取"小计"文字
					-->
					<text class="subtotal-label">{{ $t('confirmOrder.subtotal') }}</text>

					<!--
						小计金额
						(productData.price * quantity).toFixed(2): 单价乘以数量，保留2位小数
					-->
					<text class="subtotal-price">¥{{ (productData.price * quantity).toFixed(2) }}</text>
				</view>
			</view>

			<!--
				🚚 物流信息区域
				用户填写收货地址等物流相关信息
			-->
			<view class="section">
				<!-- 物流信息标题头部 -->
				<view class="section-header">
					<view class="section-title-container">
						<!-- 装饰线 -->
						<view class="section-line"></view>
						<!--
							物流信息标题
							$t('confirmOrder.shippingInfo'): 从语言包获取"物流信息"文字
						-->
						<text class="section-title">{{ $t('confirmOrder.shippingInfo') }}</text>
					</view>
					<view class="address-manage-container" @tap="selectAddress">
						<fui-icon name="location" :size="30" color="#109d58"></fui-icon>
						<text class="address-manage-text">{{ $t('confirmOrder.addressManage') }}</text>
					</view>
				</view>

				<!-- 表单字段循环 -->
				<view
					v-for="field in formFields"
					:key="field.key"
					class="form-item"
				>
					<text class="form-label">{{ $t(field.labelKey) }}</text>

					<!-- 普通输入框 -->
					<view v-if="field.type === 'input'" class="input-wrapper">
						<fui-icon
							class="input-icon"
							:size="50"
							:name="field.iconName"
							color="#000"
						></fui-icon>
						<input
							class="form-input"
							:placeholder="$t(field.placeholderKey)"
							:value="field.value"
							@input="field.onInput"
						/>
					</view>

					<!-- 地址选择器 -->
					<view v-else-if="field.type === 'selector'" class="address-selector" @tap="field.action">
						<text class="input-icon">{{ field.iconText }}</text>
						<text class="address-text">{{ field.displayValue || $t(field.defaultKey) }}</text>
						<text class="arrow-icon">></text>
					</view>

					<!-- 文本域 -->
					<view v-else-if="field.type === 'textarea'" class="address-input-wrapper">
						<fui-icon
							class="input-icon"
							:name="field.iconName"
							color="#000"
							:size="50"
							style="padding-top:30rpx;"
						></fui-icon>
						<textarea
							class="address-input"
							:placeholder="$t(field.placeholderKey)"
							:value="field.value"
							@input="field.onInput"
						></textarea>
					</view>
				</view>
			</view>

			<!-- 订单金额 -->
			<view class="section">
				<view class="section-header">
					<view class="section-line"></view>
					<text class="section-title">{{ $t('confirmOrder.orderAmount') }}</text>
				</view>

				<view class="amount-row">
					<text class="amount-label">{{ $t('confirmOrder.productAmount') }}</text>
					<text class="amount-value">¥{{ (productData.price * quantity).toFixed(2) }}</text>
				</view>

				<view class="amount-row">
					<text class="amount-label">{{ $t('confirmOrder.shippingFee') }}</text>
					<text class="amount-value free">{{ $t('confirmOrder.freeShipping') }}</text>
				</view>

				<view class="total-row">
					<text class="total-label">{{ $t('confirmOrder.totalAmount') }}</text>
					<text class="total-value">¥{{ (productData.price * quantity).toFixed(2) }}</text>
				</view>
			</view>

			<!-- 底部占位，防止内容被固定按钮遮挡 -->
			<view class="bottom-placeholder"></view>
		</scroll-view>

		<!-- 底部固定区域 -->
		<view class="bottom-fixed">
			<view class="total-section">
				<text class="total-text">{{ $t('confirmOrder.total') }}</text>
				<text class="total-amount">¥{{ (productData.price * quantity).toFixed(2) }}</text>
			</view>
			<view class="submit-btn" @tap="submitOrder">
				<text class="submit-text">{{ $t('confirmOrder.submitOrder') }}</text>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted,
		onUnmounted
	} from 'vue'
	import { useFontSizePage } from '@/utils/fontSizeMixin.js'
	import { useRTLPage } from '@/utils/rtlMixin.js'
	import { useAppStore } from '@/store/app.js'
	import { t } from '@/locale/index.js'
	import { onShow as onShowHook } from '@dcloudio/uni-app'

	// 响应式数据
	const quantity = ref(1)
	const receiverName = ref('')
	const receiverPhone = ref('')
	const selectedAddress = ref('')
	const receiverAddress = ref('')

	// 示例产品数据（实际应该从上一页传递）
	const productData = ref({
		id: 1,
		nameKey: 'kidneyTonic',
		price: 218.00,
		doctor: '沙迪克'
	})

	// 计算属性：获取产品的国际化名称
	const productName = computed(() => {
		// 添加语言变化的响应
		const lang = currentLanguage.value
		if (productData.value.nameKey) {
			return $t(`productDetails.products.${productData.value.nameKey}.name`)
		}
		return '肾宝胶囊' // 默认值
	})

	// 使用字体大小功能
	const { fontSizeClass, fontSizeUpdateKey } = useFontSizePage()

	// RTL布局支持
	const { pageClass, pageStyle } = useRTLPage()

	// 应用状态
	const appStore = useAppStore()

	// 国际化支持
	const currentLanguage = ref(appStore.lang)
	const i18nUpdateKey = ref(0)

	// 计算字体类（合并RTL类）
	const fontClass = computed(() => ({
		...pageClass.value,
		'ug': appStore.isUyghur,
		[`lang-${appStore.lang}`]: true
	}))

	// 国际化翻译函数
	const $t = (key, params) => {
		const result = t(key, params)
		console.log(`翻译 ${key}:`, result)
		return result
	}

	// 页面显示时检查是否有从SelectAddress传回的数据
	onShowHook(() => {
		console.log('ConfirmOrder页面显示，检查地址数据')
		console.log('当前语言:', appStore.lang)
		console.log('测试翻译:', $t('confirmOrder.productInfo'))
		console.log('产品名称:', productName.value)

		// 从本地存储读取选中的地址数据
		const selectedData = uni.getStorageSync('selectedAddressData')
		if (selectedData && selectedData.timestamp) {
			console.log('从本地存储读取到地址数据:', selectedData)

			// 更新表单数据
			receiverName.value = selectedData.name
			receiverPhone.value = selectedData.phone
			selectedAddress.value = selectedData.region
			receiverAddress.value = selectedData.address

			// 清除临时数据
			uni.removeStorageSync('selectedAddressData')

			console.log('地址数据已更新到表单')
		}

		console.log('当前表单数据:', {
			receiverName: receiverName.value,
			receiverPhone: receiverPhone.value,
			selectedAddress: selectedAddress.value,
			receiverAddress: receiverAddress.value
		})
	})

	// 页面挂载时的初始化
	onMounted(() => {
		console.log('ConfirmOrder页面挂载完成')

		// 监听地址选择事件
		uni.$on('addressSelected', (addressData) => {
			console.log('接收到选择的地址数据:', addressData)

			// 更新页面数据
			receiverName.value = addressData.name
			receiverPhone.value = addressData.phone
			selectedAddress.value = addressData.region
			receiverAddress.value = addressData.address

			console.log('地址信息已更新到表单')
		})
	})

	// 页面卸载时移除事件监听
	onUnmounted(() => {
		uni.$off('addressSelected')
	})

	// 数量控制
	const decreaseQuantity = () => {
		if (quantity.value > 1) {
			quantity.value--
		}
	}

	const increaseQuantity = () => {
		quantity.value++
	}

	// 返回上一页
	const goBack = () => {
		uni.navigateBack()
	}

	// 表单字段配置数组
	const formFields = computed(() => [
		{
			key: 'receiverName',
			type: 'input',
			labelKey: 'confirmOrder.receiverName',
			placeholderKey: 'confirmOrder.receiverNamePlaceholder',
			iconName: 'my',
			value: receiverName.value,
			onInput: (e) => { receiverName.value = e.detail.value }
		},
		{
			key: 'receiverPhone',
			type: 'input',
			labelKey: 'confirmOrder.receiverPhone',
			placeholderKey: 'confirmOrder.receiverPhonePlaceholder',
			iconName: 'telephone',
			value: receiverPhone.value,
			onInput: (e) => { receiverPhone.value = e.detail.value }
		},
		{
			key: 'region',
			type: 'selector',
			labelKey: 'confirmOrder.region',
			iconText: '🏢',
			displayValue: selectedAddress.value,
			defaultKey: 'confirmOrder.defaultRegion',
			action: selectAddress
		},
		{
			key: 'address',
			type: 'textarea',
			labelKey: 'confirmOrder.address',
			placeholderKey: 'confirmOrder.addressPlaceholder',
			iconName: 'location',
			value: receiverAddress.value,
			onInput: (e) => { receiverAddress.value = e.detail.value }
		}
	])

	// 选择地址 - 跳转到地址选择页面
	const selectAddress = () => {
		uni.navigateTo({
			url: '/pages/List/ProductDetails/ConfirmOrder/SelectAddress/SelectAddress'
		})
	}


	// 提交订单
	const submitOrder = () => {
		if (!receiverName.value) {
			uni.showToast({
				title: $t('confirmOrder.nameRequired'),
				icon: 'none'
			})
			return
		}

		if (!receiverPhone.value) {
			uni.showToast({
				title: $t('confirmOrder.phoneRequired'),
				icon: 'none'
			})
			return
		}

		if (!receiverAddress.value) {
			uni.showToast({
				title: $t('confirmOrder.addressRequired'),
				icon: 'none'
			})
			return
		}

		uni.showToast({
			title: $t('confirmOrder.orderSubmitSuccess'),
			icon: 'success'
		})
	}

	// 监听语言变化
	uni.$on('languageChanged', (data) => {
		console.log('ConfirmOrder页面接收到语言变化:', data.lang)
		currentLanguage.value = data.lang
		i18nUpdateKey.value++
	})

	// 页面卸载时取消监听
	onUnmounted(() => {
		uni.$off('languageChanged')
	})
</script>

<style lang="scss" scoped>
	.confirm-order-page {
		width: 100%;
		height: 100vh;
		background-color: #f5f5f5;
		display: flex;
		flex-direction: column;
	}

	/* 导航栏 */
	.navbar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;
		background-color: #ffffff;
		padding: 0 32rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		position: relative;
		z-index: 100;
	}

	.nav-left {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.back-icon {
		font-size: 36rpx;
		color: #333333;
		font-weight: 500;
	}

	.nav-title {
		flex: 1;
		display: flex;
		justify-content: center;
	}

	.title-text {
		font-size: 36rpx;
		color: #333333;
		font-weight: 600;
	}

	.nav-right {
		width: 80rpx;
	}

	/* 滚动内容区域 */
	.scroll-content {
		flex: 1;
		padding: 24rpx 24rpx 32rpx;
		background-color: #f5f5f5;
	}

	/* 区块样式 */
	.section {
		background-color: #ffffff;
		border-radius: 24rpx;
		padding: 40rpx;
		margin: 0 60rpx 30rpx 0;
		box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
	}

	.section-header {
		display: flex;
		align-items: center;
		margin-bottom: 40rpx;
		position: relative;
	}

	.section-line {
		width: 8rpx;
		height: 36rpx;
		background-color: #4CAF50;
		border-radius: 4rpx;
		margin-right: 20rpx;
		margin: 0 0 0 20rpx;
	}

	.section-title-container {
		display: flex;
		align-items: center;
		flex: 1;
	}

	.section-title {
		font-size: 36rpx;
		color: #333333;
		font-weight: 600;
		flex: 1;
	}

	.address-manage-container {
		display: flex;
		align-items: center;
		justify-content: center;
		text-align: center;
		background-color: #f0f9f4;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		position: absolute;
		right: 0;
	}

	.address-manage-text {
		font-size: 28rpx;
		color: #109d58;
		margin-left: 10rpx;
		font-weight: 500;
	}

	/* 商品信息样式 */
	.product-item {
		display: flex;
		align-items: flex-start;
		margin-bottom: 40rpx;
	}

	.product-image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 16rpx;
		overflow: hidden;
		margin-right: 32rpx;
		background-color: #f0f0f0;
	}

	.product-placeholder {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #e8e8e8;
	}

	.product-icon {
		font-size: 64rpx;
	}

	.product-img {
		width: 100%;
		height: 100%;
	}

	.product-details {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.product-name {
		font-size: 36rpx;
		color: #333333;
		font-weight: 600;
		margin-bottom: 16rpx;
	}

	.product-spec {
		font-size: 28rpx;
		color: #666666;
		margin-bottom: 24rpx;
	}

	.product-price {
		font-size: 36rpx;
		color: #4CAF50;
		font-weight: 600;
	}

	.quantity-row {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 32rpx;
	}

	.quantity-label {
		font-size: 32rpx;
		color: #333333;
	}

	.quantity-controls {
		display: flex;
		align-items: center;
		border: 2rpx solid #e0e0e0;
		border-radius: 12rpx;
		overflow: hidden;
		background-color: #ffffff;
	}

	.quantity-btn {
		width: 80rpx;
		height: 64rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		// background-color: #f8f8f8;
		// border-right: 2rpx solid #e0e0e0;
	}

	.quantity-btn:last-child {
		border-right: none;
		// border-left: 2rpx solid #e0e0e0;
	}

	.quantity-btn-text {
		font-size: 36rpx;
		color: #4CAF50;
		font-weight: 600;
	}

	.quantity-value {
		width: 100rpx;
		height: 64rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #333333;
		background-color: #ffffff;
	}

	.subtotal-row {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.subtotal-label {
		font-size: 32rpx;
		color: #333333;
	}

	.subtotal-price {
		font-size: 36rpx;
		color: #4CAF50;
		font-weight: 600;
	}

	/* 表单样式 */
	.form-item {
		margin-bottom: 40rpx;
	}

	.form-label {
		font-size: 32rpx;
		color: #333333;
		margin-bottom: 20rpx;
		display: block;
	}

	.input-wrapper {
		display: flex;
		align-items: center;
		background-color: #f5f5f5;
		border-radius: 16rpx;
		padding: 28rpx 32rpx;
		border: 2rpx solid transparent;
	}

	.input-wrapper:focus-within {
		border-color: #4CAF50;
	}

	.input-icon {
		font-size: 36rpx;
		margin-right: 24rpx;
		opacity: 0.6;
		display: flex;
		align-items: center;
		justify-content: center;
		text-align: center;
		width: 36rpx;
		margin: 0 0 0 20rpx;
	}

	.form-input {
		flex: 1;
		font-size: 32rpx;
		color: #333333;
		background-color: transparent;
	}

	/* 维吾尔文输入框字体 */
	.form-input.ug,
	.confirm-order-page.ug .form-input,
	.confirm-order-page.lang-ug .form-input {
		font-family: 'uy' !important;
	}

	.form-input::placeholder {
		color: #999999;
	}

	/* 维吾尔文输入框placeholder字体 */
	.form-input.ug::placeholder,
	.confirm-order-page.ug .form-input::placeholder,
	.confirm-order-page.lang-ug .form-input::placeholder {
		font-family: 'uy' !important;
	}

	.address-selector {
		display: flex;
		align-items: center;
		background-color: #f5f5f5;
		border-radius: 16rpx;
		padding: 28rpx 32rpx;
		border: 2rpx solid transparent;
	}

	.address-text {
		flex: 1;
		font-size: 24rpx;
		color: #333333;
		margin-left: 24rpx;
	}

	.arrow-icon {
		font-size: 32rpx;
		color: #4CAF50;
		margin-left: 24rpx;
	}

	.address-input-wrapper {
		display: flex;
		align-items: flex-start;
		background-color: #f5f5f5;
		border-radius: 16rpx;
		padding: 28rpx 32rpx;
		border: 2rpx solid transparent;
		height: 110rpx;

	}

	.address-input-wrapper:focus-within {
		border-color: #4CAF50;
	}

	.address-input {
		flex: 1;
		font-size: 32rpx;
		color: #333333;
		background-color: transparent;
		height: 110rpx;
		margin-left: 24rpx;
	}

	/* 维吾尔文地址输入框字体 */
	.address-input.ug,
	.confirm-order-page.ug .address-input,
	.confirm-order-page.lang-ug .address-input {
		font-family: 'uy' !important;
	}

	.address-input::placeholder {
		color: #999999;
	}

	/* 维吾尔文地址输入框placeholder字体 */
	.address-input.ug::placeholder,
	.confirm-order-page.ug .address-input::placeholder,
	.confirm-order-page.lang-ug .address-input::placeholder {
		font-family: 'uy' !important;
	}

	/* 金额样式 */
	.amount-row {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 32rpx;
	}

	.amount-label {
		font-size: 32rpx;
		color: #333333;
	}

	.amount-value {
		font-size: 32rpx;
		color: #333333;
	}

	.amount-value.free {
		color: #4CAF50;
	}

	.total-row {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-top: 32rpx;
		border-top: 2rpx solid #f0f0f0;
	}

	.total-label {
		font-size: 36rpx;
		color: #333333;
		font-weight: 600;
	}

	.total-value {
		font-size: 40rpx;
		color: #4CAF50;
		font-weight: 700;
	}

	/* 底部占位 */
	.bottom-placeholder {
		height: 160rpx;
	}

	/* 底部固定区域 */
	.bottom-fixed {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #ffffff;
		padding: 40rpx 24rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
		z-index: 100;
	}

	.total-section {
		display: flex;
		align-items: baseline;
		margin-right: 40rpx;
	}

	.total-text {
		font-size: 32rpx;
		color: #333333;
		margin-right: 12rpx;
	}

	.total-amount {
		font-size: 40rpx;
		color: #4CAF50;
		font-weight: 700;
	}

	.submit-btn {
		flex: 1;
		height: 96rpx;
		background: linear-gradient(135deg, #109d58 0%, #109d58 100%);
		border-radius: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 20rpx rgba(76, 175, 80, 0.3);
	}

	.submit-btn:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
	}

	.submit-text {
		font-size: 36rpx;
		color: #ffffff;
		font-weight: 600;
	}

	/* 字体大小响应式样式 */
	.font-size-small {
		.section-title {
			font-size: 28rpx !important;
		}

		.product-name {
			font-size: 26rpx !important;
		}

		.product-spec {
			font-size: 22rpx !important;
		}

		.product-price {
			font-size: 28rpx !important;
		}

		.quantity-label {
			font-size: 26rpx !important;
		}

		.quantity-value {
			font-size: 26rpx !important;
		}

		.quantity-btn-text {
			font-size: 28rpx !important;
		}

		.subtotal-label {
			font-size: 26rpx !important;
		}

		.subtotal-price {
			font-size: 28rpx !important;
		}

		.receiver-name {
			font-size: 26rpx !important;
		}

		.receiver-phone {
			font-size: 24rpx !important;
		}

		.receiver-address {
			font-size: 24rpx !important;
		}

		.payment-method-text {
			font-size: 26rpx !important;
		}

		.total-label {
			font-size: 28rpx !important;
		}

		.total-price {
			font-size: 32rpx !important;
		}

		.submit-text {
			font-size: 32rpx !important;
		}
	}

	.font-size-medium {
		.section-title {
			font-size: 32rpx !important;
		}

		.product-name {
			font-size: 30rpx !important;
		}

		.product-spec {
			font-size: 26rpx !important;
		}

		.product-price {
			font-size: 32rpx !important;
		}

		.quantity-label {
			font-size: 30rpx !important;
		}

		.quantity-value {
			font-size: 30rpx !important;
		}

		.quantity-btn-text {
			font-size: 32rpx !important;
		}

		.subtotal-label {
			font-size: 30rpx !important;
		}

		.subtotal-price {
			font-size: 32rpx !important;
		}

		.receiver-name {
			font-size: 30rpx !important;
		}

		.receiver-phone {
			font-size: 28rpx !important;
		}

		.receiver-address {
			font-size: 28rpx !important;
		}

		.payment-method-text {
			font-size: 30rpx !important;
		}

		.total-label {
			font-size: 32rpx !important;
		}

		.total-price {
			font-size: 36rpx !important;
		}

		.submit-text {
			font-size: 36rpx !important;
		}
	}

	.font-size-large {
		.section-title {
			font-size: 36rpx !important;
		}

		.product-name {
			font-size: 34rpx !important;
		}

		.product-spec {
			font-size: 30rpx !important;
		}

		.product-price {
			font-size: 36rpx !important;
		}

		.quantity-label {
			font-size: 34rpx !important;
		}

		.quantity-value {
			font-size: 34rpx !important;
		}

		.quantity-btn-text {
			font-size: 36rpx !important;
		}

		.subtotal-label {
			font-size: 34rpx !important;
		}

		.subtotal-price {
			font-size: 36rpx !important;
		}

		.receiver-name {
			font-size: 34rpx !important;
		}

		.receiver-phone {
			font-size: 32rpx !important;
		}

		.receiver-address {
			font-size: 32rpx !important;
		}

		.payment-method-text {
			font-size: 34rpx !important;
		}

		.total-label {
			font-size: 36rpx !important;
		}

		.total-price {
			font-size: 40rpx !important;
		}

		.submit-text {
			font-size: 40rpx !important;
		}
	}

	/* RTL布局支持 */
	.rtl-container {
		.section {
			margin: 0 0 30rpx 60rpx; /* RTL下镜像边距：左边距60rpx，右边距0 */
		}

		.scroll-content {
			padding: 24rpx 24rpx 32rpx 24rpx; /* RTL下保持对称内边距 */
		}

		.section-header {
			justify-content: space-between;
		}

		.section-title-container {
			order: 2; /* 让标题容器显示在右边 */
		}

		.address-manage-container {
			order: 1; /* 让地址管理按钮显示在左边 */
		}

		.product-item {
			flex-direction: row-reverse;
		}

		.product-details {
			margin-right: 0;
			margin-left: 30rpx;
			text-align: right;
		}

		.quantity-row {
			flex-direction: row-reverse;
		}

		.quantity-controls {
			margin-right: 0;
			margin-left: auto;
		}

		.subtotal-row {
			justify-content: space-between;
		}

		.subtotal-label {
			order: 1; /* 小计标签在左边 */
		}

		.subtotal-price {
			order: 2; /* 小计价格在右边 */
		}

		.amount-row {
			flex-direction: row-reverse;
		}

		.total-row {
			flex-direction: row-reverse;
		}

		.total-section {
			flex-direction: row-reverse;
		}

		.address-manage-container {
			left: 0;
			right: auto;
			margin-left: 0;
			margin-right: 20rpx;
		}

		.address-manage-text {
			margin-left: 0;
			margin-right: 10rpx;
		}

		.input-group {
			.input-wrapper {
				flex-direction: row-reverse;

				.input {
					text-align: right;
				}
			}
		}
	}
</style>