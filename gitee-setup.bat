@echo off
chcp 65001 >nul
echo 🎯 HealthApp Gitee 自动上传配置
echo ================================

REM 检查Git是否安装
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 请先安装Git
    echo 下载地址：https://gitee.com/all-about-git
    pause
    exit /b 1
)

echo ✅ Git已安装

REM 配置Git用户信息
echo.
echo 📝 配置Git用户信息...
set /p username="请输入您的Gitee用户名: "
set /p email="请输入您的Gitee邮箱: "

git config --global user.name "%username%"
git config --global user.email "%email%"

REM 初始化仓库并连接到Gitee
echo.
echo 🔗 连接到Gitee仓库...
git init
git remote add origin https://gitee.com/ablezz/HealthApp.git

REM 创建.gitignore文件
echo.
echo 📄 创建.gitignore文件...
echo # uni-app项目忽略文件 > .gitignore
echo unpackage/ >> .gitignore
echo node_modules/ >> .gitignore
echo .hbuilderx/ >> .gitignore
echo project.private.config.json >> .gitignore
echo *.log >> .gitignore
echo .DS_Store >> .gitignore
echo Thumbs.db >> .gitignore

REM 首次提交所有文件
echo.
echo 📦 首次提交所有文件...
git add .
git commit -m "初始提交：HealthApp健康助手小程序项目"
git push -u origin master

echo.
echo 🎉 配置完成！
echo 💡 现在您可以使用 quick-push.bat 快速上传代码
pause
