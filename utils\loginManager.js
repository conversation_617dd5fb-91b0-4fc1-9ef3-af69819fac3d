// 统一的登录管理工具
import { wechat_Authenticator } from '@/request/index.js'
import { loginSuccess } from '@/store/user.js'

/**
 * 统一的微信登录处理
 * @param {string} code - 微信登录code
 * @param {string} dis_code - 分销码（可选）
 * @param {boolean} silent - 是否静默登录（不显示错误提示）
 * @returns {Promise<Object>} 登录结果
 */
export const handleWechatLogin = async (code, dis_code = '', silent = false) => {
  try {
    console.log('🚀 统一登录管理 - 开始处理微信登录');
    console.log('- code:', code);
    console.log('- dis_code:', dis_code);
    console.log('- silent:', silent);
    
    // 调用后端登录接口
    const authRes = await wechat_Authenticator(code, dis_code);
    console.log('🔐 后端登录响应:', authRes);
    
    if (authRes.code === 200) {
      // 统一保存认证信息
      const success = saveAuthInfo(authRes);
      
      if (success) {
        // 更新用户状态
        updateUserState(authRes.data);
        
        console.log('✅ 微信登录成功');
        return {
          success: true,
          data: authRes.data,
          message: '登录成功',
          silent: silent
        };
      } else {
        throw new Error('保存认证信息失败');
      }
    } else {
      throw new Error(authRes.message || '登录失败');
    }
    
  } catch (error) {
    console.error('❌ 微信登录失败:', error);
    return {
      success: false,
      error: error.message || '登录失败',
      message: error.message || '登录失败'
    };
  }
}

/**
 * 统一保存认证信息
 * @param {Object} authRes - 后端认证响应
 * @returns {boolean} 是否保存成功
 */
export const saveAuthInfo = (authRes) => {
  try {
    console.log('💾 统一保存认证信息');
    
    const token = authRes.data.session_key;
    
    // 统一使用auth_token作为主要存储键名
    uni.setStorageSync('auth_token', token);
    uni.setStorageSync('session_key', token); // 保持兼容性
    
    // 保存用户信息
    uni.setStorageSync('userInfo', authRes.data);
    uni.setStorageSync('user_info', authRes.data);
    
    // 保存登录状态
    uni.setStorageSync('auth', true);
    uni.setStorageSync('is_logged_in', true);
    
    // 保存完整响应
    uni.setStorageSync('auth_response', authRes);
    
    // 保存登录时间
    uni.setStorageSync('login_time', new Date().toISOString());
    
    console.log('✅ 认证信息保存成功');
    console.log('- token:', token);
    console.log('- userInfo:', authRes.data);
    
    return true;
    
  } catch (error) {
    console.error('❌ 保存认证信息失败:', error);
    return false;
  }
}

/**
 * 更新用户状态
 * @param {Object} userData - 用户数据
 */
export const updateUserState = (userData) => {
  try {
    console.log('🔄 更新用户状态');
    
    // 更新全局用户状态
    loginSuccess(userData);
    
    // 触发用户信息更新事件
    uni.$emit('userInfoUpdated', userData);
    uni.$emit('loginSuccess', userData);
    
    console.log('✅ 用户状态更新成功');
    
  } catch (error) {
    console.error('❌ 更新用户状态失败:', error);
  }
}

// 自动登录功能已移除

/**
 * 检查登录状态
 * @returns {boolean} 是否已登录
 */
export const checkLoginStatus = () => {
  try {
    const token = uni.getStorageSync('auth_token') || uni.getStorageSync('session_key');
    const userInfo = uni.getStorageSync('userInfo');
    const isLoggedIn = uni.getStorageSync('is_logged_in');

    const isValid = !!(token && userInfo && isLoggedIn);

    console.log('🔍 登录状态检查:', isValid ? '已登录' : '未登录');

    return isValid;

  } catch (error) {
    console.error('❌ 检查登录状态失败:', error);
    return false;
  }
}

// 智能自动登录功能已移除

/**
 * 清除登录信息
 */
export const clearLoginInfo = () => {
  try {
    console.log('🧹 清除登录信息');
    
    // 清除所有token
    uni.removeStorageSync('auth_token');
    uni.removeStorageSync('session_key');
    
    // 清除用户信息
    uni.removeStorageSync('userInfo');
    uni.removeStorageSync('user_info');
    
    // 清除登录状态
    uni.removeStorageSync('auth');
    uni.removeStorageSync('is_logged_in');
    
    // 清除其他相关信息
    uni.removeStorageSync('auth_response');
    uni.removeStorageSync('login_time');
    
    console.log('✅ 登录信息清除完成');
    
  } catch (error) {
    console.error('❌ 清除登录信息失败:', error);
  }
}

/**
 * 获取当前用户信息
 * @returns {Object|null} 用户信息
 */
export const getCurrentUser = () => {
  try {
    const userInfo = uni.getStorageSync('userInfo');
    return userInfo || null;
  } catch (error) {
    console.error('❌ 获取用户信息失败:', error);
    return null;
  }
}

/**
 * 获取当前token
 * @returns {string|null} token
 */
export const getCurrentToken = () => {
  try {
    return uni.getStorageSync('auth_token') || uni.getStorageSync('session_key') || null;
  } catch (error) {
    console.error('❌ 获取token失败:', error);
    return null;
  }
}

// 默认导出
export default {
  handleWechatLogin,
  saveAuthInfo,
  updateUserState,
  checkLoginStatus,
  clearLoginInfo,
  getCurrentUser,
  getCurrentToken
}
