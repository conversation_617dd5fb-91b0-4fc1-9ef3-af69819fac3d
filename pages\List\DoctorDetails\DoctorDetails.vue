<template>
	<!--
		👨‍⚕️ 医生详情页面模板
		这是显示医生详细信息的页面，包含医生介绍、专长、评价等
		采用了绿色头部+白色内容的设计，支持滚动时的视觉效果
	-->

	<!--
		📄 医生详情页面容器
		- class="doctor-detail-container": 基础页面样式
		- :class: 动态样式类，包括字体大小和字体类型
		- :style: 动态样式，主要用于RTL布局
		- :key: 强制重新渲染的键，当字体或语言变化时重新渲染
	-->
	<view class="doctor-detail-container" :class="[fontSizeClass, fontClass]" :style="pageStyle" :key="`${fontSizeUpdateKey}-${i18nUpdateKey}`">
		<!--
			🟢 绿色顶部区域
			这是页面的头部区域，采用绿色背景设计
			包含状态栏、导航栏、医生头像等元素
		-->
		<view class="green-header">
			<!--
				📱 状态栏占位区域
				用于适配不同设备的状态栏高度，确保内容不被状态栏遮挡
				:style: 动态设置高度，根据设备的状态栏高度调整
			-->
			<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

			<!--
				🧭 自定义导航栏
				因为使用了自定义的绿色头部，所以需要自定义导航栏
			-->
			<view class="navbar">
				<!--
					⬅️ 导航栏左侧 - 返回按钮
					@tap: 点击时返回上一页
				-->
				<view class="navbar-left" @tap="goBack">
					<!-- 返回图标，使用FirstUI的back图标 -->
					<fui-icon name="back"></fui-icon>
				</view>

				<!-- 📝 导航栏中间 - 页面标题 -->
				<view class="navbar-center">
					<!--
						页面标题
						$t('doctorDetails.title'): 从语言包获取"医生详情"文字
					-->
					<text class="navbar-title">{{ $t('doctorDetails.title') }}</text>
				</view>

				<!-- 导航栏右侧 - 占位符，保持布局平衡 -->
				<view class="navbar-right">
					<!-- 占位，保持布局平衡 -->
				</view>
			</view>

			<!--
				👤 医生头像区域
				紧贴导航栏显示，包含医生头像和资历标签
				:style: 动态样式，支持滚动时的透明度和位移动画效果
				- opacity: 控制透明度，滚动时会逐渐变透明
				- transform: 控制位移，滚动时会向上移动
			-->
			<view class="doctor-avatar-section" :style="{
					opacity: avatarOpacity,
					transform: `translateY(${avatarTranslateY}px)`
				}">
				<!--
					⭕ 圆形头像容器
					白色边框突出显示，让头像更加醒目
				-->
				<view class="avatar-container">
					<!--
						医生头像图片
						:src: 当前医生的头像图片路径
						mode="aspectFill": 图片填充模式，保持宽高比并填充整个容器
					-->
					<image :src="processAvatarUrl(currentDoctor.avatar)" class="doctor-avatar" mode="aspectFill"></image>
				</view>

				<!--
					🏷️ 医生资历标签
					显示医生的经验年限和职称信息
				-->
				<view class="doctor-badge">
					<!--
						经验年限
						currentDoctor.consultations: 医生的咨询次数或经验年限
						$t('list.years'): 从语言包获取"年"字
					-->
					<text class="experience">{{ currentDoctor.consultations }} {{ $t('list.years') }}</text>
					<!-- 分隔符 -->
					<text class="badge-separator">•</text>
					<!--
						医生标识
						$t('doctorDetails.doctorBadge'): 从语言包获取医生标识文字
					-->
					<text class="badge-text">{{ $t('doctorDetails.doctorBadge') }}</text>
				</view>
			</view>
		</view>

		<!--
			📜 滑动内容区域
			这是页面的主要内容区域，支持垂直滚动
			滑动时会产生覆盖绿色头部的视觉效果
			- scroll-y="true": 启用垂直滚动
			- @scroll: 滚动时的回调函数，用于实现滚动动画效果
			- :scroll-top: 控制滚动位置
		-->
		<scroll-view class="content-scroll" scroll-y="true" @scroll="onScroll" :scroll-top="scrollTop">
			<!--
				📏 顶部间距
				初始状态下显示绿色头部，这个间距确保内容不会立即覆盖头部
			-->
			<view class="content-spacer"></view>

			<!--
				⬜ 白色内容容器
				滑动时会覆盖绿色头部，创造层次感的视觉效果
			-->
			<view class="content-container">
				<!-- 🆕 加载状态显示 -->
				<view v-if="loading" class="loading-container">
					<view class="loading-spinner"></view>
					<text class="loading-text">{{ $t('common.loading') || '加载中...' }}</text>
				</view>

				<!-- 🆕 错误状态显示 -->
				<view v-else-if="loadingError" class="error-container">
					<text class="error-text">{{ loadingError }}</text>
					<view class="retry-btn" @tap="fetchDoctorDetail(currentDoctor.id)">
						<text class="retry-text">{{ $t('common.retry') || '重试' }}</text>
					</view>
				</view>

				<!-- 正常内容显示 -->
				<view v-else>
					<!--
						📋 医生基本信息卡片
						显示医生的详细信息，如姓名、科室、专长等
					-->
					<view class="doctor-info-card">
					<!-- 👨‍⚕️ 医生姓名和操作按钮行 -->
					<view class="doctor-name-row">
						<text class="doctor-name">{{ currentDoctor.name }}</text>
						<view class="action-buttons">
							<view
								v-for="button in actionButtons"
								:key="button.key"
								class="action-btn"
								:class="[button.btnClass, { [button.activeClass]: button.isActive, 'loading': button.loading }]"
								@tap="button.action"
							>
								<!-- 🆕 加载状态显示 -->
								<view v-if="button.loading" class="loading-spinner-small"></view>
								<!-- 正常图标显示 -->
								<fui-icon
									v-else
									:name="button.iconName"
									:size="60"
									:color="button.iconColor"
								></fui-icon>
							</view>
						</view>
					</view>
					<text class="doctor-department">{{ currentDoctor.department }}</text>

					<!-- 评分和信息行 -->
					<view class="info-row">
						<view class="rating-section">
							<text v-for="i in 5" :key="i" class="star"
								:class="{ 'filled': i <= Math.floor(currentDoctor.rating) }">★</text>
							<text class="rating-score">{{ currentDoctor.rating }}</text>
						</view>
						<view class="like-section">
							<text class="like-icon">👍</text>
							<text class="like-text">{{ likeCount }}</text>
						</view>
						<!-- 收藏行 -->
						<view class="favorite-section">
							<text class="favorite-icon">♡</text>
							<text class="favorite-text">{{ favoriteCount }}</text>
						</view>
					</view>
					<view class="favorite-row">
						<!-- 咨询按钮 -->
						<view class="consult-button" @tap="consultDoctor">
							<text class="consult-text">{{ $t('doctorDetails.consultDoctor') }}</text>
						</view>
					</view>
				</view>

				<!-- 医生详情卡片 -->
				<view class="detail-card">
					<text class="section-title">{{ $t('doctorDetails.doctorDetail') }}</text>
					<!-- 🆕 医生描述 - 只显示后端真实数据 -->
					<text
						v-if="currentDoctor.description && currentDoctor.description.trim()"
						class="detail-content"
					>{{ currentDoctor.description }}</text>
					<!-- 🆕 无描述时的提示 -->
					<text
						v-else
						class="detail-content no-description"
					>{{ $t('doctorDetails.noDescription') || '暂无医生详细介绍' }}</text>
				</view>

				<!-- 联系信息循环 -->
				<view
					v-for="contactInfo in contactInfoItems"
					:key="contactInfo.key"
					:class="contactInfo.sectionClass"
				>
					<view :class="contactInfo.itemClass">
						<view :class="contactInfo.iconContainerClass">
							<text :class="contactInfo.iconClass">{{ contactInfo.icon }}</text>
						</view>
						<view :class="contactInfo.contentClass">
							<text :class="contactInfo.labelClass">{{ $t(contactInfo.labelKey) }}</text>
							<text :class="contactInfo.valueClass">{{ contactInfo.value }}</text>
						</view>
						<view :class="contactInfo.actionClass" @tap="contactInfo.action">
							<text class="action-text">{{ $t(contactInfo.actionTextKey) }}</text>
						</view>
					</view>
				</view>

				<!-- 擅长领域 -->
				<view class="specialty-section">
					<text class="section-title">{{ $t('doctorDetails.specialtyField') }}</text>
					<!-- 🆕 动态专科标签 - 从后端specialties字段获取 -->
					<view class="specialty-tags" v-if="doctorSpecialties.length > 0">
						<view
							class="specialty-tag"
							v-for="(specialty, index) in doctorSpecialties"
							:key="index"
						>
						
						
						
						
							{{ specialty }}
						</view>
					</view>
					<!-- 🆕 无专科数据时的提示 -->
					<view class="no-specialty" v-else>
						<text class="no-specialty-text">{{ $t('doctorDetails.noSpecialtyInfo') || '暂无专科信息' }}</text>
					</view>
				</view>

				<!-- 医生推荐 -->
				<view class="recommend-section">
					<text class="section-title">{{ $t('doctorDetails.doctorRecommend') }}</text>
					<view class="recommend-products">
						<view class="product-card" v-for="product in recommendedProducts" :key="product.id">
							<image
								:src="product.image"
								class="product-image"
								mode="aspectFill"
								@error="handleProductImageError($event, product.id)"
								@load="console.log('🖼️ 产品图片加载成功:', product.name)"
							></image>
							<view class="product-info">
								<text class="product-name">{{ product.name }}</text>
								<text class="product-desc">{{ product.description }}</text>
								<view class="product-price">
									<text class="price-symbol">¥</text>
									<text class="price-value">{{ product.price }}</text>
									<text class="original-price">¥{{ product.originalPrice }}</text>
								</view>
							</view>
							<view class="product-btn" @tap="goToProductDetail(product)">
								<text class="btn-text">{{ $t('doctorDetails.viewDetail') }}</text>
							</view>
						</view>
					</view>
				</view>
				</view> <!-- 🆕 关闭正常内容的view -->
			</view>

			<!-- 底部安全区域 -->
			<view class="safe-area-bottom" :style="{ height: safeAreaBottom + 'px' }"></view>
		</scroll-view>
	</view>
</template>

<script setup>
	import {ref,computed,onMounted,onUnmounted} from 'vue'
	import {onLoad,onShow} from '@dcloudio/uni-app'
	import {useAppStore} from '@/store/app.js'
	import { useFontSizePage } from '@/utils/fontSizeMixin.js'
import { useRTLPage } from '@/utils/rtlMixin.js'
import { t } from '@/locale/index.js'
import { processAvatarUrl, processProductImageUrl } from '@/request/avatar.js'
// 🆕 导入医生API和产品API
import { doctorApi, productApi } from '@/request/index.js'

	// 使用全局应用状态
	const appStore = useAppStore()
	const themeUpdateKey = ref(0)

	// 使用字体大小功能
	const { fontSizeClass, fontSizeUpdateKey } = useFontSizePage()

	// RTL布局支持
	const { pageClass, pageStyle } = useRTLPage()

	// 国际化支持
	const currentLanguage = ref(appStore.lang)
	const i18nUpdateKey = ref(0)

	// 计算字体类（合并RTL类）
	const fontClass = computed(() => ({
		...pageClass.value,
		'ug': appStore.isUyghur,
		[`lang-${appStore.lang}`]: true
	}))

	// 国际化翻译函数
	const $t = (key) => t(key)

	// 系统信息
	const statusBarHeight = ref(44)
	const safeAreaBottom = ref(34)

	// 滚动状态
	const scrollTop = ref(0)
	const isScrolled = ref(false)

	// 头像动态效果
	const avatarOpacity = ref(1)
	const avatarTranslateY = ref(0)

	// 监听主题变化
	uni.$on('themeChanged', (data) => {
		console.log('DoctorDetails页面接收到主题变化:', data.isDark ? '暗色模式' : '浅色模式')
		themeUpdateKey.value++
	})

	// 当前医生信息
	const currentDoctor = ref({})

	// 🆕 加载状态
	const loading = ref(false)
	const loadingError = ref('')

	// 🆕 点赞和收藏状态
	const likedDoctors = ref([])
	const favoriteDoctors = ref([])
	const isLiked = ref(false) // 🆕 改为ref，用于API状态管理
	const isFavorited = ref(false) // 🆕 改为ref，用于API状态管理
	const likeLoading = ref(false) // 🆕 点赞加载状态
	const favoriteLoading = ref(false) // 🆕 收藏加载状态

	// 🆕 点赞数和收藏数
	const likeCount = ref(0) // 点赞数
	const favoriteCount = ref(0) // 收藏数

	// 🆕 医生专科信息
	const doctorSpecialties = ref([]) // 专科标签数组

	// 操作按钮配置数组
	const actionButtons = computed(() => [
		{
			key: 'like',
			btnClass: 'like-btn',
			activeClass: 'liked',
			iconName: 'fabulous',
			iconColor: isLiked.value ? '#FFD700' : '#109b57',
			isActive: isLiked.value,
			loading: likeLoading.value,
			action: likeDoctor
		},
		{
			key: 'favorite',
			btnClass: 'favorite-btn',
			activeClass: 'favorited',
			iconName: 'like',
			iconColor: isFavorited.value ? '#FF4757' : '#109b57',
			isActive: isFavorited.value,
			loading: favoriteLoading.value,
			action: favoriteDoctor
		}
	])

	// 🆕 推荐产品数据
	const recommendedProducts = ref([])

	// 🆕 API调用方法 - 获取医生详情
	const fetchDoctorDetail = async (doctorId) => {
		try {
			loading.value = true
			loadingError.value = ''
			console.log('🏥 开始获取医生详情，ID:', doctorId)

			// 调用医生详情API
			const response = await doctorApi.getDoctorDetail(doctorId)
			console.log('✅ 医生详情API响应:', response)

			// 检查响应格式
			let doctorData = null
			if (response && response.code === 200 && response.data) {
				// 标准格式 {code: 200, data: {...}}
				doctorData = response.data
			} else if (response && response.id) {
				// 直接返回医生对象
				doctorData = response
			} else {
				throw new Error('API返回数据格式错误')
			}

			// 🖼️ 处理头像URL
			let avatarUrl = '/static/icon/user.svg' // 默认头像
			const originalAvatarUrl = doctorData.avatar_url || doctorData.avatar || doctorData.avatarUrl
			if (originalAvatarUrl) {
				try {
					avatarUrl = processAvatarUrl(originalAvatarUrl)
					console.log('🖼️ 处理医生头像URL:', doctorData.name, originalAvatarUrl, '->', avatarUrl)
				} catch (error) {
					console.warn('⚠️ 头像URL处理失败:', originalAvatarUrl, error)
				}
			}

			// 🆕 处理专科信息 - 优先使用specialties字段
			let department = ''
			let specialtiesArray = []

			console.log('🔍 处理专科数据，原始specialties:', doctorData.specialties)

			if (doctorData.specialties) {
				// 如果specialties是数组，处理专科标签
				if (Array.isArray(doctorData.specialties)) {
					specialtiesArray = doctorData.specialties.filter(item => item && item.trim()) // 过滤空值
					department = specialtiesArray.length > 0 ? specialtiesArray[0] : ''
				} else {
					// 如果是字符串，分割成数组或直接使用
					const specialtyStr = doctorData.specialties.toString().trim()
					if (specialtyStr) {
						// 尝试按逗号分割，如果没有逗号就作为单个专科
						specialtiesArray = specialtyStr.includes(',')
							? specialtyStr.split(',').map(s => s.trim()).filter(s => s)
							: [specialtyStr]
						department = specialtiesArray[0]
					}
				}
			} else if (doctorData.specialty) {
				department = doctorData.specialty
				specialtiesArray = [doctorData.specialty]
			} else if (doctorData.department) {
				department = doctorData.department
				specialtiesArray = [doctorData.department]
			}

			// 🆕 更新专科标签数组
			doctorSpecialties.value = specialtiesArray
			console.log('📋 处理后的专科标签:', specialtiesArray)

			// 处理医生数据
			currentDoctor.value = {
				id: doctorData.id,
				name: doctorData.name,
				department: department || '', // 🆕 使用处理后的专科信息，无数据时显示空
				avatar: avatarUrl,
				consultations: doctorData.years_of_experience || doctorData.experience || 0,
				rating: doctorData.rating || 0, // 🆕 无数据时显示0
				badge: doctorData.badge || '', // 🆕 无数据时显示空
				phone: doctorData.phone || null, // 🆕 无数据时为null，用于条件显示
				address: doctorData.address || null, // 🆕 无数据时为null，用于条件显示
				description: doctorData.detailed_info || doctorData.description || '', // 🆕 无数据时显示空
				// 保留原始数据用于其他用途
				originalData: doctorData
			}

			console.log('✅ 医生详情数据更新完成:', currentDoctor.value.name)

			// 🆕 获取医生推荐的产品
			await fetchDoctorProducts(doctorId)

			// 🆕 获取医生状态（点赞、收藏状态和数量）
			await fetchDoctorStatus(doctorId)

		} catch (error) {
			console.error('❌ 获取医生详情失败:', error)
			loadingError.value = '获取医生信息失败'

			// 显示错误提示
			uni.showToast({
				title: $t('error.dataLoadFailed') || '数据加载失败',
				icon: 'none'
			})

			// 🆕 设置空的医生信息，避免页面崩溃 - 完全不使用假数据
			currentDoctor.value = {
				id: doctorId,
				name: '', // 🆕 不显示假的医生名称
				department: '', // 🆕 不显示假的专科信息
				avatar: '/static/icon/user.svg', // 只保留默认头像
				consultations: 0,
				rating: 0, // 🆕 显示0而不是4.5
				badge: '', // 🆕 不显示假的徽章
				phone: null, // 🆕 错误时也不显示假数据
				address: null, // 🆕 错误时也不显示假数据
				description: '' // 🆕 不显示假的描述
			}
		} finally {
			loading.value = false
		}
	}

	// 🆕 API调用方法 - 获取医生推荐的产品
	const fetchDoctorProducts = async (doctorId) => {
		try {
			console.log('🛒 开始获取医生推荐产品，医生ID:', doctorId)

			// 🆕 调用专门的医生产品接口
			const response = await productApi.getDoctorProducts(doctorId, {
				page: 1,
				page_size: 10
			})

			console.log('✅ 医生产品API响应:', response)

			// 处理产品数据
			let products = []
			if (response && response.code === 200 && response.data) {
				if (Array.isArray(response.data.products)) {
					products = response.data.products
				} else if (Array.isArray(response.data)) {
					products = response.data
				}
			} else if (Array.isArray(response)) {
				products = response
			}

			// 转换产品数据格式
			recommendedProducts.value = products.slice(0, 2).map(product => {
				// 🖼️ 处理产品图片URL
				let productImageUrl = '/static/icon/user.svg' // 默认图片
				const originalImageUrl = product.main_image_url || product.image || product.imageUrl
				if (originalImageUrl) {
					try {
						productImageUrl = processProductImageUrl(originalImageUrl)
						console.log('🖼️ 处理产品图片URL:', product.name, originalImageUrl, '->', productImageUrl)
					} catch (error) {
						console.warn('⚠️ 产品图片URL处理失败:', originalImageUrl, error)
					}
				}

				return {
					id: product.id,
					name: product.name,
					description: product.description || product.detailed_description || '', // 🆕 无数据时显示空
					price: parseFloat(product.price || 0).toFixed(2),
					originalPrice: parseFloat(product.original_price || product.price || 0).toFixed(2),
					unit: product.unit || '', // 🆕 使用真实单位，无数据时显示空
					image: productImageUrl,
					category: product.category || '',
					manufacturer: product.manufacturer || '', // 🆕 无数据时显示空，不显示"未知厂商"
					doctor: currentDoctor.value?.name || '', // 🆕 无数据时显示空，不显示"专业医生"
					appointmentTime: product.appointment_time || '', // 🆕 使用真实预约时间，无数据时显示空
					detailDescription: product.detailed_description || product.description || '' // 🆕 无数据时显示空
				}
			})

			console.log('✅ 医生推荐产品数据更新完成，数量:', recommendedProducts.value.length)

		} catch (error) {
			console.error('❌ 获取医生推荐产品失败:', error)
			// 使用默认产品数据，避免页面空白
			recommendedProducts.value = []
		}
	}

	// 加载点赞和收藏状态
	const loadLikeAndFavoriteStatus = () => {
		likedDoctors.value = uni.getStorageSync('likedDoctors') || []
		favoriteDoctors.value = uni.getStorageSync('favoriteDoctors') || []
	}

	// 🆕 图片加载错误处理
	const handleProductImageError = (e, productId) => {
		console.warn('🖼️ 产品图片加载失败:', e.detail?.errMsg || e)

		// 找到对应的产品并更新其图片为默认图片
		const productIndex = recommendedProducts.value.findIndex(p => p.id === productId)
		if (productIndex !== -1) {
			recommendedProducts.value[productIndex].image = '/static/icon/user.svg'
			console.log(`✅ 已将产品 ${recommendedProducts.value[productIndex].name} 的图片更新为默认图片`)
		}
	}

	// 🆕 使用onLoad生命周期接收页面参数并调用API
	onLoad(async (options) => {
		console.log('📄 医生详情页面参数:', options)
		const doctorId = options.doctorId

		if (doctorId) {
			// 🆕 调用API获取医生详情
			await fetchDoctorDetail(doctorId)
		} else {
			console.error('❌ 未获取到医生ID参数')
			uni.showToast({
				title: '参数错误',
				icon: 'none'
			})
			// 延迟返回上一页
			setTimeout(() => {
				uni.navigateBack()
			}, 1500)
		}

		// 加载点赞和收藏状态
		loadLikeAndFavoriteStatus()
	})

	// 页面显示时刷新状态
	onShow(() => {
		loadLikeAndFavoriteStatus()
	})

	// 获取系统信息
	const getSystemInfo = () => {
		uni.getSystemInfo({
			success: (res) => {
				statusBarHeight.value = res.statusBarHeight || 44
				safeAreaBottom.value = res.safeAreaInsets?.bottom || 34
				console.log('系统信息:', res)
			}
		})
	}

	// 方法
	const goBack = () => {
		uni.navigateBack()
	}

	// 🆕 点赞医生 - 使用真实API接口
	const likeDoctor = async () => {
		if (!currentDoctor.value.id) {
			console.error('❌ 医生ID不存在')
			return
		}

		if (likeLoading.value) {
			console.log('⏳ 点赞操作进行中，请稍候...')
			return
		}

		try {
			likeLoading.value = true
			const doctorId = currentDoctor.value.id

			console.log('👍 开始点赞操作，医生ID:', doctorId, '当前状态:', isLiked.value)

			if (isLiked.value) {
				// 🆕 取消点赞
				console.log('🔄 调用取消点赞API...')
				const response = await doctorApi.unlikeDoctorApi(doctorId)
				console.log('✅ 取消点赞API响应:', response)

				// 更新状态
				isLiked.value = false
				// 🆕 实时更新点赞数（减1）
				likeCount.value = Math.max(0, likeCount.value - 1)

				// 显示成功提示
				uni.showToast({
					title: $t('doctorDetails.unliked') || '已取消点赞',
					icon: 'none'
				})

				console.log('✅ 取消点赞成功，当前点赞数:', likeCount.value)
			} else {
				// 🆕 点赞
				console.log('🔄 调用点赞API...')
				const response = await doctorApi.likeDoctorApi(doctorId)
				console.log('✅ 点赞API响应:', response)

				// 更新状态
				isLiked.value = true
				// 🆕 实时更新点赞数（加1）
				likeCount.value = likeCount.value + 1

				// 显示成功提示
				uni.showToast({
					title: $t('doctorDetails.liked') || '点赞成功',
					icon: 'success'
				})

				console.log('✅ 点赞成功，当前点赞数:', likeCount.value)
			}

			// 🆕 同步更新本地存储（用于离线状态显示）
			await updateLocalLikeStatus()

			// 通知其他页面刷新数据
			uni.$emit('refreshLikedList')

		} catch (error) {
			console.error('❌ 点赞操作失败:', error)

			// 根据错误类型显示不同提示
			let errorMessage = '操作失败'
			if (error.code === 400) {
				errorMessage = isLiked.value ? '已点赞' : '未点赞'
			} else if (error.code === 401) {
				errorMessage = '请先登录'
			} else if (error.message) {
				errorMessage = error.message
			}

			uni.showToast({
				title: errorMessage,
				icon: 'none'
			})
		} finally {
			likeLoading.value = false
		}
	}

	// 🆕 更新本地点赞状态（用于离线显示）
	const updateLocalLikeStatus = async () => {
		try {
			// 获取当前已点赞的医生列表
			let currentLikedDoctors = uni.getStorageSync('likedDoctors') || []

			if (isLiked.value) {
				// 添加到点赞列表
				const isAlreadyInLocal = currentLikedDoctors.some(doctor => doctor.id === currentDoctor.value.id)
				if (!isAlreadyInLocal) {
					const doctorData = {
						id: currentDoctor.value.id,
						name: currentDoctor.value.name,
						specialty: currentDoctor.value.department,
						description: currentDoctor.value.description,
						avatar: currentDoctor.value.avatar,
						likedAt: new Date().toISOString()
					}
					currentLikedDoctors.push(doctorData)
				}
			} else {
				// 从点赞列表中移除
				currentLikedDoctors = currentLikedDoctors.filter(doctor => doctor.id !== currentDoctor.value.id)
			}

			// 保存到本地存储
			uni.setStorageSync('likedDoctors', currentLikedDoctors)
			likedDoctors.value = currentLikedDoctors

			console.log('✅ 本地点赞状态已更新')
		} catch (error) {
			console.error('❌ 更新本地点赞状态失败:', error)
		}
	}

	// 🆕 更新本地交互状态（点赞和收藏）
	const updateLocalInteractionStatus = async () => {
		try {
			// 更新点赞本地存储
			let currentLikedDoctors = uni.getStorageSync('likedDoctors') || []
			if (isLiked.value) {
				const isAlreadyInLocal = currentLikedDoctors.some(doctor => doctor.id === currentDoctor.value.id)
				if (!isAlreadyInLocal) {
					const doctorData = {
						id: currentDoctor.value.id,
						name: currentDoctor.value.name,
						specialty: currentDoctor.value.department,
						description: currentDoctor.value.description,
						avatar: currentDoctor.value.avatar,
						likedAt: new Date().toISOString()
					}
					currentLikedDoctors.push(doctorData)
				}
			} else {
				currentLikedDoctors = currentLikedDoctors.filter(doctor => doctor.id !== currentDoctor.value.id)
			}
			uni.setStorageSync('likedDoctors', currentLikedDoctors)
			likedDoctors.value = currentLikedDoctors

			// 更新收藏本地存储
			let currentFavoriteDoctors = uni.getStorageSync('favoriteDoctors') || []
			if (isFavorited.value) {
				const isAlreadyInLocal = currentFavoriteDoctors.some(doctor => doctor.id === currentDoctor.value.id)
				if (!isAlreadyInLocal) {
					const doctorData = {
						id: currentDoctor.value.id,
						name: currentDoctor.value.name,
						specialty: currentDoctor.value.department,
						description: currentDoctor.value.description,
						avatar: currentDoctor.value.avatar,
						favoritedAt: new Date().toISOString()
					}
					currentFavoriteDoctors.push(doctorData)
				}
			} else {
				currentFavoriteDoctors = currentFavoriteDoctors.filter(doctor => doctor.id !== currentDoctor.value.id)
			}
			uni.setStorageSync('favoriteDoctors', currentFavoriteDoctors)
			favoriteDoctors.value = currentFavoriteDoctors

			console.log('✅ 本地交互状态已更新')
		} catch (error) {
			console.error('❌ 更新本地交互状态失败:', error)
		}
	}

	// 🆕 获取医生状态（点赞、收藏状态和数量）
	const fetchDoctorStatus = async (doctorId) => {
		try {
			console.log('🔍 开始获取医生状态，医生ID:', doctorId)

			// 🆕 调用医生状态接口
			const response = await doctorApi.getDoctorStatus(doctorId)
			console.log('✅ 医生状态API响应:', response)

			// 处理响应数据
			let statusData = null
			if (response && response.code === 200 && response.data) {
				statusData = response.data
			} else if (response && (response.is_liked !== undefined || response.is_favorited !== undefined)) {
				statusData = response
			} else {
				throw new Error('API返回数据格式错误')
			}

			// 🆕 更新点赞和收藏状态
			isLiked.value = statusData.is_liked || false
			isFavorited.value = statusData.is_favorited || false

			// 🆕 更新点赞数和收藏数
			likeCount.value = statusData.like_count || statusData.likes_count || 0
			favoriteCount.value = statusData.favorite_count || statusData.favorites_count || 0

			console.log('✅ 医生状态获取完成:')
			console.log('   - 点赞状态:', isLiked.value ? '已点赞' : '未点赞')
			console.log('   - 收藏状态:', isFavorited.value ? '已收藏' : '未收藏')
			console.log('   - 点赞数:', likeCount.value)
			console.log('   - 收藏数:', favoriteCount.value)

			// 🆕 同步更新本地存储（用于离线显示）
			await updateLocalInteractionStatus()

		} catch (error) {
			console.error('❌ 获取医生状态失败:', error)

			// 如果API调用失败，尝试从本地存储获取状态
			try {
				const localLikedDoctors = uni.getStorageSync('likedDoctors') || []
				const localFavoriteDoctors = uni.getStorageSync('favoriteDoctors') || []

				const isLocalLiked = localLikedDoctors.some(doctor => doctor.id == doctorId)
				const isLocalFavorited = localFavoriteDoctors.some(doctor => doctor.id == doctorId)

				isLiked.value = isLocalLiked
				isFavorited.value = isLocalFavorited

				// 本地存储没有数量信息，使用默认值
				likeCount.value = 0
				favoriteCount.value = 0

				console.log('📱 使用本地存储的状态:')
				console.log('   - 点赞状态:', isLocalLiked ? '已点赞' : '未点赞')
				console.log('   - 收藏状态:', isLocalFavorited ? '已收藏' : '未收藏')
			} catch (localError) {
				console.error('❌ 获取本地状态也失败:', localError)
				isLiked.value = false
				isFavorited.value = false
				likeCount.value = 0
				favoriteCount.value = 0
			}
		}
	}

	// 🆕 收藏医生 - 使用真实API接口
	const favoriteDoctor = async () => {
		if (!currentDoctor.value.id) {
			console.error('❌ 医生ID不存在')
			return
		}

		if (favoriteLoading.value) {
			console.log('⏳ 收藏操作进行中，请稍候...')
			return
		}

		try {
			favoriteLoading.value = true
			const doctorId = currentDoctor.value.id

			console.log('⭐ 开始收藏操作，医生ID:', doctorId, '当前状态:', isFavorited.value)

			if (isFavorited.value) {
				// 🆕 取消收藏
				console.log('🔄 调用取消收藏API...')
				const response = await doctorApi.unfavoriteDoctorApi(doctorId)
				console.log('✅ 取消收藏API响应:', response)

				// 更新状态
				isFavorited.value = false
				// 🆕 实时更新收藏数（减1）
				favoriteCount.value = Math.max(0, favoriteCount.value - 1)

				// 显示成功提示
				uni.showToast({
					title: $t('doctorDetails.unfavorited') || '已取消收藏',
					icon: 'none'
				})

				console.log('✅ 取消收藏成功，当前收藏数:', favoriteCount.value)
			} else {
				// 🆕 收藏
				console.log('🔄 调用收藏API...')
				const response = await doctorApi.favoriteDoctorApi(doctorId)
				console.log('✅ 收藏API响应:', response)

				// 更新状态
				isFavorited.value = true
				// 🆕 实时更新收藏数（加1）
				favoriteCount.value = favoriteCount.value + 1

				// 显示成功提示
				uni.showToast({
					title: $t('doctorDetails.favorited') || '收藏成功',
					icon: 'success'
				})

				console.log('✅ 收藏成功，当前收藏数:', favoriteCount.value)
			}

			// 🆕 同步更新本地存储（用于离线状态显示）
			await updateLocalInteractionStatus()

			// 通知其他页面刷新数据
			uni.$emit('refreshFavoriteList')

		} catch (error) {
			console.error('❌ 收藏操作失败:', error)

			// 根据错误类型显示不同提示
			let errorMessage = '操作失败'
			if (error.code === 400) {
				errorMessage = isFavorited.value ? '已收藏' : '未收藏'
			} else if (error.code === 401) {
				errorMessage = '请先登录'
			} else if (error.message) {
				errorMessage = error.message
			}

			uni.showToast({
				title: errorMessage,
				icon: 'none'
			})
		} finally {
			favoriteLoading.value = false
		}
	}

	const shareDoctor = () => {
		console.log('分享医生')
		uni.showToast({
			title: '分享功能',
			icon: 'none'
		})
	}

	const consultDoctor = () => {
		console.log('咨询医生:', currentDoctor.value.name)

		// 将医生信息存储到本地存储，供首页使用
		uni.setStorageSync('selectedDoctor', {
			id: currentDoctor.value.id,
			name: currentDoctor.value.name,
			specialty: currentDoctor.value.specialty,
			avatar: currentDoctor.value.avatar,
			experience: currentDoctor.value.experience,
			rating: currentDoctor.value.rating,
			address: currentDoctor.value.address,
			phone: currentDoctor.value.phone
		})

		uni.switchTab({
			url: '/pages/index/index'
		})
	}

	const makePhoneCall = (phoneNumber) => {
		console.log('拨打电话:', phoneNumber)
		uni.makePhoneCall({
			phoneNumber: phoneNumber,
			success: () => {
				console.log('拨打电话成功')
			},
			fail: (err) => {
				console.error('拨打电话失败:', err)
				uni.showToast({
					title: $t('doctorDetails.callFailed'),
					icon: 'none'
				})
			}
		})
	}

	// 🆕 复制地址功能 - 只复制真实地址数据
	const copyAddress = () => {
		const address = currentDoctor.value.address
		if (address && address.trim()) {
			console.log('📋 复制地址:', address)
			uni.setClipboardData({
				data: address,
				success: () => {
					uni.showToast({
						title: $t('doctorDetails.addressCopied') || '地址已复制',
						icon: 'success'
					})
				},
				fail: (error) => {
					console.error('❌ 复制地址失败:', error)
					uni.showToast({
						title: '复制失败',
						icon: 'none'
					})
				}
			})
		} else {
			console.warn('⚠️ 没有可复制的地址数据')
			uni.showToast({
				title: '暂无地址信息',
				icon: 'none'
			})
		}
	}

	// 🆕 联系信息配置数组 - 只显示后端有数据的字段
	const contactInfoItems = computed(() => {
		const items = []

		// 🆕 只有当后端有phone数据时才添加电话信息
		if (currentDoctor.value.phone && currentDoctor.value.phone.trim()) {
			console.log('📞 显示电话信息:', currentDoctor.value.phone)
			items.push({
				key: 'phone',
				sectionClass: 'contact-section',
				itemClass: 'contact-item',
				iconContainerClass: 'contact-icon-container',
				iconClass: 'contact-icon',
				icon: '📞',
				contentClass: 'contact-content',
				labelClass: 'contact-label',
				labelKey: 'doctorDetails.contactPhone',
				valueClass: 'contact-value',
				value: currentDoctor.value.phone, // 🆕 直接使用真实数据，不使用默认值
				actionClass: 'contact-action',
				actionTextKey: 'doctorDetails.call',
				action: () => makePhoneCall(currentDoctor.value.phone)
			})
		} else {
			console.log('📞 后端无电话数据，不显示电话信息')
		}

		// 🆕 只有当后端有address数据时才添加地址信息
		if (currentDoctor.value.address && currentDoctor.value.address.trim()) {
			console.log('📍 显示地址信息:', currentDoctor.value.address)
			items.push({
				key: 'address',
				sectionClass: 'address-section',
				itemClass: 'address-item',
				iconContainerClass: 'address-icon-container',
				iconClass: 'address-icon',
				icon: '📍',
				contentClass: 'address-content',
				labelClass: 'address-label',
				labelKey: 'doctorDetails.workAddress',
				valueClass: 'address-value',
				value: currentDoctor.value.address, // 🆕 直接使用真实数据，不使用默认值
				actionClass: 'address-action',
				actionTextKey: 'doctorDetails.copy',
				action: copyAddress
			})
		} else {
			console.log('📍 后端无地址数据，不显示地址信息')
		}

		console.log('📋 最终显示的联系信息项目数量:', items.length)
		return items
	})



	// 跳转到产品详情页面
	const goToProductDetail = (product) => {
		console.log('查看产品详情:', product)
		// 跳转到ProductDetails页面并传递产品信息
		const productData = encodeURIComponent(JSON.stringify(product))
		uni.navigateTo({
			url: `/pages/List/ProductDetails/ProductDetails?productData=${productData}`
		})
	}

	const buyProduct = () => {
		console.log('查看产品详情')
		uni.showToast({
			title: '产品详情',
			icon: 'none'
		})
	}

	// 滚动事件处理 - 实现头像动态透明度和位置效果
	const onScroll = (e) => {
		const currentScrollTop = e.detail.scrollTop
		scrollTop.value = currentScrollTop

		// 定义关键滚动距离 - 根据图片效果调整
		const startFadeDistance = 30 // 开始淡出的滚动距离（更早开始）
		const endFadeDistance = 120 // 完全消失的滚动距离
		const maxMoveDistance = 100 // 最大向上移动距离（移动到状态栏附近）

		// 计算透明度 (1 -> 0)
		if (currentScrollTop <= startFadeDistance) {
			avatarOpacity.value = 1
		} else if (currentScrollTop >= endFadeDistance) {
			avatarOpacity.value = 0
		} else {
			// 线性插值计算透明度
			const fadeProgress = (currentScrollTop - startFadeDistance) / (endFadeDistance - startFadeDistance)
			avatarOpacity.value = 1 - fadeProgress
		}

		// 计算向上移动距离 (0 -> maxMoveDistance)
		if (currentScrollTop <= startFadeDistance) {
			avatarTranslateY.value = 0
		} else if (currentScrollTop >= endFadeDistance) {
			avatarTranslateY.value = -maxMoveDistance
		} else {
			// 线性插值计算移动距离
			const moveProgress = (currentScrollTop - startFadeDistance) / (endFadeDistance - startFadeDistance)
			avatarTranslateY.value = -moveProgress * maxMoveDistance
		}

		// 更新滚动状态
		isScrolled.value = currentScrollTop > 50

		console.log('滚动:', currentScrollTop, '透明度:', avatarOpacity.value.toFixed(2), '移动:', avatarTranslateY.value
			.toFixed(1))
	}

	// 生命周期
	onMounted(() => {
		getSystemInfo()
	})

	onUnmounted(() => {
		uni.$off('themeChanged')
	})
</script>

<style lang="scss" scoped>
	/* ========== 页面容器 ========== */
	.doctor-detail-container {
		width: 100%;
		height: 100vh;
		background-color: #f5f5f5;
		position: relative;
	}

	/* ========== 绿色顶部区域 - 固定头部，会被白色内容覆盖 ========== */
	.green-header {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		background-color: #109d58;
		z-index: 100;
		/* 低于滑动内容的z-index */
		// padding-bottom: 80rpx;
	}

	/* 状态栏占位 */
	.status-bar {
		width: 100%;
		background-color: transparent;
		
	}

	/* 导航栏 - 与微信胶囊按钮对齐 */
	.navbar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 32rpx;
		height: 88rpx;
		position: relative;
		
	}

	.navbar-left {
		width: 88rpx;
		height: 64rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: rgba(255, 255, 255, 0.2);
		border-radius: 32rpx;
		backdrop-filter: blur(10px);
	}

	.back-icon {
		font-size: 36rpx;
		color: #ffffff;
		font-weight: 400;
	}

	.navbar-center {
		// position: absolute;
		// left: 30%;
		transform: translateX(-50%);
		text-align: center;
		justify-content: center;
		align-items: center;
		margin-left: 200rpx;
	}

	.navbar-title {
		font-size: 34rpx;
		font-weight: 600;
		color: #ffffff;
	}

	.navbar-right {
		width: 88rpx;
		height: 64rpx;
		/* 占位，保持布局平衡 */
	}

	.nav-icon {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.icon {
		font-size: 40rpx;
		color: #ffffff;
	}

	/* 医生头像区域 - 支持动态透明度和位置变化，紧贴导航栏 */
	.doctor-avatar-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40rpx 0 80rpx; /* 调整上边距，为导航栏留出空间 */
		margin-top: 0; /* 重置边距 */
		/* 移除CSS过渡，使用JavaScript控制实现更精确的动画 */
		/* transition: opacity 0.1s ease-out, transform 0.1s ease-out; */
		position: relative;
		z-index: 200;
		/* 确保在绿色背景之上 */
	}

	/* 头像容器 - 圆形头像，白色边框突出显示 */
	.avatar-container {
		width: 150rpx; /* 调整头像大小，参考图片比例 */
		height: 150rpx;
		border-radius: 50%;
		overflow: hidden;
		border: 6rpx solid rgba(255, 255, 255, 0.9); /* 增加边框厚度，更突出 */
		margin-bottom: 40rpx; /* 调整与标签的间距 */
		background-color: #ffffff;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
	}

	.doctor-avatar {
		width: 100%;
		height: 100%;
	}

	.doctor-badge {
		background-color: rgba(255, 255, 255, 0.95);
		padding: 12rpx 32rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		gap: 8rpx;
	}

	.experience {
		font-size: 28rpx;
		font-weight: 500;
		color: #4CAF50;
	}

	.badge-separator {
		font-size: 28rpx;
		color: #999999;
	}

	.badge-text {
		font-size: 28rpx;
		color: #666666;
	}

	/* ========== 滑动内容区域 - 实现覆盖效果 ========== */
	.content-scroll {
		position: fixed;
		top: 180rpx;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 500;
		background-color: transparent;
	}

	/* 顶部间距 - 初始状态显示完整绿色头部，调整为更紧凑的布局 */
	.content-spacer {
		height: calc(var(--status-bar-height) + 88rpx + 200rpx); /* 状态栏 + 导航栏 + 头像区域 */
		background-color: transparent;
	}

	/* 白色内容容器 - 滑动时覆盖绿色头部 */
	.content-container {
		background-color: #f5f5f5;
		min-height: calc(100vh + 200rpx);
		border-radius: 40rpx 40rpx 0 0;
		position: relative;
		z-index: 600;
		padding: 32rpx;
	}

	/* 医生基本信息卡片 - 带阴影的白色卡片 */
	.doctor-info-card {
		background-color: #ffffff;
		padding: 32rpx;
		border-radius: 24rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
		position: relative;
		z-index: 700;
	}

	/* 医生姓名和操作按钮行 */
	.doctor-name-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 8rpx;
		/* 确保有足够的空间分布 */
		width: 100%;
	}

	.doctor-name {
		font-size: 40rpx;
		font-weight: 600;
		color: #333333;
		flex: 1;
	}

	/* 操作按钮组 */
	.action-buttons {
		display: flex;
		gap: 16rpx;
	}

	.action-btn {
		width: 80rpx;
		height: 80rpx;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: all 0.3s ease;
	}

	/* 🆕 按钮加载状态 */
	.action-btn.loading {
		opacity: 0.7;
		pointer-events: none;
	}

	/* 🆕 小的加载动画 */
	.loading-spinner-small {
		width: 32rpx;
		height: 32rpx;
		border: 3rpx solid #f3f3f3;
		border-top: 3rpx solid #109d58;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	.like-btn {
		transition: all 0.3s ease;
	}

	.like-btn.liked {
		background-color: rgba(255, 215, 0, 0.1);
		transform: scale(1.1);
	}

	.favorite-btn {
		transition: all 0.3s ease;
	}

	.favorite-btn.favorited {
		background-color: rgba(255, 71, 87, 0.1);
		transform: scale(1.1);
	}

	.action-btn:active {
		transform: scale(0.95);
		opacity: 0.8;
	}

	.doctor-department {
		font-size: 28rpx;
		color: #666666;
		margin-bottom: 32rpx;
		display: block;
	}

	/* 评分和信息行 */
	.info-row {
		display: flex;
		align-items: center;
		margin-bottom: 24rpx;
		gap: 32rpx;
		flex-wrap: wrap;
	}

	.rating-section {
		display: flex;
		align-items: center;
	}

	.star {
		font-size: 28rpx;
		color: #ddd;
		margin-right: 2rpx;
	}

	.star.filled {
		color: #FFB800;
	}

	.rating-score {
		font-size: 26rpx;
		color: #333333;
		margin-left: 8rpx;
		font-weight: 400;
	}

	.work-time {
		display: flex;
		align-items: center;
	}

	.time-icon {
		font-size: 24rpx;
		margin-right: 8rpx;
		color: #666666;
	}

	.time-text {
		font-size: 24rpx;
		color: #666666;
	}

	.like-section {
		display: flex;
		align-items: center;
	}

	.like-icon {
		font-size: 24rpx;
		margin-right: 8rpx;
		color: #666666;
	}

	.like-text {
		font-size: 24rpx;
		color: #666666;
	}

	/* 收藏行 - 包含收藏和咨询按钮 */
	.favorite-row {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 32rpx;
	}

	.favorite-section {
		display: flex;
		align-items: center;
	}

	.favorite-icon {
		font-size: 32rpx;
		margin-right: 8rpx;
		color: #666666;
	}

	.favorite-text {
		font-size: 24rpx;
		color: #666666;
	}

	/* 咨询按钮 */
	.consult-button {
		background-color: #e9f6ef;
		padding: 16rpx 48rpx;
		border-radius: 32rpx;
		text-align: center;
	}

	.consult-text {
		font-size: 28rpx;
		font-weight: 500;
		color: #119c58;
	}

	/* ========== 医生详情卡片 ========== */
	.detail-card {
		background-color: #ffffff;
		margin-bottom: 24rpx;
		padding: 32rpx;
		border-radius: 24rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	}

	.section-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333333;
		margin-bottom: 24rpx;
		display: block;
	}

	.detail-content {
		font-size: 26rpx;
		color: #666666;
		line-height: 1.5;
		display: block;
	}

	/* 🆕 无描述时的样式 */
	.no-description {
		color: #999999 !important;
		font-style: italic;
		text-align: center !important;
		padding: 24rpx 0;
	}

	/* ========== 联系电话区域 ========== */
	.contact-section {
		margin-bottom: 24rpx;
	}

	.contact-item {
		background-color: #ffffff;
		padding: 32rpx;
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	}

	.contact-icon-container {
		width: 72rpx;
		height: 72rpx;
		background-color: #e9f6ef;
		border-radius:30%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 24rpx;
	}

	.contact-icon {
		font-size: 32rpx;
		color: #4CAF50;
	}

	.contact-content {
		flex: 1;
	}

	.contact-label {
		font-size: 26rpx;
		color: #999999;
		margin-bottom: 4rpx;
		display: block;
	}

	.contact-value {
		font-size: 30rpx;
		color: #333333;
		font-weight: 500;
		display: block;
	}

	.contact-action {
		padding: 12rpx 24rpx;
		background-color: transparent;
		// border: 1px solid #4CAF50;
		border-radius: 24rpx;
	}

	.action-text {
		font-size: 26rpx;
		color: #4CAF50;
		font-weight: 500;
	}

	/* ========== 工作地址区域 ========== */
	.address-section {
		margin-bottom: 24rpx;
	}

	.address-item {
		background-color: #ffffff;
		padding: 32rpx;
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	}

	.address-icon-container {
		width: 72rpx;
		height: 72rpx;
		background-color: #e9f6ef;
		border-radius: 30%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 24rpx;
	}

	.address-icon {
		font-size: 32rpx;
		color: #4CAF50;
	}

	.address-content {
		flex: 1;
	}

	.address-label {
		font-size: 26rpx;
		color: #999999;
		margin-bottom: 4rpx;
		display: block;
	}

	.address-value {
		font-size: 30rpx;
		color: #333333;
		font-weight: 500;
		display: block;
	}

	.address-action {
		padding: 12rpx 24rpx;
		background-color: transparent;
		// border: 1px solid #4CAF50;
		border-radius: 24rpx;
	}

	/* ========== 擅长领域区域 ========== */
	.specialty-section {
		background-color: #ffffff;
		margin-bottom: 24rpx;
		padding: 32rpx;
		border-radius: 24rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	}

	.specialty-tags {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
	}

	.specialty-tag {
		background-color:#e9f6ef;
		color: #4CAF50;
		padding: 12rpx 24rpx;
		border-radius: 32rpx;
		font-size: 26rpx;
		font-weight: 500;
	}

	/* 🆕 无专科信息时的样式 */
	.no-specialty {
		padding: 24rpx 0;
		text-align: center;
	}

	.no-specialty-text {
		font-size: 26rpx;
		color: #999999;
		font-style: italic;
	}

	/* ========== 医生推荐区域 ========== */
	.recommend-section {
		background-color: #ffffff;
		margin-bottom: 24rpx;
		padding: 32rpx;
		border-radius: 24rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	}

	.recommend-products {
		display: flex;
		gap: 24rpx;
	}

	.product-card {
		flex: 1;
		background-color: #ffffff;
		border-radius: 16rpx;
		border: 1px solid #f0f0f0;
		overflow: hidden;
	}

	.product-image {
		width: 100%;
		height: 200rpx;
		border-radius: 0;
	}

	.product-info {
		padding: 16rpx;
	}

	.product-name {
		font-size: 26rpx;
		font-weight: 600;
		color: #333333;
		margin-bottom: 8rpx;
		display: block;
	}

	.product-desc {
		font-size: 22rpx;
		color: #666666;
		margin-bottom: 16rpx;
		display: block;
		line-height: 1.3;
	}

	.product-price {
		display: flex;
		align-items: baseline;
		margin-bottom: 16rpx;
	}

	.price-symbol {
		font-size: 22rpx;
		color: #4CAF50;
		margin-right: 2rpx;
	}

	.price-value {
		font-size: 28rpx;
		font-weight: 700;
		color: #4CAF50;
		margin-right: 12rpx;
	}

	.original-price {
		font-size: 22rpx;
		color: #999999;
		text-decoration: line-through;
	}

	.product-btn {
		background-color: #109d58;
		padding: 12rpx 0;
		text-align: center;
		margin: 0 16rpx 16rpx 16rpx;
		border-radius: 8rpx;
		display: flex;
		justify-content: center;
	}

	.btn-text {
		font-size: 24rpx;
		color: #ffffff;
		font-weight: 500;
	}

	/* ========== 加载状态样式 ========== */
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 32rpx;
		background-color: #ffffff;
		border-radius: 24rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	}

	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #4CAF50;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 24rpx;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.loading-text {
		font-size: 28rpx;
		color: #666666;
	}

	/* ========== 错误状态样式 ========== */
	.error-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 32rpx;
		background-color: #ffffff;
		border-radius: 24rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	}

	.error-text {
		font-size: 28rpx;
		color: #ff4757;
		margin-bottom: 32rpx;
		text-align: center;
	}

	.retry-btn {
		background-color: #4CAF50;
		padding: 16rpx 32rpx;
		border-radius: 24rpx;
	}

	.retry-text {
		font-size: 26rpx;
		color: #ffffff;
		font-weight: 500;
	}

	/* ========== 底部安全区域 ========== */
	.safe-area-bottom {
		width: 100%;
		background-color: #ffffff;
	}

	/* ========== RTL布局支持 ========== */
	/* 导航栏RTL调整 */
	.ug.lang-ug .navbar-center {
		left: 70%;
	}

	/* 医生信息卡片RTL调整 - 收藏和点赞按钮在维吾尔文状态下左边显示 */
	.ug.lang-ug .doctor-name-row {
		/* 反转布局：按钮在左，姓名在右 */
		flex-direction: row-reverse;
		justify-content: space-between;
	}

	.ug.lang-ug .doctor-name {
		/* 医生姓名右对齐 */
		text-align: right;
		flex: 1;
		/* 确保姓名在右侧有足够空间 */
		margin-left: 16rpx;
	}

	.ug.lang-ug .action-buttons {
		/* 按钮组在左侧，移除右边距，添加左边距 */
		margin-left: 0;
		margin-right: 0;
		/* 确保按钮组在最左侧 */
		order: -1;
	}

	/* 评分和信息行RTL调整 - 维吾尔文状态下右边显示 */
	.ug.lang-ug .info-row {
		/* 整体布局调整为右对齐 */
		justify-content: flex-end;
		flex-direction: row-reverse;
		gap: 24rpx; /* 减小间距 */
	}

	/* 评分部分RTL调整 */
	.ug.lang-ug .rating-section {
		/* 评分部分内部布局调整 */
		flex-direction: row-reverse;
		order: 1; /* 确保评分在最右侧 */
	}

	.ug.lang-ug .star {
		/* 星星间距调整 */
		margin-right: 2rpx;
		margin-left: 0;
	}

	.ug.lang-ug .rating-score {
		/* 评分数字位置调整 */
		margin-left: 8rpx;
		margin-right: 0;
	}

	/* 点赞部分RTL调整 */
	.ug.lang-ug .like-section {
		/* 点赞部分内部布局调整 */
		flex-direction: row-reverse;
		order: 2; /* 点赞在中间 */
	}

	.ug.lang-ug .like-icon {
		/* 点赞图标间距调整 */
		margin-right: 0;
		margin-left: 8rpx;
	}

	.ug.lang-ug .like-text {
		/* 点赞文本右对齐 */
		text-align: right;
	}

	/* 收藏部分RTL调整 */
	.ug.lang-ug .favorite-section {
		/* 收藏部分内部布局调整 */
		flex-direction: row-reverse;
		order: 3; /* 收藏在最左侧 */
	}

	.ug.lang-ug .favorite-icon {
		/* 收藏图标间距调整 */
		margin-right: 0;
		margin-left: 8rpx;
	}

	.ug.lang-ug .favorite-text {
		/* 收藏文本右对齐 */
		text-align: right;
	}

	/* 工作时间RTL调整 */
	.ug.lang-ug .time-icon {
		margin-right: 0;
		margin-left: 8rpx;
	}

	/* 收藏行RTL调整 - 咨询按钮在维吾尔文状态下靠右显示 */
	.ug.lang-ug .favorite-row {
		/* 咨询按钮完全靠右对齐 */
		justify-content: flex-end;
		/* 确保按钮在右侧 */
		flex-direction: row;
		align-items: center;
	}

	.ug.lang-ug .consult-button {
		/* 咨询按钮样式调整 */
		text-align: center;
		/* 确保按钮在最右侧 */
		margin-left: auto;
		margin-right: 0;
	}

	.ug.lang-ug .consult-text {
		/* 咨询按钮文本居中 */
		text-align: center;
	}

	/* 联系信息RTL调整 */
	.ug.lang-ug .contact-item,
	.ug.lang-ug .address-item {
		flex-direction: row-reverse;
	}

	/* 维吾尔文状态下图标和操作按钮位置互换 */
	.ug.lang-ug .contact-icon-container,
	.ug.lang-ug .address-icon-container {
		margin-right: 0;
		margin-left: 24rpx;
		order: 3; /* 图标移到右边 */
	}

	.ug.lang-ug .contact-action,
	.ug.lang-ug .address-action {
		order: 1; /* 操作按钮移到左边 */
	}

	.ug.lang-ug .contact-content,
	.ug.lang-ug .address-content {
		order: 2; /* 内容居中 */
	}

	/* 推荐产品RTL调整 */
	.ug.lang-ug .product-price {
		flex-direction: row-reverse;
	}

	.ug.lang-ug .price-symbol {
		margin-right: 0;
		margin-left: 2rpx;
	}

	.ug.lang-ug .price-value {
		margin-right: 0;
		margin-left: 12rpx;
	}

	/* 字体大小响应式样式 */
	.font-size-small {
		.navbar-title {
			font-size: 32rpx !important;
		}

		.experience {
			font-size: 24rpx !important;
		}

		.badge-text {
			font-size: 24rpx !important;
		}

		.doctor-name {
			font-size: 40rpx !important;
		}

		.doctor-department {
			font-size: 26rpx !important;
		}

		.doctor-hospital {
			font-size: 24rpx !important;
		}

		.section-title {
			font-size: 32rpx !important;
		}

		.detail-content {
			font-size: 26rpx !important;
		}

		.contact-label {
			font-size: 26rpx !important;
		}

		.contact-value {
			font-size: 26rpx !important;
		}

		.product-name {
			font-size: 26rpx !important;
		}

		.product-desc {
			font-size: 22rpx !important;
		}

		.price-value {
			font-size: 28rpx !important;
		}

		.btn-text {
			font-size: 22rpx !important;
		}
	}

	.font-size-medium {
		.navbar-title {
			font-size: 36rpx !important;
		}

		.experience {
			font-size: 28rpx !important;
		}

		.badge-text {
			font-size: 28rpx !important;
		}

		.doctor-name {
			font-size: 44rpx !important;
		}

		.doctor-department {
			font-size: 30rpx !important;
		}

		.doctor-hospital {
			font-size: 28rpx !important;
		}

		.section-title {
			font-size: 36rpx !important;
		}

		.detail-content {
			font-size: 30rpx !important;
		}

		.contact-label {
			font-size: 30rpx !important;
		}

		.contact-value {
			font-size: 30rpx !important;
		}

		.product-name {
			font-size: 30rpx !important;
		}

		.product-desc {
			font-size: 26rpx !important;
		}

		.price-value {
			font-size: 32rpx !important;
		}

		.btn-text {
			font-size: 24rpx !important;
		}
	}

	.font-size-large {
		.navbar-title {
			font-size: 40rpx !important;
		}

		.experience {
			font-size: 32rpx !important;
		}

		.badge-text {
			font-size: 32rpx !important;
		}

		.doctor-name {
			font-size: 48rpx !important;
		}

		.doctor-department {
			font-size: 34rpx !important;
		}

		.doctor-hospital {
			font-size: 32rpx !important;
		}

		.section-title {
			font-size: 40rpx !important;
		}

		.detail-content {
			font-size: 34rpx !important;
		}

		.contact-label {
			font-size: 34rpx !important;
		}

		.contact-value {
			font-size: 34rpx !important;
		}

		.product-name {
			font-size: 34rpx !important;
		}

		.product-desc {
			font-size: 30rpx !important;
		}

		.price-value {
			font-size: 36rpx !important;
		}

		.btn-text {
			font-size: 28rpx !important;
		}
	}
</style>