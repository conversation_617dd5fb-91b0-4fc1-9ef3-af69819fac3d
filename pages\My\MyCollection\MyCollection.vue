<template>
	<view class="collection-container">
		<!-- 内容区域 -->
		<view class="content-area">
			<!-- 空状态 -->
			<view v-if="favoriteDoctors.length === 0" class="empty-state">
				<view class="empty-icon">
					<fui-icon name="like" :size="160" color="#d9d9d9"></fui-icon>
				</view>
				<text class="empty-title">暂无收藏的医生</text>
				<text class="empty-subtitle">去医生详情页面收藏您喜欢的医生吧</text>
			</view>

			<!-- 收藏列表 -->
			<view v-else class="favorite-list">
				<view
					v-for="doctor in favoriteDoctors"
					:key="doctor.id"
					class="doctor-card"
					@click="goToDoctorDetail(doctor.id)"
				>
					<!-- 医生头像 -->
					<view class="doctor-avatar">
						<image
							:src="doctor.avatar"
							class="avatar-image"
							mode="aspectFill"
							@error="handleImageError"
						></image>
					</view>

					<!-- 医生信息 -->
					<view class="doctor-info">
						<view class="doctor-header">
							<text class="doctor-name">{{ doctor.name }}</text>
							<view class="doctor-specialty">{{ doctor.specialty }}</view>
							<view class="favorite-heart">
								<fui-icon name="like" :size="32" color="#ff4757"></fui-icon>
							</view>
						</view>
						<text class="doctor-description">{{ doctor.description }}</text>

						<!-- 互动信息 -->
						<view class="interaction-info">
							<view class="interaction-item">
								<fui-icon name="fabulous" :size="32" color="#999"></fui-icon>
								<text class="interaction-count">{{ doctor.likeCount }}</text>
							</view>
							<view class="interaction-item">
								<fui-icon name="like" :size="32" color="#999"></fui-icon>
								<text class="interaction-count">{{ doctor.favoriteCount }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'

// 收藏的医生列表
const favoriteDoctors = ref([])

// 返回上一页
const goBack = () => {
	uni.navigateBack()
}

// 跳转到医生详情页
const goToDoctorDetail = (doctorId) => {
	uni.navigateTo({
		url: `/pages/List/DoctorDetails/DoctorDetails?id=${doctorId}`
	})
}

// 获取收藏列表
const getFavoriteDoctors = () => {
	// 从本地存储获取收藏的医生列表
	const favoriteList = uni.getStorageSync('favoriteDoctors') || []
	favoriteDoctors.value = favoriteList
}

// 处理图片加载错误
const handleImageError = (e) => {
	console.log('图片加载失败:', e)
	// 可以设置默认头像
}

// 页面加载时获取数据
onMounted(() => {
	getFavoriteDoctors()
})

// 页面显示时刷新数据
onShow(() => {
	getFavoriteDoctors()
})

// 监听页面显示，刷新数据
uni.$on('refreshFavoriteList', () => {
	getFavoriteDoctors()
})

// 页面卸载时移除事件监听
onUnmounted(() => {
	uni.$off('refreshFavoriteList')
})
</script>
<style lang="scss">
.collection-container {
	min-height: 100vh;
	background: #f5f5f5;
}

/* 内容区域 */
.content-area {
	padding: 16rpx 0;
}

/* 空状态样式 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 300rpx 60rpx;
	text-align: center;
}

.empty-icon {
	margin-bottom: 60rpx;
	opacity: 0.3;
}

.empty-title {
	font-size: 32rpx;
	font-weight: 400;
	color: #999;
	margin-bottom: 20rpx;
}

.empty-subtitle {
	font-size: 26rpx;
	color: #bbb;
	line-height: 1.5;
}

/* 收藏列表样式 */
.favorite-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.doctor-card {
	background: white;
	border-radius: 12rpx;
	padding: 24rpx;
	display: flex;
	align-items: flex-start;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	margin: 0 16rpx;
}

.doctor-avatar {
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	overflow: hidden;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.avatar-image {
	width: 100%;
	height: 100%;
}

.doctor-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.doctor-header {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
	position: relative;
}

.doctor-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	margin-right: 12rpx;
}

.doctor-specialty {
	background: #e8f5e8;
	color: #52c41a;
	font-size: 22rpx;
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
	margin-right: auto;
}

.favorite-heart {
	position: absolute;
	right: 0;
	top: 0;
}

.doctor-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.4;
	margin-bottom: 16rpx;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.interaction-info {
	display: flex;
	align-items: center;
	gap: 24rpx;
}

.interaction-item {
	display: flex;
	align-items: center;
	gap: 6rpx;
}

.interaction-count {
	font-size: 24rpx;
	color: #999;
}
</style>
