# 医生推荐商品图片处理说明

## 📋 概述

医生详情页面中的推荐商品图片处理已经完全优化，包括URL处理、错误处理和默认图片显示。

## 🖼️ 图片处理功能

### 1. **产品图片URL处理函数**
位置：`request/avatar.js`

```javascript
export const processProductImageUrl = (imageUrl) => {
  if (!imageUrl) return '/static/icon/user.svg'

  // 🔧 修复错误的域名
  if (imageUrl.includes('127.0.0.1:8002')) {
    imageUrl = imageUrl.replace('https://127.0.0.1:8002', 'https://appdava.sulmas.com.cn')
    imageUrl = imageUrl.replace('http://127.0.0.1:8002', 'https://appdava.sulmas.com.cn')
    return imageUrl
  }

  // HTTP转HTTPS
  if (imageUrl.startsWith('http://')) {
    return imageUrl.replace('http://', 'https://')
  }

  // 已经是HTTPS或data协议，直接返回
  if (imageUrl.startsWith('https://') || imageUrl.startsWith('data:')) {
    return imageUrl
  }

  // 相对路径处理
  if (imageUrl.startsWith('/file/')) {
    return `https://appdava.sulmas.com.cn${imageUrl}`
  }

  // 文件名处理
  const cleanImageUrl = imageUrl.startsWith('/') ? imageUrl.substring(1) : imageUrl
  return `https://appdava.sulmas.com.cn/file/product_images/${cleanImageUrl}`
}
```

### 2. **医生详情页面中的使用**
位置：`pages/List/DoctorDetails/DoctorDetails.vue`

#### 导入函数
```javascript
import { processAvatarUrl, processProductImageUrl } from '@/request/avatar.js'
```

#### 产品数据处理
```javascript
// 转换产品数据格式
recommendedProducts.value = products.slice(0, 2).map(product => {
  // 🖼️ 处理产品图片URL
  let productImageUrl = '/static/icon/user.svg' // 默认图片
  const originalImageUrl = product.main_image_url || product.image || product.imageUrl
  if (originalImageUrl) {
    try {
      productImageUrl = processProductImageUrl(originalImageUrl)
      console.log('🖼️ 处理产品图片URL:', product.name, originalImageUrl, '->', productImageUrl)
    } catch (error) {
      console.warn('⚠️ 产品图片URL处理失败:', originalImageUrl, error)
    }
  }

  return {
    id: product.id,
    name: product.name,
    image: productImageUrl,
    // ... 其他字段
  }
})
```

#### 图片错误处理
```javascript
// 🆕 图片加载错误处理
const handleProductImageError = (e, productId) => {
  console.warn('🖼️ 产品图片加载失败:', e.detail?.errMsg || e)
  
  // 找到对应的产品并更新其图片为默认图片
  const productIndex = recommendedProducts.value.findIndex(p => p.id === productId)
  if (productIndex !== -1) {
    recommendedProducts.value[productIndex].image = '/static/icon/user.svg'
    console.log(`✅ 已将产品 ${recommendedProducts.value[productIndex].name} 的图片更新为默认图片`)
  }
}
```

#### 模板中的使用
```vue
<image 
  :src="product.image" 
  class="product-image" 
  mode="aspectFill"
  @error="handleProductImageError($event, product.id)"
  @load="console.log('🖼️ 产品图片加载成功:', product.name)"
></image>
```

## 🔧 支持的图片URL格式

### 1. **完整HTTPS URL**
```
https://appdava.sulmas.com.cn/file/product_images/medicine1.jpg
```

### 2. **HTTP URL（自动转换为HTTPS）**
```
http://appdava.sulmas.com.cn/file/product_images/medicine1.jpg
→ https://appdava.sulmas.com.cn/file/product_images/medicine1.jpg
```

### 3. **错误域名（自动修复）**
```
https://127.0.0.1:8002/file/product_images/medicine1.jpg
→ https://appdava.sulmas.com.cn/file/product_images/medicine1.jpg
```

### 4. **相对路径**
```
/file/product_images/medicine1.jpg
→ https://appdava.sulmas.com.cn/file/product_images/medicine1.jpg
```

### 5. **文件名**
```
medicine1.jpg
→ https://appdava.sulmas.com.cn/file/product_images/medicine1.jpg
```

### 6. **Base64图片**
```
data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...
```

## 🎯 字段优先级

产品图片获取的字段优先级：
1. `product.main_image_url` (主要图片URL)
2. `product.image` (备用图片字段)
3. `product.imageUrl` (备用图片字段)
4. `/static/icon/user.svg` (默认图片)

## 🛡️ 错误处理机制

### 1. **URL处理错误**
- 使用try-catch包装图片URL处理
- 处理失败时使用默认图片
- 记录错误日志便于调试

### 2. **图片加载错误**
- 监听image组件的@error事件
- 自动替换为默认图片
- 更新对应产品的图片数据

### 3. **网络异常**
- 图片加载失败时不影响页面显示
- 提供友好的默认图片
- 记录详细的错误信息

## 📱 用户体验优化

### 1. **加载状态**
- 图片加载成功时记录日志
- 图片加载失败时自动切换默认图片
- 不显示破损的图片图标

### 2. **性能优化**
- 使用mode="aspectFill"保持图片比例
- 避免重复处理相同的URL
- 缓存处理结果

### 3. **视觉一致性**
- 统一的默认图片样式
- 保持图片容器尺寸一致
- 平滑的图片切换效果

## 🧪 测试建议

### 1. **URL格式测试**
- 测试完整HTTPS URL
- 测试HTTP URL自动转换
- 测试相对路径处理
- 测试文件名处理

### 2. **错误处理测试**
- 测试无效URL的处理
- 测试网络异常时的表现
- 测试图片不存在时的处理

### 3. **用户体验测试**
- 检查默认图片是否正确显示
- 验证图片加载失败时的切换效果
- 确认页面不会因图片问题而崩溃

## 🔄 维护建议

1. **定期检查图片服务器状态**
2. **监控图片加载失败率**
3. **优化默认图片的选择**
4. **考虑添加图片预加载功能**
5. **实现图片缓存机制**

---

✅ **医生推荐商品图片处理已完全优化！**

现在医生详情页面的推荐商品图片具有完善的URL处理、错误处理和用户体验优化功能。
