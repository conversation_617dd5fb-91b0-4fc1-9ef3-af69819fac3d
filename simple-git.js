const { spawn } = require('child_process');
const fs = require('fs');

console.log('🚀 启动简化版Git自动上传...');
console.log('📁 监听目录:', process.cwd());
console.log('⏰ 每次文件变化后会自动提交到Git');
console.log('🛑 按 Ctrl+C 停止监听\n');

// 忽略的文件和目录
const ignorePaths = [
    'node_modules',
    'unpackage', 
    '.git',
    '.hbuilderx',
    '.vscode',
    'auto-git.js',
    'simple-git.js'
];

// 检查文件是否应该被忽略
function shouldIgnore(filePath) {
    return ignorePaths.some(ignore => filePath.includes(ignore));
}

// 执行Git命令（使用spawn避免编码问题）
function executeGitCommands() {
    const timestamp = new Date().toLocaleString('zh-CN');
    const commitMessage = `自动提交-${timestamp}`;
    
    console.log(`\n📝 检测到文件变化，开始自动提交...`);
    
    // Step 1: git add .
    const gitAdd = spawn('git', ['add', '.'], { 
        stdio: 'pipe',
        shell: true 
    });
    
    gitAdd.on('close', (code) => {
        if (code !== 0) {
            console.error('❌ Git add 失败');
            return;
        }
        
        console.log('✅ 文件已添加到暂存区');
        
        // Step 2: git commit
        const gitCommit = spawn('git', ['commit', '-m', commitMessage], { 
            stdio: 'pipe',
            shell: true 
        });
        
        gitCommit.on('close', (code) => {
            if (code !== 0) {
                console.log('ℹ️  没有文件变化需要提交');
                return;
            }
            
            console.log('✅ 文件已提交到本地仓库');

            // Step 3: git push to origin (主仓库)
            const gitPushOrigin = spawn('git', ['push', 'origin', 'master'], {
                stdio: 'pipe',
                shell: true
            });

            gitPushOrigin.on('close', (code) => {
                if (code !== 0) {
                    console.error('❌ 推送到主仓库失败');
                    return;
                }

                console.log('✅ 代码已成功上传到主仓库(Gitee)!');

                // Step 4: git push to backup (备份仓库)
                const gitPushBackup = spawn('git', ['push', 'backup', 'master'], {
                    stdio: 'pipe',
                    shell: true
                });

                gitPushBackup.on('close', (backupCode) => {
                    if (backupCode !== 0) {
                        console.error('⚠️ 推送到备份仓库失败，但主仓库已成功');
                        console.log('⏰ 继续监听文件变化...\n');
                        return;
                    }

                    console.log('✅ 代码已成功上传到备份仓库!');
                    console.log('🎉 双仓库备份完成!');
                    console.log('⏰ 继续监听文件变化...\n');
                });

                gitPushBackup.on('error', (err) => {
                    console.error('⚠️ 备份仓库推送错误:', err.message);
                    console.log('⏰ 继续监听文件变化...\n');
                });
            });

            gitPushOrigin.on('error', (err) => {
                console.error('❌ 主仓库推送错误:', err.message);
            });
        });
        
        gitCommit.on('error', (err) => {
            console.error('❌ Git commit 错误:', err.message);
        });
    });
    
    gitAdd.on('error', (err) => {
        console.error('❌ Git add 错误:', err.message);
    });
}

// 监听文件变化
let timeout;
fs.watch('.', { recursive: true }, (eventType, filename) => {
    if (!filename || shouldIgnore(filename)) {
        return;
    }
    
    console.log(`📄 文件变化: ${filename}`);
    
    // 防抖：延迟执行，避免频繁提交
    clearTimeout(timeout);
    timeout = setTimeout(() => {
        executeGitCommands();
    }, 3000); // 3秒延迟
});

// 处理程序退出
process.on('SIGINT', () => {
    console.log('\n\n🛑 停止Git自动上传监听');
    console.log('👋 再见！');
    process.exit(0);
});
