@echo off
chcp 65001 >nul
echo 🚀 设置Gitee双仓库备份...
echo ================================

REM 检查Git是否安装
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到Git，请先安装Git
    pause
    exit /b 1
)

echo 📋 当前远程仓库配置：
git remote -v

echo.
echo 🔧 添加备份仓库...
git remote add backup https://gitee.com/ablezz/health-app-backup.git

if %errorlevel% equ 0 (
    echo ✅ 备份仓库添加成功！
) else (
    echo ⚠️ 备份仓库可能已存在，继续...
)

echo.
echo 📋 更新后的远程仓库配置：
git remote -v

echo.
echo 📤 首次推送到备份仓库...
git push backup master

if %errorlevel% equ 0 (
    echo ✅ 首次推送到备份仓库成功！
    echo.
    echo 🎉 双仓库备份设置完成！
    echo 💡 现在运行 npm run git:auto 将同时备份到两个仓库
    echo.
    echo 📊 备份仓库信息：
    echo 主仓库：https://gitee.com/ablezz/health-app.git
    echo 备份仓库：https://gitee.com/ablezz/health-app-backup.git
) else (
    echo ❌ 推送到备份仓库失败
    echo 💡 请确保已在Gitee创建 health-app-backup 仓库
    echo 🌐 创建地址：https://gitee.com/ablezz
)

echo.
pause
