<template>
	<!--
		🏠 首页模板 - 这是整个应用的主页面
		包含：顶部导航栏、侧边栏、医生信息、聊天输入等功能
	-->

	<!--
		📱 顶部导航栏组件
		- CustomNavbar: 自定义的导航栏组件
		- back: 显示返回按钮
		- :title: 动态标题，从语言包获取
		- background: 背景颜色设为白色
		- :showNewButton: 是否显示新建按钮（这里设为false不显示）
		- :showMenuButton: 是否显示菜单按钮（只有登录后才显示）
		- @menuClick: 点击菜单按钮时触发侧边栏
		- @newClick: 点击新建按钮时的处理函数
	-->
	<CustomNavbar
		back
		:title="$t('home.title')"
		background="#ffffff"
		hei
		:showNewButton="false"
		:showMenuButton="isLoggedIn"
		@menuClick="toggleSidebar"
		@newClick="handleNewClick"
	/>

	<!--
		📄 主页面容器
		- class="page": 基础页面样式
		- :class: 动态样式类，包括：
		  * dark-theme: 暗色主题（当isDarkMode为true时）
		  * fontSizeClass: 字体大小样式类
		  * fontClass: 字体类型样式类（支持维吾尔文等）
		- :style: 动态样式，主要用于RTL（从右到左）布局
		- :key: 强制重新渲染的键，当主题、字体、语言变化时会重新渲染
	-->
	<view
		class="page"
		:class="[{ 'dark-theme': isDarkMode }, fontSizeClass, fontClass]"
		:style="pageStyle"
		:key="`${themeUpdateKey}-${fontSizeUpdateKey}-${i18nUpdateKey}`"
	>
		<!--
			🔧 侧边栏导航 - 显示聊天历史记录
			- :class: 动态样式，当isSidebarOpen为true时添加sidebar-open类来显示侧边栏
		-->
		<view :class="['sidebar', isSidebarOpen ? 'sidebar-open' : '']">
			<view class="sidebar-content">
				<!-- 侧边栏头部 - 包含标题和操作按钮 -->
				<view class="sidebar-header">
					<!-- 侧边栏标题，从语言包获取 -->
					<text class="sidebar-title">{{ $t('chatHistory.title') }}</text>

					<!-- 侧边栏操作按钮区域 -->
					<view class="sidebar-actions">
						<!-- 刷新按钮 -->
						<view class="action-btn refresh-btn" @tap="refreshHistory">
							<text class="action-icon">↻</text>
						</view>
						<!-- 关闭按钮 -->
						<view class="action-btn close-btn" @tap="closeSidebar">
							<text class="action-icon">✕</text>
						</view>    
					</view>
					<view>你好</view>
					
					
				</view>
				<!--
					💬 聊天历史记录区域
					这里显示用户之前与医生的聊天记录
				-->
				<view class="chat-history">
					<!--
						📋 有聊天记录时显示列表
						v-if: 只有当chatHistory数组有内容时才显示这个区域
						chatHistory.length > 0: 检查聊天记录数组是否有数据
					-->
					<view v-if="chatHistory.length > 0" class="history-list">
						<!--
							🔄 循环显示每条聊天记录
							v-for: 遍历chatHistory数组中的每个chat对象
							:key: 每个列表项的唯一标识，用chat.id作为key
							@tap: 点击聊天记录时加载该聊天历史
						-->
						<view
							v-for="chat in chatHistory"
							:key="chat.id"
							class="history-item"
							@tap="loadChatHistory(chat)"
						>
							<!-- 医生头像区域 -->
							<view class="history-avatar">
								<image :src="chat.doctorAvatar" class="avatar-img"></image>
							</view>

							<!-- 聊天内容区域 -->
							<view class="history-content">
								<!-- 医生信息头部 -->
								<view class="history-header">
									<!-- 医生姓名 -->
									<text class="doctor-name">{{ chat.doctorName }}</text>
									<!-- 医生科室 -->
									<text class="doctor-dept">{{ chat.doctorDept }}</text>
								</view>
								<!-- 最后一条消息内容 -->
								<text class="history-message">{{ chat.lastMessage }}</text>
							</view>

							<!-- 聊天元信息区域（时间和编辑按钮） -->
							<view class="history-meta">
								<!-- 聊天时间 -->
								<text class="history-time">{{ chat.timestamp }}</text>
								<!--
									编辑按钮
									@tap.stop: 阻止事件冒泡，防止触发父元素的点击事件
								-->
								<view class="edit-icon" @tap.stop="editChatHistory(chat)">
									<text class="edit-text">✏️</text>
								</view>
							</view>
						</view>
					</view>

					<!--
						🈳 无聊天记录时显示空状态
						v-else: 当chatHistory数组为空时显示这个区域
					-->
					<view v-else class="empty-state">
						<!-- 空状态图标 -->
						<view class="empty-icon">💬</view>
						<!-- 空状态标题 -->
						<text class="empty-title">{{ $t('chatHistory.noHistory') }}</text>
						<!-- 空状态副标题 -->
						<text class="empty-subtitle">{{ $t('home.quickConsult') }}</text>
					</view>

					<!--
						🗑️ 清空历史按钮
						放在聊天历史区域内部，用于清空所有聊天记录
					-->
					<view class="clear-history-container">
						<view class="clear-history-btn" @tap="clearHistory">
							<!-- 垃圾桶图标 -->
							<text class="clear-icon">🗑</text>
							<!-- 清空历史文字，从语言包获取 -->
							<text class="clear-text">{{ $t('chatHistory.clearHistory') }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!--
			🎭 主内容区遮罩
			当侧边栏打开时，主内容区会有一个遮罩层
			- :class: 当侧边栏打开时添加sidebar-mask样式
			- @tap: 点击遮罩层时关闭侧边栏
		-->
		<view class="main-content" :class="isSidebarOpen ? 'sidebar-mask' : ''" @tap="closeSidebar"></view>

		<!--
			📱 导航栏占位符
			为了避免内容被顶部导航栏遮挡，添加一个占位符
		-->
		<view class="navbar-placeholder"></view>

		<!--
			📄 主要内容区域
			包含医生信息、轮播图、AI咨询等主要功能
		-->
		<view class="content">
			<!--
				🔐 登录状态显示区域（测试用）
				显示当前用户的登录状态和基本信息
			-->
		<!-- 	<view class="login-status-test" v-if="isLoggedIn">
				<view class="status-header">
					<text class="status-title">✅ 已登录</text>
					<text class="status-subtitle">微信登录成功</text>
					<view class="btn-group">
						<button class="refresh-btn" @click="refreshUserInfo">🔄 刷新</button>
						<button class="debug-btn" @click="debugUserInfo">🔍 调试</button>
					</view>
				</view>
				<view class="user-info-test">
					<image class="user-avatar-test" :src="userInfo.avatar" mode="aspectFill"></image>
					<view class="user-details-test">
						<text class="user-name-test">{{ userInfo.nickname || userInfo.name }}</text>
						<text class="user-phone-test">{{ userInfo.phone }}</text>
					</view>
				</view>
			</view> -->

	<!-- 		<view class="login-status-test" v-else>
				<view class="status-header">
					<text class="status-title">❌ 未登录</text>
					<text class="status-subtitle">点击右上角菜单登录</text>
					<button class="refresh-btn" @click="refreshUserInfo">🔄 刷新</button>
				</view>
			</view> -->

			<!--
				👨‍⚕️ 医生信息头部 - 固定在顶部
				显示当前选中医生的基本信息
			-->
			<view class="doctor-header-fixed">
				<!--
					医生头像
					:src: 动态获取医生头像，如果没有则使用默认头像
					doctors[currentSwiper]?.avatar: 获取当前医生的头像
					|| '/static/doctor1.jpg': 如果没有头像则使用默认头像
				-->
				<image :src="processAvatarUrl(doctors[currentSwiper]?.avatar) || '/static/doctor1.jpg'" class="doctor-avatar"></image>

				<!-- 医生信息区域 -->
				<view class="doctor-info">
					<!-- 医生姓名行 -->
					<view class="doctor-name-row">
						<!--
							医生姓名
							doctors[currentSwiper]?.name: 获取当前医生的姓名
							|| $t('doctorList.defaultDoctor'): 如果没有姓名则显示默认文字
						-->
						<text class="doctor-name">{{ doctors[currentSwiper]?.name || $t('doctorList.defaultDoctor') }}</text>
						<!-- AI标签 -->
						<text class="doctor-tag ai-tag">AI</text>
					</view>
					<!--
						医生职称和科室
						显示医生的职称和所属科室，用 · 分隔
					-->
					<text class="doctor-title">{{ doctors[currentSwiper]?.title || $t('doctorList.defaultTitle') }} ·
						{{ doctors[currentSwiper]?.department || $t('doctorList.defaultDepartment') }}</text>
				</view>

				<!--
					新建聊天按钮
					@tap: 点击时开始新的聊天对话
				-->
				<view class="expand-icon" @tap="handleNewClick">
					<text class="expand-text">{{ $t('home.newChat') }}</text>
				</view>
			</view>

			<!--
				📱 动态切换显示区域 - 可滚动内容区
				根据用户操作动态显示不同的内容
			-->
			<view class="scrollable-content">
				<!--
					👨‍⚕️ 默认显示医生资料轮播区
					v-if="!showAiConsultation": 当没有开始AI咨询时显示
					DoctorSwiper: 医生轮播组件，显示医生详细信息
					:doctors: 传递医生数据数组
					:currentSwiper: 当前选中的医生索引
					@swiperChange: 当轮播切换时触发的事件
				-->
				<DoctorSwiper
					v-if="!showAiConsultation"
					:doctors="doctors"
					:currentSwiper="currentSwiper"
					@swiperChange="handleSwiperChange"
				/>

				<!--
					🤖 AI咨询组件
					v-if="showAiConsultation": 当开始AI咨询时显示
					ref="aiConsultationRef": 组件引用，用于在JS中调用组件方法
					:input-message: 传递用户输入的消息
					:history-messages: 传递历史聊天消息
					@messageProcessed: 消息处理完成时的回调
					@editMessage: 编辑消息时的回调
				-->
				<AiConsultation
					ref="aiConsultationRef"
					v-if="showAiConsultation"
					:input-message="currentMessage"
					:history-messages="currentHistoryMessages"
					@messageProcessed="onMessageProcessed"
					@editMessage="onEditMessage"
				/>
			</view>

			<!--
				🎤 语音输入区域
				这是页面底部的输入区域，支持语音和文字两种输入方式
				:style: 动态调整底部位置，当键盘弹起时会调整位置避免被遮挡
			-->
			<view class="voice-input-area" :style="{ bottom: keyboardHeight > 0 ? keyboardHeight + 'rpx' : '162rpx' }">
				<!--
					📝 隐藏的输入框
					v-if="!isKeyboardMode": 只在语音模式下显示
					这个输入框是隐藏的，主要用于在语音模式下触发键盘
					ref="hiddenInput": 组件引用，用于在JS中控制焦点
					v-model="inputText": 双向绑定输入文本
					:focus: 控制是否获得焦点
					@blur: 失去焦点时的回调
					@confirm: 确认输入时的回调（按回车或发送）
					confirm-type="send": 键盘确认按钮显示为"发送"
				-->
				<input
					v-if="!isKeyboardMode"
					ref="hiddenInput"
					v-model="inputText"
					class="hidden-input"
					:focus="showKeyboardFlag"
					@blur="onInputBlur"
					@confirm="onInputConfirm"
					:placeholder="$t('home.inputPlaceholder')"
					confirm-type="send"
				/>

				<!--
					🛠️ 输入工具栏
					根据当前模式（语音/键盘）显示不同的样式
					:class: 动态样式类，键盘模式和语音模式有不同的样式
				-->
				<view :class="isKeyboardMode ? 'keyboard-input-tools' : 'input-tools'">
					<!--
						⌨️ 左侧按钮 - 切换输入模式
						点击可以在语音模式和键盘模式之间切换
					-->
					<view class="side-button" @tap="showKeyboard">
						<view class="button-icon">
							<!--
								图标组件
								:name: 根据当前模式显示不同图标
								- 键盘模式时显示语音图标
								- 语音模式时显示键盘图标
							-->
							<fui-icon
								:name="isKeyboardMode ? 'voice' : 'keyboard'"
								:size="UI_CONSTANTS.SIZES.ICON_LG"
								:color="COLOR_CONSTANTS.TEXT_SECONDARY"
							></fui-icon>
						</view>
					</view>

					<!--
						🎤 中间区域 - 语音按钮（语音模式）
						v-if="!isKeyboardMode": 只在语音模式下显示
						:class="{ 'recording': isRecording }": 录音时添加recording样式
						@touchstart: 开始触摸时开始录音
						@touchmove: 触摸移动时处理手势
						@touchend: 结束触摸时停止录音
						@touchcancel: 触摸取消时停止录音
					-->
					<view
						v-if="!isKeyboardMode"
						class="voice-button"
						:class="{ 'recording': isRecording }"
						@touchstart="startVoiceRecording"
						@touchmove="handleVoiceTouchMove"
						@touchend="stopVoiceRecording"
						@touchcancel="stopVoiceRecording"
					>
						<!-- 语音图标 -->
						<view class="voice-icon"></view>
						<!--
							语音按钮文字
							根据是否正在录音显示不同的提示文字
						-->
						<text class="voice-text">{{ isRecording ? $t('home.releaseToSend') : $t('home.holdToSpeak') }}</text>
					</view>

					<!--
						📝 中间区域 - 文本输入框（键盘模式）
						v-else: 在键盘模式下显示
					-->
					<view v-else class="text-input-container">
						<!--
							多行文本输入框
							ref="visibleInput": 组件引用
							v-model="inputText": 双向绑定输入文本
							:focus: 在键盘模式下自动获得焦点
							:auto-height: 自动调整高度
							maxlength="500": 最大输入500个字符
							@confirm: 确认输入时的回调
							@blur: 失去焦点时的回调
							@linechange: 行数变化时的回调
						-->
						<textarea
							ref="visibleInput"
							v-model="inputText"
							class="text-input"
							:focus="isKeyboardMode && showKeyboardFlag"
							:placeholder="$t('home.inputPlaceholder')"
							confirm-type="send"
							:auto-height="true"
							maxlength="500"
							@confirm="onInputConfirm"
							@blur="onInputBlur"
							@linechange="onLineChange"
						/>
					</view>

					<!--
						📷 右侧按钮 - 发送/拍照
						:class: 在键盘模式下添加send-button样式
						@tap: 根据当前模式执行不同操作
						- 键盘模式：发送消息
						- 语音模式：拍照
					-->
					<view
						class="side-button"
						:class="{ 'send-button': isKeyboardMode }"
						@tap="isKeyboardMode ? sendMessage : takePhoto"
					>
						<view class="button-icon">
							<!--
								相机图标（语音模式下显示）
								v-if="!isKeyboardMode": 只在语音模式下显示
							-->
							<fui-icon
								v-if="!isKeyboardMode"
								name="camera"
								:size="UI_CONSTANTS.SIZES.ICON_LG"
								:color="COLOR_CONSTANTS.TEXT_SECONDARY"
							></fui-icon>
							<!--
								📤 发送图标（键盘模式下显示）
								v-else: 在键盘模式下显示
								使用静态图片作为发送图标
							-->
							<image
								v-else
								src="/static/icon/SendIcon.png"
								class="send-icon"
							></image>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!--
			⬆️ 上滑取消录音的顶部提示条
			v-if: 只有在显示录音弹窗且用户上滑时才显示
			showRecordingModal: 录音弹窗是否显示
			isSlideUp: 用户是否上滑
		-->
		<view v-if="showRecordingModal && isSlideUp" class="top-cancel-hint">
			<view class="cancel-hint-content">
				<!-- 向上箭头 -->
				<text class="cancel-hint-arrow">^</text>
				<!-- 提示文字 -->
				<text class="cancel-hint-message">{{ $t('home.continueSlideToCancel') }}</text>
			</view>
		</view>

		<!--
			🎤 录音弹窗
			v-if="showRecordingModal": 只有在录音时才显示
			@tap="closeRecordingModal": 点击遮罩层关闭录音弹窗
		-->
		<view v-if="showRecordingModal" class="recording-modal-overlay" @tap="closeRecordingModal">
			<!--
				录音弹窗主体
				:class="{ 'slide-up': isSlideUp }": 上滑时添加slide-up样式
				@tap.stop: 阻止事件冒泡，防止点击弹窗内容时关闭弹窗
				@touchmove: 处理触摸移动事件
				@touchend: 处理触摸结束事件
			-->
			<view
				class="recording-modal"
				:class="{ 'slide-up': isSlideUp }"
				@tap.stop
				@touchmove="handleTouchMove"
				@touchend="handleTouchEnd"
			>
				<!-- 📊 录音信息区域 -->
				<view class="recording-content">
					<!-- 🎯 左侧：麦克风/删除图标和信息 -->
					<view class="left-section">
						<!--
							🎤 正常录音时显示绿色麦克风图标
							v-if="!isSlideUp": 只有在没有上滑时显示
						-->
						<view v-if="!isSlideUp" class="mic-icon">
							<fui-icon name="voice" :size="UI_CONSTANTS.SIZES.FONT_SM" :color="COLOR_CONSTANTS.TEXT_WHITE"></fui-icon>
						</view>

						<!--
							🗑️ 上滑取消时显示红色删除图标
							v-else: 在上滑时显示
						-->
						<view v-else class="delete-icon active">
							<fui-icon name="delete" :size="UI_CONSTANTS.SIZES.FONT_SM" :color="COLOR_CONSTANTS.TEXT_WHITE"></fui-icon>
						</view>

						<!-- 📊 录音信息显示 -->
						<view class="recording-info">
							<!--
								⏱️ 录音时间显示
								formatRecordingTime: 格式化录音时间的函数
								recordingTime: 当前录音时长
							-->
							<text class="time-text">{{ formatRecordingTime(recordingTime) }}</text>

							<!-- 📍 状态行 -->
							<view class="status-row">
								<!--
									状态指示点
									:class="{ 'cancel': isSlideUp }": 上滑时添加cancel样式
								-->
								<view class="status-dot" :class="{ 'cancel': isSlideUp }"></view>
								<!--
									状态文字
									根据是否上滑显示不同的状态文字
								-->
								<text class="status-text" :class="{ 'cancel': isSlideUp }">{{ isSlideUp ? $t('home.releaseToCancel') : $t('home.recording') }}</text>
							</view>
						</view>
					</view>

					<!--
						➡️ 右侧：上滑取消提示（仅在正常录音时显示）
						v-if="!isSlideUp": 只有在没有上滑时显示
						提示用户可以上滑取消录音
					-->
					<view v-if="!isSlideUp" class="right-section">
						<!-- 取消图标 -->
						<view class="cancel-icon">
							<text class="arrow-up">^</text>
						</view>
						<!-- 取消提示文字 -->
						<text class="cancel-text">{{ $t('home.slideUpToCancel') }}</text>
					</view>
				</view>

				<!--
					🌊 底部波浪线
					录音时的视觉效果，模拟声波
				-->
				<view class="wave-container">
					<view class="wave-line"></view>
				</view>
			</view>
		</view>

		<!--
			📱 底部导航栏组件
			CustomTabbar: 自定义的底部导航栏
		-->
		<CustomTabbar></CustomTabbar>

		<!--
			💬 微信登录组件
			ref="weixinLoginRef": 组件引用，用于在JS中调用组件方法
			@close: 关闭微信登录弹窗时的回调
		-->
		<WeixinLogin ref="weixinLoginRef" @close="handleWeixinLoginClose" />
	</view>
</template>

<!--
	🔧 JavaScript 逻辑部分
	使用 Vue 3 的 Composition API 语法
	setup: 表示使用组合式API
-->
<script setup>
	// ==================== 📦 导入依赖 ====================

	// Vue 3 核心功能导入
	import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
	// uni-app 生命周期钩子
	import { onShow, onLoad } from '@dcloudio/uni-app';

	// 自定义组件导入
	import { CustomNavbar } from '/components/CustomNavbar/CustomNavbar';  // 顶部导航栏
	import { CustomTabbar } from '/components/CustomTabbar/CustomTabbar';  // 底部导航栏
	import DoctorSwiper from '@/components/DoctorSwiper/DoctorSwiper.vue';  // 医生轮播组件
	import AiConsultation from '@/components/AiConsultation/AiConsultation.vue';  // AI咨询组件
	import fuiIcon from '@/components/firstui/fui-icon/fui-icon.vue';  // 图标组件
	import WeixinLogin from '@/components/Weixin/index.vue';  // 微信登录组件

	// 状态管理导入
	import { useAppStore } from '@/store/app.js';  // 应用全局状态
	import { useUserStore } from '@/store/user.js';  // 用户状态

	// 工具函数导入
	import { useFontSizePage } from '@/utils/fontSizeMixin.js';  // 字体大小控制
	import { useRTLPage } from '@/utils/rtlMixin.js';  // RTL（从右到左）布局支持
	import { t } from '@/locale/index.js';  // 国际化翻译函数
	import { showSuccessToast, showErrorToast, showLoadingToast, showPlainToast } from '@/utils/toast.js';  // Toast工具函数
	import { UI_CONSTANTS, COLOR_CONSTANTS } from '@/utils/constants.js';  // 常量定义
	import { processAvatarUrl } from '@/request/avatar.js';  // 头像URL处理

	// ==================== 🎨 主题和样式控制 ====================

	// 应用全局状态管理
	const appStore = useAppStore();
	// 计算属性：是否为暗色模式
	const isDarkMode = computed(() => appStore.isDarkMode);
	// 主题更新键，用于强制重新渲染
	const themeUpdateKey = ref(0);

	// 字体大小控制
	// useFontSizePage: 自定义Hook，返回字体大小相关的响应式数据
	const { fontSizeClass, fontSizeUpdateKey } = useFontSizePage();

	// RTL（从右到左）布局支持
	// useRTLPage: 自定义Hook，用于支持阿拉伯语、维吾尔文等从右到左的语言
	const { pageClass, pageStyle } = useRTLPage();

	// ==================== 🌍 国际化支持 ====================

	// 当前语言状态
	const currentLanguage = ref(appStore.lang);
	// 国际化更新键，用于强制重新渲染
	const i18nUpdateKey = ref(0);

	// 计算字体类（合并RTL类）
	// 这个计算属性会根据当前语言自动应用正确的字体和布局样式
	const fontClass = computed(() => ({
		...pageClass.value,  // 展开RTL布局相关的样式类
		'ug': appStore.isUyghur,  // 如果是维吾尔语，添加ug类
		[`lang-${appStore.lang}`]: true  // 添加语言特定的样式类
	}));

	// 翻译方法
	// $t: 简化的翻译函数，用于在模板中获取翻译文本
	const $t = (key) => t(key);

	// ==================== 👤 用户状态管理 ====================

	// 用户状态管理
	// 使用全局用户状态
	const { initUserState, loginSuccess } = useUserStore()

	// 使用全局应用状态（与My页面保持一致）
	const userInfo = computed(() => appStore.user_info)
	const isLoggedIn = computed(() => {
		return appStore.user_info.auth;
	})

	// 微信登录组件引用
	// 用于在JavaScript中调用微信登录组件的方法
	const weixinLoginRef = ref(null);

	// ==================== 📡 事件监听 ====================

	// 监听主题变化
	// uni.$on: uni-app的全局事件监听
	// 当用户在设置页面切换主题时，这里会收到通知并更新页面
	uni.$on('themeChanged', (data) => {
		console.log('首页接收到主题变化:', data.isDark ? '暗色模式' : '浅色模式');
		themeUpdateKey.value++;  // 更新键值，强制页面重新渲染
	});

	// 监听语言变化
	// 当用户在设置页面切换语言时，这里会收到通知并更新页面
	uni.$on('languageChanged', (data) => {
		console.log('首页接收到语言变化:', data.language);
		currentLanguage.value = data.language;  // 更新当前语言
		i18nUpdateKey.value++;  // 更新键值，强制页面重新渲染
	});

	// 监听字体变化
	// 当用户在设置页面切换字体时，这里会收到通知
	uni.$on('languageFontChanged', (data) => {
		console.log('首页接收到字体变化:', data.language);
		currentLanguage.value = data.language;
		i18nUpdateKey.value++;
	});

	// 字体大小变化监听已由useFontSizePage自动处理
	// useFontSizePage Hook内部已经处理了字体大小变化的监听

	// ==================== 🔧 侧边栏控制 ====================

	// 侧边栏开关状态
	const isSidebarOpen = ref(false);

	// 切换侧边栏显示/隐藏
	const toggleSidebar = () => {
		isSidebarOpen.value = !isSidebarOpen.value;
	};

	// 关闭侧边栏
	const closeSidebar = () => {
		if (isSidebarOpen.value) {
			isSidebarOpen.value = false;
		}
	};

	// ==================== 🔄 轮播控制 ====================

	// 当前轮播索引（当前选中的医生）
	const currentSwiper = ref(0);

	// 处理轮播切换事件
	// 当用户滑动轮播图时，这个函数会被调用
	const handleSwiperChange = (index) => {
		console.log('Swiper changed to index:', index);
		console.log('Current doctor:', doctors.value[index]);
		currentSwiper.value = index;  // 更新当前选中的医生索引
	};

	// ==================== 👨‍⚕️ 医生信息处理 ====================

	// 处理从其他页面传递过来的医生信息
	// 当用户从医生列表页面选择医生后，会跳转到首页并传递医生信息
	const handleSelectedDoctor = () => {
		try {
			const selectedDoctor = uni.getStorageSync('selectedDoctor');

			if (selectedDoctor) {
				// 根据医生姓名或科室匹配首页的医生数据
				const doctorIndex = findDoctorIndex(selectedDoctor);

				if (doctorIndex !== -1) {
					// 找到匹配的医生，切换轮播到对应位置
					currentSwiper.value = doctorIndex;
					// 显示成功提示
					showSuccessToast($t('home.switchedToDoctor', { name: doctors.value[doctorIndex].name }));
				} else {
					addDoctorToList(selectedDoctor);
				}

				// 清除存储的医生信息
				uni.removeStorageSync('selectedDoctor');
			}
		} catch (error) {
			console.error('处理选中医生信息时出错:', error);
		}
	};

	// 查找医生在首页医生列表中的索引
	const findDoctorIndex = (selectedDoctor) => {
		console.log('开始匹配医生:', selectedDoctor.name, selectedDoctor.specialty);

		return doctors.value.findIndex((doctor, index) => {
			console.log(`检查医生 ${index}:`, doctor.name, doctor.department);

			// 优先按姓名精确匹配
			if (doctor.name === selectedDoctor.name) {
				console.log('姓名匹配成功:', doctor.name);
				return true;
			}

			// 按姓名模糊匹配（去掉"医生"后缀）
			const doctorNameBase = doctor.name.replace('医生', '');
			const selectedNameBase = selectedDoctor.name.replace('医生', '');
			if (doctorNameBase === selectedNameBase) {
				console.log('姓名模糊匹配成功:', doctorNameBase);
				return true;
			}

			// 按科室匹配
			if (doctor.department === selectedDoctor.specialty) {
				console.log('科室匹配成功:', doctor.department);
				return true;
			}

			// 科室模糊匹配（包含关系）
			if (doctor.department && selectedDoctor.specialty &&
				(doctor.department.includes(selectedDoctor.specialty) ||
				 selectedDoctor.specialty.includes(doctor.department))) {
				console.log('科室模糊匹配成功:', doctor.department, selectedDoctor.specialty);
				return true;
			}

			return false;
		});
	};

	// 将新医生添加到医生列表中
	const addDoctorToList = (selectedDoctor) => {
		const newDoctor = {
			name: selectedDoctor.name,
			title: '医师',
			department: selectedDoctor.specialty || '综合科',
			avatar: selectedDoctor.avatar || '/static/doctor1.jpg',
			introduction: `专业从事${selectedDoctor.specialty || '医疗'}工作${selectedDoctor.experience || '多'}年`,
			specialties: [selectedDoctor.specialty || '综合医疗'],
			experience: selectedDoctor.experience ? `${selectedDoctor.experience}年` : '多年',
			rating: selectedDoctor.rating ? `${selectedDoctor.rating}分` : '4.5分'
		};

		console.log('准备添加新医生:', newDoctor);

		// 添加到医生列表的第一位
		doctors.value.unshift(newDoctor);
		// 切换到新添加的医生
		currentSwiper.value = 0;

		console.log('已添加新医生到列表，当前索引:', currentSwiper.value);
		console.log('更新后的医生列表长度:', doctors.value.length);

		// 显示成功提示
		showSuccessToast($t('home.addedDoctor', { name: newDoctor.name }));
	};

	// 手动检查医生信息的方法（用于调试）
	const manualCheckDoctor = () => {
		console.log('=== 手动检查医生信息 ===');
		const stored = uni.getStorageSync('selectedDoctor');
		console.log('手动检查存储:', stored);
		if (stored) {
			handleSelectedDoctor();
		} else {
			console.log('没有存储的医生信息');
		}
	};

	// 暴露到全局，方便调试
	if (typeof window !== 'undefined') {
		window.manualCheckDoctor = manualCheckDoctor;
		window.handleSelectedDoctor = handleSelectedDoctor;
	}

	// 键盘输入相关
	const inputText = ref('');
	const showKeyboardFlag = ref(false);
	const keyboardHeight = ref(0);
	const isKeyboardMode = ref(false); // 控制是键盘模式还是语音模式
	const hiddenInput = ref(null);
	const visibleInput = ref(null);

	// 控制组件切换显示
	const showAiConsultation = ref(false);

	// ==================== 🤖 AI咨询相关 ====================

	// AI咨询组件引用
	// 用于在JavaScript中调用AI咨询组件的方法
	const aiConsultationRef = ref(null);

	// 传递给AI咨询组件的消息
	// 当用户发送消息时，这个变量会存储消息内容
	const currentMessage = ref('');

	// 当前加载的历史对话ID（用于更新历史记录）
	// 当用户点击历史对话时，会记录对话ID用于后续更新
	const currentHistoryId = ref(null);

	// 聊天历史记录（来自用户真实对话，只有点击新建后才会有数据）
	// 存储用户与医生的历史聊天记录
	const chatHistory = ref([]);

	// 当前历史消息（传递给AiConsultation组件）
	// 当加载历史对话时，这里会存储历史消息用于显示
	const currentHistoryMessages = ref([]);

	// ==================== 👨‍⚕️ 医生数据 ====================

	// 医生数据 - 与List页面保持一致
	// 这里定义了所有可用的医生信息，包括基本信息、专长、经验等
	const doctors = ref([
		{
			name: '张伟医生',  // 医生姓名
			title: '医师',  // 职称
			department: '心血管内科',  // 科室
			avatar: '/static/doctor1.jpg',  // 头像图片路径
			introduction: '从事心血管疾病诊疗20年，擅长冠心病、高血压、心律失常的治疗诊断',  // 医生介绍
			specialties: ['心血管疾病', '冠心病', '高血压'],  // 专长领域
			experience: '10年',  // 工作经验
			rating: '4.6分'  // 评分
		},
		{
			name: '李娜医生',
			title: '主任医师',
			department: '消化内科',
			avatar: '/static/doctor2.jpg',
			introduction: '专注消化系统疾病诊治15年，尤其擅长胃炎、胃溃疡等疾病的综合治疗',
			specialties: ['消化系统疾病', '胃炎', '胃溃疡'],
			experience: '8年',
			rating: '4.9分'
		},
		{
			name: '王强医生',
			title: '副主任医师',
			department: '呼吸内科',
			avatar: '/static/doctor3.jpg',
			introduction: '呼吸内科专家，对呼吸系统疾病有丰富诊疗经验',
			specialties: ['呼吸系统疾病', '肺炎', '哮喘'],
			experience: '12年',
			rating: '4.7分'
		},
		{
			name: '刘芳医生',
			title: '主治医师',
			department: '内分泌科',
			avatar: '/static/doctor4.jpg',
			introduction: '内分泌科专家，擅长糖尿病、甲状腺疾病等的诊断与治疗',
			specialties: ['糖尿病', '甲状腺疾病', '内分泌失调'],
			experience: '6年',
			rating: '4.8分'
		},
		{
			name: '陈军医生',
			title: '主任医师',
			department: '神经内科',
			avatar: '/static/doctor5.jpg',
			introduction: '神经内科专家，对神经系统疾病有深入研究',
			specialties: ['神经系统疾病', '头痛', '眩晕'],
			experience: '9年',
			rating: '4.6分'
		},
		{
			name: '赵敏医生',
			title: '医师',
			department: '肾内科',
			avatar: '/static/doctor6.jpg',
			introduction: '肾内科专家，擅长各类肾脏疾病的治疗',
			specialties: ['肾脏疾病', '肾炎', '肾结石'],
			experience: '11年',
			rating: '4.9分'
		},
		{
			name: '孙丽医生',
			title: '副主任医师',
			department: '妇产科',
			avatar: '/static/doctor7.jpg',
			introduction: '妇产科专家，擅长妇科疾病的诊治',
			specialties: ['妇科疾病', '产科', '妇科炎症'],
			experience: '7年',
			rating: '4.8分'
		},
		{
			name: '马强医生',
			title: '主治医师',
			department: '骨科',
			avatar: '/static/doctor8.jpg',
			introduction: '骨科专家，对骨科疾病有丰富诊疗经验',
			specialties: ['骨科疾病', '骨折', '关节炎'],
			experience: '15年',
			rating: '4.7分'
		}
	]);

	// 键盘输入功能/模式切换
	const showKeyboard = () => {
		if (isKeyboardMode.value) {
			// 当前是键盘模式，切换回语音模式
			isKeyboardMode.value = false;
			showKeyboardFlag.value = false;
		} else {
			// 当前是语音模式，切换到键盘模式（不需要登录检查）
			isKeyboardMode.value = true;
			showKeyboardFlag.value = true;
		}
	};

	// 输入框失去焦点
	const onInputBlur = () => {
		showKeyboardFlag.value = false;
		isKeyboardMode.value = false; // 键盘收起时切换回语音模式
	};

	// 处理文本框行数变化
	const onLineChange = (e) => {
		console.log('文本框行数变化:', e.detail);
		// 可以在这里处理行数变化的逻辑，比如调整容器高度
	};

	// 输入确认（回车发送）
	const onInputConfirm = () => {
		if (inputText.value.trim()) {
			// 检查登录状态
			if (!checkLoginStatus()) {
				return;
			}

			console.log('index页面发送消息:', inputText.value.trim());

			// 保存用户输入的消息
			const userMessage = inputText.value.trim();

			// 清除历史状态（开始新对话）
			currentHistoryId.value = null;
			currentHistoryMessages.value = [];

			// 清空输入框并切换回语音模式
			inputText.value = '';
			isKeyboardMode.value = false;
			showKeyboardFlag.value = false;

			// 立即切换到AI咨询页面
			showAiConsultation.value = true;
			console.log('切换到AI咨询页面');

			// 使用nextTick确保组件已挂载后再传递消息
			nextTick(() => {
				currentMessage.value = userMessage;
				console.log('设置currentMessage:', currentMessage.value);
			});
		}
	};

	// 发送按钮点击
	const sendMessage = () => {
		// 发送按钮与回车功能完全一致
		onInputConfirm();
	};

	// 消息处理完成回调
	const onMessageProcessed = () => {
		console.log('消息处理完成回调被调用');
		// 延迟清空当前消息，确保AiConsultation组件有足够时间处理
		setTimeout(() => {
			console.log('清空currentMessage');
			currentMessage.value = '';
		}, 100);
	};

	// 编辑消息回调
	const onEditMessage = (content) => {
		// 将消息内容放到输入框中
		inputText.value = content;
		// 切换到键盘模式并获取焦点
		isKeyboardMode.value = true;
		showKeyboardFlag.value = true;
	};







	// 语音录音相关状态
	const isRecording = ref(false);
	const showRecordingModal = ref(false);
	const recordingTime = ref(0);
	const recordingTimer = ref(null);
	const waveHeights = ref([]);
	const recorderManager = uni.getRecorderManager();

	// 上滑取消相关状态
	const isSlideUp = ref(false);
	const startTouchY = ref(0);
	const currentTouchY = ref(0);
	const slideThreshold = 100; // 上滑阈值（rpx）
	const isCancelledRecording = ref(false); // 标记是否是取消录音

	// 初始化波形数据
	const initWaveform = () => {
		waveHeights.value = Array.from({
			length: 50
		}, () => Math.random() * 40 + 10);
	};

	// 更新波形动画
	const updateWaveform = () => {
		waveHeights.value = waveHeights.value.map(() => Math.random() * 60 + 20);
	};

	// 格式化录音时间
	const formatRecordingTime = (seconds) => {
		const mins = Math.floor(seconds / 60);
		const secs = seconds % 60;
		return `${mins}:${secs.toString().padStart(2, '0')}`;
	};

	// 录音管理器事件监听
	recorderManager.onStart(() => {
		console.log('录音开始');
		isRecording.value = true;
		showRecordingModal.value = true;
		recordingTime.value = 0;

		// 初始化波形
		initWaveform();

		// 开始计时器
		recordingTimer.value = setInterval(() => {
			recordingTime.value++;
			updateWaveform(); // 更新波形动画
		}, 1000);
	});

	recorderManager.onStop((res) => {
		console.log('录音结束', res);

		// 检查是否是取消录音
		if (isCancelledRecording.value) {
			console.log('录音已取消，完全回到初始状态');

			// 清理录音状态，回到初始状态
			clearRecordingState();

			// 显示取消提示
			showPlainToast($t('home.recordingCancelled'), 1500);

			// 重置取消标记
			isCancelledRecording.value = false;
			return; // 直接返回，不进行任何后续处理
		}

		// 清理录音状态
		clearRecordingState();

		// 录音完成后直接进行语音识别
		if (res.tempFilePath) {
			console.log('录音文件路径:', res.tempFilePath);
			console.log('录音时长:', res.duration + 'ms');
			console.log('录音文件大小:', res.fileSize + 'bytes');

			// 显示语音识别提示
			showLoadingToast($t('home.recognizingVoice'));

			// 直接进行语音识别转文字
			performVoiceRecognition({
				filePath: res.tempFilePath,
				duration: res.duration,
				fileSize: res.fileSize
			});
		}
	});

	recorderManager.onError((err) => {
		console.error('录音错误', err);
		isRecording.value = false;
		uni.showToast({
			title: $t('home.recordingFailed') + '：' + (err.errMsg || $t('home.unknownError')),
			icon: 'none',
			duration: UI_CONSTANTS.TIMING.TOAST_LONG
		});
	});

	// 执行语音识别转文字
	const performVoiceRecognition = (recordInfo) => {
		console.log('开始语音识别:', recordInfo);

		// 模拟语音识别过程（实际项目中替换为真实API调用）
		setTimeout(() => {
			// 模拟识别结果
			const mockRecognitionResults = [
				'你好，我想咨询一下健康问题',
				'最近感觉有点不舒服，头痛',
				'请问这个症状严重吗',
				'需要去医院检查吗',
				'谢谢医生的建议',
				'我想了解一下这个药物的副作用',
				'这种情况需要注意什么',
				'我的血压有点高，该怎么办',
				'这个检查报告正常吗',
				'需要做进一步检查吗'
			];

			const recognizedText = mockRecognitionResults[Math.floor(Math.random() * mockRecognitionResults
				.length)];

			// 识别成功，直接发送
			handleRecognitionSuccess(recognizedText);

		}, 2000); // 模拟2秒识别时间
	};

	// 处理识别成功结果
	const handleRecognitionSuccess = (recognizedText) => {
		console.log('语音识别成功:', recognizedText);

		// 显示识别成功提示
		uni.showToast({
			title: $t('home.recognitionSuccess'),
			icon: 'success',
			duration: UI_CONSTANTS.TIMING.TOAST_SHORT
		});

		// 将识别结果放入输入框
		inputText.value = recognizedText;

		// 延迟发送，让用户看到识别结果
		setTimeout(() => {
			// 清除历史状态（开始新对话）
			currentHistoryId.value = null;
			currentHistoryMessages.value = [];

			// 立即切换到AI咨询页面
			showAiConsultation.value = true;

			// 使用nextTick确保组件已挂载后再传递消息
			nextTick(() => {
				currentMessage.value = recognizedText;
				console.log('发送识别结果:', recognizedText);
			});

			// 清空输入框
			inputText.value = '';
		}, 1500);
	};

	// 检查登录状态
	const checkLoginStatus = () => {
		if (!isLoggedIn.value) {
			// 未登录，显示登录弹窗
			if (weixinLoginRef.value) {
				weixinLoginRef.value.open();
			}
			return false;
		}
		return true;
	};

	// 刷新用户信息（测试用）
	const refreshUserInfo = () => {
		console.log('🔄 手动刷新用户信息...');
		initUserState();
		console.log('✅ 用户信息刷新完成:', {
			isLoggedIn: isLoggedIn.value,
			userInfo: userInfo.value
		});

		uni.showToast({
			title: '用户信息已刷新',
			icon: 'success'
		});
	};

	// 调试用户信息（测试用）
	const debugUserInfo = () => {
		console.log('=== 🔍 用户信息调试 ===');
		console.log('1. 当前响应式状态:');
		console.log('   - isLoggedIn.value:', isLoggedIn.value);
		console.log('   - userInfo.value:', userInfo.value);

		console.log('2. 本地存储数据:');
		console.log('   - userInfo:', uni.getStorageSync('userInfo'));
		console.log('   - session_key:', uni.getStorageSync('session_key'));
		console.log('   - is_logged_in:', uni.getStorageSync('is_logged_in'));
		console.log('   - auth:', uni.getStorageSync('auth'));
		console.log('   - wechatUserInfo:', uni.getStorageSync('wechatUserInfo'));

		console.log('3. 页面当前显示:');
		console.log('   - 头像src:', userInfo.value.avatar);
		console.log('   - 昵称:', userInfo.value.nickname || userInfo.value.name);
		console.log('   - 手机号:', userInfo.value.phone);

		uni.showModal({
			title: '调试信息',
			content: `登录状态: ${isLoggedIn.value}\n昵称: ${userInfo.value.nickname || userInfo.value.name}\n头像: ${userInfo.value.avatar}`,
			showCancel: false
		});
	};

	// 开始录音（按住开始）
	const startVoiceRecording = (e) => {
		// 检查登录状态
		if (!checkLoginStatus()) {
			return;
		}

		if (isRecording.value) {
			// 如果已经在录音，不重复开始
			return;
		}

		console.log('按住开始录音');

		// 记录初始触摸位置
		startTouchY.value = e.touches[0].clientY;
		currentTouchY.value = e.touches[0].clientY;
		isSlideUp.value = false;

		// 检查录音权限
		uni.authorize({
			scope: 'scope.record',
			success() {
				// 开始录音
				recorderManager.start({
					duration: UI_CONSTANTS.TIMING.RECORD_MAX, // 最长60秒
					sampleRate: 16000, // 采样率
					numberOfChannels: 1, // 单声道
					encodeBitRate: 96000, // 编码码率
					format: 'mp3' // 音频格式
				});
			},
			fail() {
				uni.showModal({
					title: $t('home.permissionRequest'),
					content: $t('home.recordPermissionNeeded'),
					success: (res) => {
						if (res.confirm) {
							// 引导用户到设置页面
							uni.openSetting();
						}
					}
				});
			}
		});
	};

	// 处理语音按钮触摸移动
	const handleVoiceTouchMove = (e) => {
		if (!isRecording.value) {
			return;
		}

		currentTouchY.value = e.touches[0].clientY;
		const deltaY = startTouchY.value - currentTouchY.value;

		// 判断是否上滑超过阈值
		if (deltaY > slideThreshold) {
			isSlideUp.value = true;
		} else {
			isSlideUp.value = false;
		}
	};

	// 停止录音（松开发送或取消）
	const stopVoiceRecording = () => {
		if (!isRecording.value) {
			// 如果没有在录音，不执行停止
			return;
		}

		// 判断是否是上滑取消
		if (isSlideUp.value) {
			console.log('上滑取消录音，将回到初始状态');
			// 设置取消标记
			isCancelledRecording.value = true;
			// 停止录音（不发送）
			recorderManager.stop();
		} else {
			console.log('松开停止录音并发送');
			// 设置为正常发送
			isCancelledRecording.value = false;
			// 停止录音并发送
			recorderManager.stop();
		}

		// 重置触摸状态（这些会在clearRecordingState中再次重置）
		startTouchY.value = 0;
		currentTouchY.value = 0;
		isSlideUp.value = false;
	};

	// 处理录音弹窗触摸移动
	const handleTouchMove = (e) => {
		// 这个函数主要用于阻止事件冒泡，实际的滑动处理在语音按钮上
		e.stopPropagation();
	};

	// 处理录音弹窗触摸结束
	const handleTouchEnd = (e) => {
		// 这个函数主要用于阻止事件冒泡
		e.stopPropagation();
	};

	// 清理录音状态，完全回到初始状态
	const clearRecordingState = () => {
		// 重置录音相关状态
		isRecording.value = false;
		showRecordingModal.value = false;
		recordingTime.value = 0;

		// 清除计时器
		if (recordingTimer.value) {
			clearInterval(recordingTimer.value);
			recordingTimer.value = null;
		}

		// 重置上滑取消相关状态
		isSlideUp.value = false;
		startTouchY.value = 0;
		currentTouchY.value = 0;
		isCancelledRecording.value = false;

		// 重置波形数据
		waveHeights.value = [];

		console.log('录音状态已完全清理，回到初始状态');
	};

	// 关闭录音弹窗
	const closeRecordingModal = () => {
		if (isRecording.value) {
			// 如果正在录音，停止录音
			recorderManager.stop();
		} else {
			// 直接关闭弹窗
			clearRecordingState();
		}
	};



	const takePhoto = () => {
		// 检查登录状态
		if (!checkLoginStatus()) {
			return;
		}

		// 跳转到专门的相机页面
		uni.navigateTo({
			url: '/pages/camera/camera'
		});
	};

	// 处理相机返回的结果
	const handleCameraResult = (imagePath) => {
		console.log('收到相机返回的照片:', imagePath);

		// 显示照片选择成功提示
		uni.showToast({
			title: '照片已选择',
			icon: 'success',
			duration: UI_CONSTANTS.TIMING.TOAST_SHORT
		});

		// 这里可以处理照片，比如发送给AI或保存
		// 可以将照片路径添加到消息中
		if (imagePath) {
			// 创建包含图片的消息
			const imageMessage = `[图片消息] ${imagePath}`;

			// 如果需要立即发送，可以调用发送逻辑
			// 这里暂时只是记录，具体处理可以根据需求调整
			console.log('准备发送图片消息:', imageMessage);
		}
	};

	// 处理图片发送结果
	const handlePhotoSend = (result) => {
		console.log('收到图片发送结果:', result);

		// 显示发送成功提示
		uni.showToast({
			title: '图片发送成功',
			icon: 'success',
			duration: UI_CONSTANTS.TIMING.TOAST_NORMAL
		});

		// 处理发送的图片和选择区域
		if (result.imagePath && result.selection) {
			// 创建包含图片和选择区域的消息
			const message = {
				type: 'image',
				imagePath: result.imagePath,
				selection: result.selection,
				timestamp: new Date().getTime()
			};

			// 这里可以添加到消息列表或发送给AI
			console.log('准备处理图片消息:', message);

			// 可以在这里调用AI接口或其他处理逻辑
			// processImageMessage(message);
		}
	};

	// 新建按钮点击处理
	const handleNewClick = () => {
		// 如果当前在AI咨询页面且有对话内容，保存到历史记录
		if (showAiConsultation.value) {
			saveCurrentChatToHistory();
		}

		// 清空当前对话状态
		currentMessage.value = '';
		currentHistoryId.value = null;
		currentHistoryMessages.value = [];

		// 切换回医生轮播页面
		showAiConsultation.value = false;

		// 切换回语音模式
		isKeyboardMode.value = false;
		showKeyboardFlag.value = false;

		uni.showToast({
			title: $t('home.newChatStarted'),
			icon: 'success',
			duration: UI_CONSTANTS.TIMING.TOAST_SHORT
		});
	};

	// 保存当前对话到历史记录
	const saveCurrentChatToHistory = () => {
		// 检查是否有AI咨询组件的引用
		if (!aiConsultationRef.value) {
			console.log('AI咨询组件引用不存在，无法保存对话');
			return;
		}

		// 获取AI咨询组件中的真实对话数据
		const messages = aiConsultationRef.value.messages || [];

		// 如果没有对话内容，不保存
		if (messages.length === 0) {
			console.log('没有对话内容，不保存到历史记录');
			return;
		}

		// 获取当前医生信息
		const currentDoctor = doctors.value[currentSwiper.value];

		// 获取用户的第一条消息作为lastMessage（用于历史记录列表显示）
		const firstUserMessage = messages.find(msg => msg.type === 'user');
		const lastMessage = firstUserMessage ? firstUserMessage.content : '开始了新的咨询';

		// 如果当前正在编辑历史对话，更新现有记录
		if (currentHistoryId.value) {
			const existingHistoryIndex = chatHistory.value.findIndex(item => item.id === currentHistoryId.value);
			if (existingHistoryIndex !== -1) {
				// 更新现有历史记录
				chatHistory.value[existingHistoryIndex] = {
					...chatHistory.value[existingHistoryIndex],
					lastMessage: lastMessage,
					timestamp: formatChatTime(new Date()),
					messages: [...messages] // 更新完整的对话记录
				};
				console.log('已更新现有历史记录:', chatHistory.value[existingHistoryIndex]);
				console.log('更新的消息数量:', messages.length);
				return;
			}
		}

		// 创建新的聊天历史记录
		const newChatHistory = {
			id: Date.now(),
			doctorName: currentDoctor?.name || '李娜医生',
			doctorAvatar: currentDoctor?.avatar || '/static/doctor2.jpg',
			doctorDept: currentDoctor?.department || '消化内科',
			lastMessage: lastMessage,
			timestamp: formatChatTime(new Date()),
			messages: [...messages] // 保存完整的对话记录
		};

		// 添加到聊天历史列表的开头
		chatHistory.value.unshift(newChatHistory);

		console.log('已保存新的对话到历史记录:', newChatHistory);
		console.log('保存的消息数量:', messages.length);
	};

	// 格式化聊天时间
	const formatChatTime = (date) => {
		const now = new Date();
		const diffTime = now - date;
		const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

		if (diffDays === 0) {
			return '今天';
		} else if (diffDays === 1) {
			return '昨天';
		} else if (diffDays <= 7) {
			return `${diffDays}天前`;
		} else {
			return date.toLocaleDateString();
		}
	};

	// 加载聊天历史
	const loadChatHistory = (chat) => {
		console.log('加载聊天历史:', chat);
		console.log('历史消息内容:', chat.messages);

		// 设置当前历史ID和消息
		currentHistoryId.value = chat.id;
		currentHistoryMessages.value = chat.messages || [];

		// 切换到AI咨询页面
		showAiConsultation.value = true;

		// 关闭侧边栏
		isSidebarOpen.value = false;

		// 使用nextTick确保组件已挂载后再传递历史消息
		nextTick(() => {
			// 传递历史对话标识
			currentMessage.value = `[HISTORY:${chat.id}]`;
			console.log('设置历史对话标识:', currentMessage.value);
			console.log('当前历史消息:', currentHistoryMessages.value);
		});

		uni.showToast({
			title: $t('home.loadedChatWith', { name: chat.doctorName }),
			icon: 'success',
			duration: UI_CONSTANTS.TIMING.TOAST_SHORT
		});
	};

	// 编辑聊天历史（暂时只是提示）
	const editChatHistory = (chat) => {
		uni.showToast({
			title: $t('home.editFeatureInDevelopment'),
			icon: 'none'
		});
	};

	// 侧边栏相关方法
	const refreshHistory = () => {
		uni.showToast({
			title: $t('chatHistory.refreshHistory'),
			icon: 'none'
		});
	};

	const clearHistory = () => {
		uni.showModal({
			title: $t('chatHistory.confirmClear'),
			content: $t('chatHistory.clearWarning'),
			confirmText: $t('chatHistory.confirmClear'),
			cancelText: $t('common.cancel'),
			confirmColor: '#ff4757',
			success: function(res) {
				if (res.confirm) {
					// 清空聊天历史记录数组
					chatHistory.value = [];

					// 如果当前正在查看历史对话，也清空相关状态
					if (currentHistoryId.value) {
						currentHistoryId.value = null;
						currentHistoryMessages.value = [];
						// 如果在AI咨询页面，切换回医生轮播页面
						if (showAiConsultation.value) {
							showAiConsultation.value = false;
						}
					}

					// 显示成功提示
					uni.showToast({
						title: $t('chatHistory.historyCleared'),
						icon: 'success',
						duration: UI_CONSTANTS.TIMING.TOAST_NORMAL
					});

					// 自动关闭侧边栏
					setTimeout(() => {
						isSidebarOpen.value = false;
					}, 1500);
				}
			}
		});
	};

	// 处理微信登录组件关闭事件
	const handleWeixinLoginClose = (loginSuccessFlag, userData) => {
		console.log('微信登录组件关闭，登录成功:', loginSuccessFlag, '用户数据:', userData);
		if (loginSuccessFlag && userData) {
			// 使用全局状态管理的登录成功方法
			loginSuccess(userData);

			uni.showToast({
				title: $t('user.loginSuccess'),
				icon: 'success'
			});
		} else {
			// 重新检查登录状态，以防本地存储已更新
			initUserState();
		}
	};

	// 页面生命周期
	onMounted(() => {
		// 初始化用户状态
		initUserState();

		// 监听医生选择事件
		uni.$on('selectDoctor', (doctorData) => {
			// 将医生数据存储到本地存储
			uni.setStorageSync('selectedDoctor', doctorData);
			// 直接处理医生数据
			handleSelectedDoctor();
		});

		// 监听用户信息更新事件
		uni.$on('userInfoUpdated', (newUserInfo) => {
			if (newUserInfo) {
				initUserState();
			}
		});

		// 监听强制刷新用户信息事件
		uni.$on('forceRefreshUserInfo', (newUserInfo) => {
			if (newUserInfo) {
				initUserState();
			}
		});

		// 监听头像更新事件
		uni.$on('avatarUpdated', (newAvatarUrl) => {
			// 实际的状态更新会通过appStore自动同步
		});

		// 监听昵称更新事件
		uni.$on('nicknameUpdated', (newNickname) => {
			// 实际的状态更新会通过appStore自动同步
		});

		// 监听登录成功事件
		uni.$on('loginSuccess', (userData) => {
			if (userData) {
				initUserState();
			}
		});

		// 监听刷新Index页面事件
		uni.$on('refreshIndexPage', () => {
			initUserState();
		});

		// 延迟检查医生信息
		setTimeout(() => {
			handleSelectedDoctor();
		}, 500);

		// 监听键盘高度变化
		uni.onKeyboardHeightChange((res) => {
			// 直接使用键盘高度，不需要额外偏移
			keyboardHeight.value = res.height * 2; // px转rpx，紧贴键盘顶部

			// 根据键盘高度同步模式状态
			if (res.height > 0) {
				// 键盘弹出，确保是键盘模式
				if (!isKeyboardMode.value) {
					isKeyboardMode.value = true;
				}
			} else {
				// 键盘收起，切换回语音模式
				isKeyboardMode.value = false;
				showKeyboardFlag.value = false;
			}
		});
	});

	// 页面显示时处理传递的医生信息
	onShow(() => {
		console.log('=== onShow 触发 ===');

		// 立即检查一次
		handleSelectedDoctor();

		// 延迟检查，确保数据已经存储
		setTimeout(() => {
			console.log('延迟检查医生信息');
			handleSelectedDoctor();
		}, 100);

		// 再延迟检查一次
		setTimeout(() => {
			console.log('第二次延迟检查医生信息');
			handleSelectedDoctor();
		}, 500);
	});

	onUnmounted(() => {
		// 移除键盘监听
		uni.offKeyboardHeightChange();

		// 移除用户信息更新事件监听
		uni.$off('userInfoUpdated');
		uni.$off('forceRefreshUserInfo');
		uni.$off('avatarUpdated');
		uni.$off('nicknameUpdated');
		uni.$off('loginSuccess');
		uni.$off('refreshIndexPage');
	});
</script>

<script>
// 使用uni-app的页面生命周期
export default {
	onShow() {
		// 页面显示时重新检查登录状态
		uni.$emit('refreshIndexPage')
	}
}
</script>

<style lang="scss">
	.page {
		position: relative;
		min-height: 100vh;
		background-color: #f9f9f9;
	}

	/* 导航栏占位 */
	.navbar-placeholder {
		height: calc(var(--status-bar-height) + 84rpx);
		width: 100%;
	}

	/* 侧边栏样式 */
	.sidebar {
		position: fixed;
		top: 0;
		left: 0;
		height: calc(100vh - 160rpx);
		/* 为底部Tabbar留出空间 */
		width: 75%;
		max-width: 640rpx;
		background-color: #ffffff;
		z-index: 1001;
		transform: translateX(-100%);
		transition: transform 0.3s ease;
		box-shadow: 4rpx 0 24rpx rgba(0, 0, 0, 0.1);
		border-radius: 0 20rpx 20rpx 0;
		/* 右上和右下都有圆角 */
		overflow: hidden;
		/* 确保内容不会超出圆角 */
	}

	.sidebar-open {
		transform: translateX(0);
	}

	.sidebar-content {
		padding-top: var(--status-bar-height);
		height: 100%;
		display: flex;
		flex-direction: column;
		background-color: #ffffff;
		overflow: hidden;
		/* 防止内容溢出 */
		position: relative;
	}

	/* 侧边栏底部渐变效果 */
	.sidebar-content::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 20rpx;
		background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.02));
		pointer-events: none;
		border-radius: 0 0 20rpx 0;
	}

	.sidebar-header {
		padding: 32rpx 32rpx 28rpx 32rpx;
		/* 增加高度 */
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1rpx solid #e8e8e8;
		background-color: #ffffff;
		/* 白色背景 */
		min-height: 100rpx;
		/* 确保最小高度 */
	}

	.sidebar-title {
		font-size: 34rpx;
		/* 稍微大一点 */
		font-weight: 600;
		/* 更粗一点 */
		color: #1a1a1a;
		/* 更深的颜色 */
		letter-spacing: 0.5rpx;
	}

	.sidebar-actions {
		display: flex;
		gap: 20rpx;
		align-items: center;
	}

	.action-btn {
		width: 52rpx;
		/* 稍微大一点 */
		height: 52rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;
		background-color: rgba(255, 255, 255, 0.8);
		/* 半透明白色背景 */
		cursor: pointer;
		transition: all 0.2s ease;
		border: 1rpx solid rgba(0, 0, 0, 0.08);
		/* 淡边框 */
	}

	.action-btn:hover {
		background-color: #f8f9fa;
		transform: scale(1.05);
	}

	.action-icon {
		font-size: 30rpx;
		/* 稍微大一点 */
		color: #666666;
		font-weight: 500;
	}

	.refresh-btn .action-icon {
		color: #1890ff;
		/* 改为蓝色，更符合刷新的含义 */
		font-weight: 600;
	}

	.close-btn .action-icon {
		color: #8c8c8c;
		font-weight: 500;
	}

	.chat-history {
		flex: 1;
		display: flex;
		flex-direction: column;
		overflow-y: auto;
		background-color: #f9f9f9;
		/* 浅灰色背景 */
		padding-top: 8rpx;
		/* 添加顶部间距 */
	}

	.empty-state {
		text-align: center;
		padding: 120rpx 40rpx;
	}

	.empty-icon {
		font-size: 96rpx;
		margin-bottom: 32rpx;
		opacity: 0.3;
	}

	.empty-title {
		display: block;
		font-size: 32rpx;
		color: #333333;
		margin-bottom: 16rpx;
		font-weight: 500;
	}

	.empty-subtitle {
		display: block;
		font-size: 28rpx;
		color: #999;
	}

	.clear-history-container {
		padding: 24rpx 32rpx 24rpx 32rpx;
		/* 调整padding */
		background-color: #ffffff;
		margin-top: auto;
		border-top: 1rpx solid #f0f0f0;
		/* 添加顶部分割线 */
		border-radius: 0 0 20rpx 0;
		/* 右下角圆角，与侧边栏保持一致 */
		position: relative;
	}

	/* 清空历史容器底部边界效果 */
	.clear-history-container::before {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 2rpx;
		background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.05), transparent);
	}

	.clear-history-btn {
		width: 100%;
		height: 88rpx;
		/* 调整高度 */
		background-color: #ff4757;
		/* 更鲜艳的红色 */
		border-radius: 16rpx;
		/* 调整圆角，与整体风格协调 */
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 16rpx;
		/* 增加间距 */
		cursor: pointer;
		transition: all 0.2s ease;
		box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.15);
		/* 调整阴影 */
		margin-bottom: 50rpx;
		/* 添加底部边距，与圆角底部保持距离 */
	}

	.clear-history-btn:hover {
		background-color: #ff7875;
		transform: translateY(-1rpx);
		box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.25);
	}

	.clear-icon {
		font-size: 32rpx;
		/* 稍微大一点 */
		color: #ffffff;
	}

	.clear-text {
		font-size: 30rpx;
		/* 稍微大一点 */
		color: #ffffff;
		font-weight: 600;
		/* 更粗一点 */
		letter-spacing: 1rpx;
		/* 增加字间距 */
	}

	.main-content {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 1000;
		display: none;
	}

	.sidebar-mask {
		display: block;
	}

	/* 内容区域 */
	.content {
		padding: 0;
		padding-bottom: 240rpx;
		box-sizing: border-box;
		height: calc(100vh - var(--status-bar-height) - 84rpx - 100rpx);
		background-color: #f9f9f9;
		display: flex;
		flex-direction: column;
		overflow: hidden;
	}

	/* 医生信息头部 - 固定在顶部 */
	.doctor-header-fixed {
		background-color: #fff;
		border-radius: 24rpx;
		padding: 40rpx;
		margin: 40rpx 30rpx 20rpx 30rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
		position: sticky;
		top: 80rpx;
		z-index: 100;
		transition: all 0.3s ease;
	}

	/* 可滚动内容区域 */
	.scrollable-content {
		flex: 1;
		overflow: hidden;
		margin-top: 20rpx;
	}

	.doctor-avatar {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		margin-right: 30rpx;
		border: 4rpx solid #e8f5e8;
		transition: all 0.3s ease;
		object-fit: cover;
	}

	.doctor-info {
		flex: 1;
		transition: all 0.3s ease;
	}

	.doctor-name-row {
		display: flex;
		align-items: center;
		gap: 16rpx;
		margin-bottom: 12rpx;
	}

	.doctor-name {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		transition: all 0.3s ease;
	}

	.doctor-title {
		color: #666;
		font-size: 28rpx;
		line-height: 1.4;
		transition: all 0.3s ease;
	}

	.ai-tag {
		background: linear-gradient(135deg, #4cd964, #5dd974);
		color: white;
		padding: 4rpx 12rpx;
		border-radius: 16rpx;
		font-size: 18rpx;
		font-weight: 600;
		box-shadow: 0 2rpx 8rpx rgba(76, 217, 100, 0.25);
		letter-spacing: 0.3px;
	}

	.expand-icon {
		position: absolute;
		top: 40rpx;
		right: 40rpx;
		width: 80rpx;
		height: 48rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #4cd964;
		border-radius: 24rpx;
		border: 2rpx solid #4cd964;
		transition: all 0.3s ease;
		box-shadow: 0 2rpx 8rpx rgba(76, 217, 100, 0.25);
	}

	.expand-icon:active {
		background-color: #3db854;
		transform: scale(0.95);
	}

	.expand-text {
		color: #ffffff;
		font-size: 24rpx;
		font-weight: 500;
	}









	/* 语音输入区域 */
	.voice-input-area {
		position: fixed;
		left: 0;
		right: 0;
		padding: 24rpx 30rpx 32rpx;
		background-color: #ffffff;
		border-top: 2rpx solid #f0f0f0;
		transition: bottom 0.3s ease;
		box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
	}

	/* 隐藏的输入框 */
	.hidden-input {
		position: absolute;
		left: -9999rpx;
		top: -9999rpx;
		opacity: 0;
		width: 1rpx;
		height: 1rpx;
	}

	.input-tools {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 24rpx;
		padding: 0 10rpx;
	}

	/* 键盘模式工具栏 */
	.keyboard-input-tools {
		display: flex;
		align-items: flex-end; /* 改为底部对齐，适应多行文本 */
		gap: 24rpx;
		padding: 0 10rpx;
		min-height: 88rpx; /* 设置最小高度 */
	}

	/* 文本输入框容器 */
	.text-input-container {
		flex: 1;
		background-color: #f5f5f5;
		border-radius: 50rpx; /* 调整圆角，适合多行文本 */
		padding: 16rpx 32rpx; /* 调整内边距，适合多行文本 */
		min-height: 60rpx; /* 最小高度 */
		max-height: 200rpx; /* 最大高度，防止过高 */
		display: flex;
		align-items: center; /* 保持居中对齐 */
		border: 2rpx solid #e8e8e8;
		transition: all 0.3s ease;
		overflow: hidden; /* 防止内容溢出 */
	}

	.text-input-container:focus-within {
		border-color: #e0e0e0;
		background-color: #ffffff;
		box-shadow: 0 2rpx 8rpx rgba(76, 217, 100, 0.2);
	}

	/* 文本输入框 */
	.text-input {
		width: 100%;
		min-height: 48rpx; /* 最小高度 */
		max-height: 160rpx; /* 最大高度 */
		border: none;
		background: transparent;
		font-size: 32rpx;
		color: #333;
		line-height: 1.5; /* 调整行高 */
		resize: none; /* 禁止手动调整大小 */
		word-wrap: break-word; /* 自动换行 */
		word-break: break-all; /* 强制换行 */
		padding: 0; /* 移除默认内边距 */
		margin: 0; /* 移除默认外边距 */
		vertical-align: top; /* 顶部对齐 */
		box-sizing: border-box; /* 盒模型 */
	}

	/* 发送按钮特殊样式 */
	.send-button {
		background-color: #4b81ee !important;
		// border-color: red !important;
	}

	.send-button .button-icon {
		background-color: transparent;
		border-radius: 50%;
		width: 88rpx;
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.send-icon {
		width: 36rpx;
		height: 36rpx;
		object-fit: contain;
	}

	.send-button:active {
		background-color: #3db854 !important;
		border-color: #3db854 !important;
	}

	/* 左右两侧按钮 */
	.side-button {
		width: 96rpx;
		height: 96rpx;
		border-radius: 50%;
		background-color: #f5f5f5;
		border: 2rpx solid #e0e0e0;
		display: flex;
		justify-content: center;
		align-items: center;
		transition: all 0.3s ease;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.side-button:active {
		background-color: #e8e8e8;
		transform: scale(0.95);
		box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.15);
	}

	.button-icon {
		display: flex;
		justify-content: center;
		align-items: center;
	}



	/* 中间语音按钮 */
	.voice-button {
		flex: 1;
		height: 100rpx;
		max-width: 500rpx;
		background-color: #109d58;
		border-radius: 50rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 16rpx;
		transition: all 0.3s ease;
		box-shadow: 0 6rpx 16rpx rgba(16, 157, 88, 0.3); /* 调整阴影颜色与按钮颜色协调 */
		margin: 0 20rpx;
	}

	.voice-button:active {
		background-color: #109d58; /* 保持相同的颜色 */
		transform: scale(0.98);
		box-shadow: 0 4rpx 12rpx rgba(16, 157, 88, 0.4); /* 调整阴影颜色 */
	}

	/* 录音状态样式 - 保持相同颜色，只添加动画效果 */
	.voice-button.recording {
		background-color: #109d58; /* 保持相同的颜色 */
		animation: recording-pulse 1.5s infinite;
	}

	@keyframes recording-pulse {
		0% {
			background-color: #109d58; /* 使用统一的绿色 */
			box-shadow: 0 6rpx 16rpx rgba(16, 157, 88, 0.3);
			transform: scale(1);
		}

		50% {
			background-color: #0e8a4f; /* 稍微深一点的绿色用于动画效果 */
			box-shadow: 0 8rpx 20rpx rgba(16, 157, 88, 0.5);
			transform: scale(1.02);
		}

		100% {
			background-color: #109d58; /* 回到原始绿色 */
			box-shadow: 0 6rpx 16rpx rgba(16, 157, 88, 0.3);
			transform: scale(1);
		}
	}

	.voice-icon {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.mic-icon {
		font-size: 32rpx;
		color: #ffffff;
	}

	.voice-text {
		font-size: 34rpx;
		color: #ffffff;
		font-weight: 600;
		letter-spacing: 1rpx;
	}

	/* 聊天历史样式 */
	.history-list {
		flex: 1;
		padding: 0;
		background-color: #ffffff;
		overflow-y: auto;
	}

	.history-item {
		display: flex;
		align-items: flex-start;
		padding: 24rpx 32rpx;
		border-bottom: 1rpx solid #eeeeee;
		/* 调整分割线颜色 */
		background-color: #ffffff;
		/* 保持白色背景，在浅灰色背景上形成卡片效果 */
		transition: background-color 0.2s ease;
		position: relative;
		margin: 8rpx 16rpx;
		/* 添加外边距，形成卡片效果 */
		border-radius: 12rpx;
		/* 添加圆角 */
		box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
		/* 添加轻微阴影 */
	}

	.history-item:hover {
		background-color: #f8f9fa;
		/* 调整hover颜色 */
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
		/* 增强阴影 */
	}

	.history-item:last-child {
		border-bottom: none;
	}

	.history-avatar {
		width: 88rpx;
		height: 88rpx;
		border-radius: 50%;
		overflow: hidden;
		margin-right: 20rpx;
		flex-shrink: 0;
		border: 1rpx solid #e8e8e8;
	}

	.history-avatar .avatar-img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.history-content {
		flex: 1;
		min-width: 0;
		padding-top: 2rpx;
	}

	.history-header {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.doctor-name {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
		margin-right: 12rpx;
	}

	.doctor-dept {
		font-size: 20rpx;
		color: #52c41a;
		background-color: #f6ffed;
		padding: 4rpx 12rpx;
		border-radius: 12rpx;
		border: 1rpx solid #d9f7be;
		font-weight: 400;
	}

	.history-message {
		font-size: 28rpx;
		color: #999999;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-top: 8rpx;
		max-width: 100%;
		display: block;
	}

	.history-meta {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		gap: 12rpx;
		flex-shrink: 0;
		padding-top: 4rpx;
	}

	.history-time {
		font-size: 24rpx;
		color: #cccccc;
		font-weight: 400;
	}

	.edit-icon {
		width: 44rpx;
		height: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 8rpx;
		background-color: transparent;
		transition: all 0.2s ease;
		border: 1rpx solid #e8e8e8;
	}

	.edit-icon:hover {
		background-color: #f0f0f0;
		border-color: #d0d0d0;
		transform: scale(1.05);
	}

	.edit-text {
		font-size: 20rpx;
		color: #999999;
	}

	/* 录音弹窗样式 */
	.recording-modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 9999;
		pointer-events: auto;
	}

	.recording-modal {
		width: 640rpx;
		background-color: #ffffff;
		border-radius: 32rpx;
		padding: 40rpx 32rpx 32rpx 32rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
		position: absolute;
		left: 50%;
		bottom: 180rpx;
		/* 距离底部240rpx，确保在Tabbar(~100rpx)和语音输入区域(~140rpx)之上 */
		transform: translateX(-50%);
		/* 水平居中 */
		transition: all 0.3s ease;
	}

	.recording-modal.slide-up {
		background-color: #fff5f5;
		border: 2rpx solid #ff4757;
		/* 移除位置变化，弹窗保持在原位置不动 */
		/* bottom: 260rpx; */
		/* transform: translateX(-50%); */
	}

	/* 录音内容区域 */
	.recording-content {
		display: flex;
		align-items: flex-start;
		justify-content: space-between;
		margin-bottom: 40rpx;
	}

	/* 左侧区域 */
	.left-section {
		display: flex;
		align-items: flex-start;
		gap: 24rpx;
	}

	.mic-icon {
		width: 80rpx;
		height: 80rpx;
		background-color: #4cd964;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
	}

	.delete-icon {
		width: 80rpx;
		height: 80rpx;
		background-color: #ff4757;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		transition: all 0.3s ease;
	}

	.delete-icon.active {
		background-color: #ff3742;
		transform: scale(1.1);
	}

	/* 右侧区域 */
	.right-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 8rpx;
	}

	.cancel-icon {
		width: 32rpx;
		height: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.arrow-up {
		font-size: 24rpx;
		color: #999999;
		line-height: 1;
	}

	.cancel-text {
		font-size: 20rpx;
		color: #999999;
		line-height: 1;
	}

	.recording-info {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
	}

	.time-text {
		font-size: 36rpx;
		font-weight: 600;
		color: #333333;
		line-height: 1;
	}

	.status-row {
		display: flex;
		align-items: center;
		gap: 12rpx;
	}

	.status-dot {
		width: 12rpx;
		height: 12rpx;
		background-color: #4cd964; /* 正常录音时绿色 */
		border-radius: 50%;
		animation: status-blink 1.5s infinite;
		transition: background-color 0.3s ease; /* 添加颜色过渡动画 */
	}

	/* 取消录音时的红色状态点 */
	.status-dot.cancel {
		background-color: #ff4757; /* 取消录音时红色 */
	}

	@keyframes status-blink {

		0%,
		50% {
			opacity: 1;
		}

		51%,
		100% {
			opacity: 0.3;
		}
	}

	.status-text {
		font-size: 24rpx;
		color: #4cd964; /* 正常录音时绿色 */
		line-height: 1;
		transition: color 0.3s ease; /* 添加颜色过渡动画 */
	}

	/* 取消录音时的红色状态文字 */
	.status-text.cancel {
		color: #ff4757; /* 取消录音时红色 */
	}

	/* 右侧区域 */
	.right-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 8rpx;
	}

	.cancel-icon {
		width: 32rpx;
		height: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.arrow-up {
		font-size: 24rpx;
		color: #999999;
		line-height: 1;
	}

	.cancel-text {
		font-size: 20rpx;
		color: #999999;
		line-height: 1;
	}

	/* 波浪线容器 */
	.wave-container {
		width: 100%;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 20rpx;
	}

	.wave-line {
		width: 100%;
		height: 6rpx;
		background-color: #4cd964;
		border-radius: 3rpx;
		position: relative;
		overflow: hidden;
	}

	.wave-line::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg,
				#4cd964 0%,
				#4cd964 30%,
				rgba(76, 217, 100, 0.5) 50%,
				#4cd964 70%,
				#4cd964 100%);
		animation: wave-flow 2s infinite ease-in-out;
	}

	@keyframes wave-flow {
		0% {
			background: linear-gradient(90deg,
					#4cd964 0%,
					#4cd964 20%,
					rgba(76, 217, 100, 0.5) 40%,
					#4cd964 60%,
					#4cd964 100%);
		}

		50% {
			background: linear-gradient(90deg,
					rgba(76, 217, 100, 0.5) 0%,
					#4cd964 20%,
					#4cd964 50%,
					rgba(76, 217, 100, 0.5) 80%,
					#4cd964 100%);
		}

		100% {
			background: linear-gradient(90deg,
					#4cd964 0%,
					rgba(76, 217, 100, 0.5) 30%,
					#4cd964 50%,
					#4cd964 80%,
					rgba(76, 217, 100, 0.5) 100%);
		}
	}

	/* 顶部上滑取消提示条 */
	.top-cancel-hint {
		position: fixed;
		top: 460rpx;
		/* 调整到医生卡片上方的位置 */
		left: 50%;
		transform: translateX(-50%);
		z-index: 10001;
		/* 确保在录音弹窗之上 */
		animation: slide-down 0.3s ease;
	}

	.cancel-hint-content {
		background-color: #ff4757;
		padding: 16rpx 32rpx;
		border-radius: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.3);
	}

	.cancel-hint-arrow {
		color: #ffffff;
		font-size: 28rpx;
		font-weight: bold;
		margin-right: 12rpx;
		animation: arrow-bounce 1s infinite;
	}

	.cancel-hint-message {
		color: #ffffff;
		font-size: 28rpx;
		font-weight: 500;
		white-space: nowrap;
	}

	/* 顶部提示条动画 */
	@keyframes slide-down {
		0% {
			opacity: 0;
			transform: translateX(-50%) translateY(-20rpx);
		}

		100% {
			opacity: 1;
			transform: translateX(-50%) translateY(0);
		}
	}

	/* RTL布局支持 */
	.rtl-container {
		.sidebar {
			left: auto;
			right: 0;
			transform: translateX(100%);
			border-radius: 20rpx 0 0 20rpx;
		}

		.sidebar-open {
			transform: translateX(0);
		}

		.sidebar-header {
			flex-direction: row-reverse;
		}

		.sidebar-title {
			text-align: right;
		}

		.history-item {
			flex-direction: row-reverse;
		}

		.history-avatar {
			margin-right: 0;
			margin-left: 20rpx;
		}

		.history-content {
			text-align: right;
		}

		.history-meta {
			align-items: flex-start;
		}

		.doctor-header-fixed {
			flex-direction: row-reverse;
		}

		.doctor-avatar {
			order: 3; /* 头像显示在最右边 */
			margin-right: 0;
			margin-left: 30rpx;
		}

		.doctor-info {
			order: 2; /* 医生信息在中间 */
			text-align: right;
		}

		.expand-icon {
			order: 1; /* 新建按钮在最左边 */
			left: 40rpx;
			right: auto;
		}

		.input-tools {
			flex-direction: row-reverse;
		}

		.keyboard-input-tools {
			flex-direction: row-reverse;
		}

		.recording-content {
			flex-direction: row-reverse;
		}

		.left-section {
			flex-direction: row-reverse;
		}

		.right-section {
			order: 1;
		}

		.cancel-hint-content {
			flex-direction: row-reverse;
		}

		.cancel-hint-arrow {
			margin-right: 0;
			margin-left: 12rpx;
		}
	}

	/* 暗色主题样式 */
	.dark-theme {
		background-color: #1a1a1a !important;
	}

	.dark-theme .page {
		background-color: #1a1a1a !important;
	}

	.dark-theme .sidebar {
		background-color: #2d2d2d !important;
	}

	.dark-theme .sidebar-title {
		color: #ffffff !important;
	}

	.dark-theme .history-item {
		background-color: #333333 !important;
		border-bottom-color: #404040 !important;
	}

	.dark-theme .doctor-name {
		color: #ffffff !important;
	}

	.dark-theme .doctor-dept {
		color: #b0b0b0 !important;
	}

	.dark-theme .history-message {
		color: #e0e0e0 !important;
	}

	.dark-theme .history-time {
		color: #888888 !important;
	}

	.dark-theme .empty-title {
		color: #ffffff !important;
	}

	.dark-theme .empty-subtitle {
		color: #b0b0b0 !important;
	}

	.dark-theme .clear-history-btn {
		background-color: #404040 !important;
		color: #ffffff !important;
	}

	.dark-theme .chat-container {
		background-color: #2d2d2d !important;
	}

	.dark-theme .input-container {
		background-color: #333333 !important;
		border-top-color: #404040 !important;
	}

	.dark-theme .input-box {
		background-color: #404040 !important;
		color: #ffffff !important;
	}

	.dark-theme .voice-btn {
		background-color: #4caf50 !important;
	}

	.dark-theme .send-btn {
		background-color: #4caf50 !important;
	}

	/* 字体大小响应式样式 */
	.font-size-small {
		.sidebar-title {
			font-size: 32rpx !important;
		}

		.doctor-name {
			font-size: 28rpx !important;
		}

		.doctor-dept {
			font-size: 24rpx !important;
		}

		.history-message {
			font-size: 26rpx !important;
		}

		.history-time {
			font-size: 22rpx !important;
		}

		.empty-title {
			font-size: 32rpx !important;
		}

		.empty-subtitle {
			font-size: 26rpx !important;
		}

		.clear-history-btn {
			font-size: 26rpx !important;
		}

		.input-box {
			font-size: 28rpx !important;
		}

		.message-text {
			font-size: 28rpx !important;
		}

		.doctor-info .doctor-name {
			font-size: 28rpx !important;
		}

		.doctor-info .doctor-dept {
			font-size: 24rpx !important;
		}
	}

	.font-size-medium {
		.sidebar-title {
			font-size: 36rpx !important;
		}

		.doctor-name {
			font-size: 32rpx !important;
		}

		.doctor-dept {
			font-size: 28rpx !important;
		}

		.history-message {
			font-size: 30rpx !important;
		}

		.history-time {
			font-size: 26rpx !important;
		}

		.empty-title {
			font-size: 36rpx !important;
		}

		.empty-subtitle {
			font-size: 30rpx !important;
		}

		.clear-history-btn {
			font-size: 30rpx !important;
		}

		.input-box {
			font-size: 32rpx !important;
		}

		.message-text {
			font-size: 32rpx !important;
		}

		.doctor-info .doctor-name {
			font-size: 32rpx !important;
		}

		.doctor-info .doctor-dept {
			font-size: 28rpx !important;
		}
	}

	.font-size-large {
		.sidebar-title {
			font-size: 40rpx !important;
		}

		.doctor-name {
			font-size: 36rpx !important;
		}

		.doctor-dept {
			font-size: 32rpx !important;
		}

		.history-message {
			font-size: 34rpx !important;
		}

		.history-time {
			font-size: 30rpx !important;
		}

		.empty-title {
			font-size: 40rpx !important;
		}

		.empty-subtitle {
			font-size: 34rpx !important;
		}

		.clear-history-btn {
			font-size: 34rpx !important;
		}

		.input-box {
			font-size: 36rpx !important;
		}

		.message-text {
			font-size: 36rpx !important;
		}

		.doctor-info .doctor-name {
			font-size: 36rpx !important;
		}

		.doctor-info .doctor-dept {
			font-size: 32rpx !important;
		}
	}

	@keyframes arrow-bounce {

		0%,
		100% {
			transform: translateY(0);
		}

		50% {
			transform: translateY(-4rpx);
		}
	}

	/* 登录状态测试区域样式 */
	.login-status-test {
		background: linear-gradient(135deg, #f8f9fa, #e9ecef);
		border-radius: 20rpx;
		padding: 30rpx;
		margin: 20rpx;
		border: 2rpx solid #dee2e6;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.status-header {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.status-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #495057;
		margin-bottom: 8rpx;
	}

	.status-subtitle {
		font-size: 26rpx;
		color: #6c757d;
	}

	.user-info-test {
		display: flex;
		align-items: center;
		background: white;
		border-radius: 15rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	}

	.user-avatar-test {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		margin-right: 20rpx;
		border: 2rpx solid #e9ecef;
	}

	.user-details-test {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.user-name-test {
		font-size: 30rpx;
		font-weight: bold;
		color: #212529;
		margin-bottom: 8rpx;
	}

	.user-phone-test {
		font-size: 26rpx;
		color: #6c757d;
	}

	.btn-group {
		display: flex;
		gap: 10rpx;
		margin-top: 10rpx;
	}

	.refresh-btn, .debug-btn {
		background: #007AFF;
		color: white;
		border: none;
		border-radius: 8rpx;
		padding: 8rpx 16rpx;
		font-size: 24rpx;
		flex: 1;
	}

	.debug-btn {
		background: #FF9500;
	}

	.refresh-btn:active, .debug-btn:active {
		opacity: 0.8;
	}
</style>