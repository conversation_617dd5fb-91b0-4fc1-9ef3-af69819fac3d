// 字体大小混入工具
import { computed, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/store/app.js'

export const useFontSize = () => {
  const appStore = useAppStore()
  
  // 计算当前字体大小类名
  const fontSizeClass = computed(() => {
    return `font-size-${appStore.fontSize}`
  })
  
  // 计算字体大小样式对象
  const fontSizeStyle = computed(() => {
    const fontSizes = appStore.getFontSizeValues(appStore.fontSize)
    return fontSizes
  })
  
  // 字体大小变化处理函数
  const handleFontSizeChange = (data) => {
    console.log('页面接收到字体大小变化:', data.fontSize)
    // 强制更新组件
    if (typeof getCurrentInstance === 'function') {
      const instance = getCurrentInstance()
      if (instance && instance.proxy && instance.proxy.$forceUpdate) {
        instance.proxy.$forceUpdate()
      }
    }
  }
  
  onMounted(() => {
    // 监听字体大小变化事件
    uni.$on('fontSizeChanged', handleFontSizeChange)
  })
  
  onUnmounted(() => {
    // 移除字体大小变化监听
    uni.$off('fontSizeChanged', handleFontSizeChange)
  })
  
  return {
    fontSizeClass,
    fontSizeStyle,
    currentFontSize: computed(() => appStore.fontSize)
  }
}

// 传统选项式API的mixin
export const fontSizeMixin = {
  data() {
    return {
      fontSizeUpdateKey: 0
    }
  },
  computed: {
    fontSizeClass() {
      // 添加响应式依赖
      this.fontSizeUpdateKey
      const appStore = useAppStore()
      return `font-size-${appStore.fontSize}`
    },
    fontSizeStyle() {
      // 添加响应式依赖
      this.fontSizeUpdateKey
      const appStore = useAppStore()
      return appStore.getFontSizeValues(appStore.fontSize)
    },
    currentFontSize() {
      // 添加响应式依赖
      this.fontSizeUpdateKey
      const appStore = useAppStore()
      return appStore.fontSize
    }
  },
  mounted() {
    // 监听字体大小变化事件
    uni.$on('fontSizeChanged', this.handleFontSizeChange)
  },
  beforeDestroy() {
    // 移除字体大小变化监听
    uni.$off('fontSizeChanged', this.handleFontSizeChange)
  },
  methods: {
    handleFontSizeChange(data) {
      console.log('页面接收到字体大小变化:', data.fontSize)
      // 强制更新响应式数据
      this.fontSizeUpdateKey++
      // 强制更新组件
      this.$forceUpdate()
    }
  }
}

// 获取字体大小标签的工具函数
export const getFontSizeLabel = (size) => {
  const labels = {
    small: '小',
    medium: '中',
    large: '大'
  }
  return labels[size] || '中'
}

// 应用字体大小到元素的工具函数
export const applyFontSizeToElement = (element, size) => {
  if (!element) return
  
  const appStore = useAppStore()
  const fontSizes = appStore.getFontSizeValues(size)
  
  Object.keys(fontSizes).forEach(key => {
    element.style.setProperty(key, fontSizes[key])
  })
}
