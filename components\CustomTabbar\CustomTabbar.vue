<template>
	<!--
		📱 自定义底部导航栏组件模板
		这是应用底部的主导航栏，用户可以通过它在不同页面间切换
		包含：首页、列表、个人中心等主要功能入口
		支持动态样式、多语言、字体大小调整等功能
	-->

	<!--
		📄 底部导航栏容器
		- class="tabbar": 基础导航栏样式
		- :class="fontSizeClass": 动态字体大小样式类
		- :key: 强制重新渲染的键，当字体大小变化时重新渲染
		- :style: 内联样式设置：
		  * paddingBottom: '0': 底部内边距为0
		  * marginBottom: '0': 底部外边距为0
		  * height: '162rpx': 导航栏高度
		  * --window-bottom: '0': CSS变量，窗口底部距离
		  * --safe-area-inset-bottom: '0': 安全区域底部距离
		  * zIndex: 999: 层级设置，确保导航栏在最上层
	-->
	<view class="tabbar" :class="fontSizeClass" :key="fontSizeUpdateKey"
		:style="{paddingBottom: '0', marginBottom: '0', height: '162rpx','--window-bottom': '0','--safe-area-inset-bottom': '0',zIndex: 999}">

		<!--
			🔄 标签项循环
			v-for: 遍历tabItems数组，为每个标签项创建导航按钮
			:key: 每个标签项的唯一标识
		-->
		<view
			v-for="tab in tabItems"
			:key="tab.key"
			class="tabbar_list"
			@click="jumToPage(tab.page)"
			:class="{active: current === tab.page}"
		>
			<!-- 标签内容容器 -->
			<view class="tabbar-content">
				<!--
					🎨 标签图标区域
					显示每个导航项的图标
				-->
				<view class="tabbar-icon">
					<!--
						图标容器
						:class: 动态样式类数组
						- tab.iconClass: 每个标签的基础图标样式类
						- {active: current === tab.page}: 当前选中时添加active样式
					-->
					<view :class="[tab.iconClass, {active: current === tab.page}]">
						<!--
							图标文字
							:class: 图标文字的样式类
							tab.iconText: 图标的文字内容（通常是符号或emoji）
						-->
						<text :class="tab.iconTextClass">{{ tab.iconText }}</text>
					</view>
				</view>

				<!--
					📝 标签标题
					- class="ug": 维吾尔文字体样式
					- $t(tab.titleKey): 从语言包获取标签标题文字
				-->
				<view class="tabbar_list-title ug">{{ $t(tab.titleKey) }}</view>
			</view>
		</view>
	</view>
</template>

<!--
	🔧 JavaScript 逻辑部分
	使用 Vue 3 的 Composition API 语法
-->
<script setup>
	// ==================== 📦 导入依赖 ====================

	// Vue 3 核心功能导入
	import {ref,onMounted} from 'vue'  // 响应式引用和生命周期钩子

	// uni-app 生命周期导入
	import {onShow} from '@dcloudio/uni-app'  // 页面显示时的生命周期

	// 工具函数导入
	import { useFontSizePage } from '@/utils/fontSizeMixin.js';  // 字体大小控制混入
	import { t } from '@/locale/index.js';  // 国际化翻译函数

	// 注释掉的国际化导入（项目使用自定义国际化方案）
	// import { useI18n } from 'vue-i18n';
	// const { t } = useI18n();

	// 注释掉的状态管理导入（当前组件暂未使用）
	// import {useAppStore} from '@/store/app.js';
	// const appStore = useAppStore(); // 状态管理

	// ==================== 📊 状态和工具初始化 ====================

	// 使用字体大小功能混入
	// fontSizeClass: 当前字体大小对应的CSS类名
	// fontSizeUpdateKey: 字体大小更新时的强制重渲染键
	const { fontSizeClass, fontSizeUpdateKey } = useFontSizePage()

	// 翻译函数封装
	// 将导入的t函数封装为$t，保持与模板中使用习惯一致
	const $t = (key) => t(key);

	// ==================== 📊 组件状态 ====================

	// 当前选中的页面
	// 默认选中首页('index')，用于高亮显示当前所在页面的导航项
	const current = ref('index');

	// ==================== 📋 导航配置 ====================

	// 标签项配置数组
	// 定义底部导航栏的所有标签项，包括图标、页面路径、标题等信息
	const tabItems = [
		{
			key: 'home',              // 唯一标识
			page: 'index',            // 对应的页面路径
			iconClass: 'health-shield', // 图标样式类
			iconTextClass: 'shield-plus', // 图标文字样式类
			iconText: '+',
			titleKey: 'nav.home'
		},
		{
			key: 'list',
			page: 'function',
			iconClass: 'list-icon',
			iconTextClass: 'list-lines',
			iconText: '☰',
			titleKey: 'nav.list'
		},
		{
			key: 'my',
			page: 'My',
			iconClass: 'user-icon',
			iconTextClass: 'user-avatar',
			iconText: '👤',
			titleKey: 'nav.my'
		}
	];

	// 页面跳转方法
	const jumToPage = (page) => {
		current.value = page; //更新当前选中状态
		let jump_path = '';

		switch (page) {
			case 'index':
				jump_path = '/pages/index/index';
				break;
			case 'function':
				jump_path = '/pages/List/List';
				break;
			case 'My':
				jump_path = '/pages/My/My';
				break;
		}
		uni.switchTab({
			url: jump_path,
			fail: (res) => {
				console.error("跳转失败:", res);
			}
		});
	}

	// 获取当前页面并且设置选中状态
	function updateCurrent() {
		const pages = getCurrentPages();
		if (!pages.length) return;

		const page = pages[pages.length - 1];
		const route = page.route || page.__route__;
		if (!route) return;

		// 从路由路径中提出页面名称
		const pageName = route.split('/').pop();
		current.value = pageName;
	}

	onMounted(updateCurrent);
	onShow(updateCurrent);
</script>

<style lang="scss">
	.tabbar {
		position: fixed;
		bottom: 0;
		width: 100%;
		display: flex;
		justify-content: space-around;
		/* 改为space-around使按钮分布更均匀 */
		align-items: center;
		background: #ffffff;
		height: 120rpx;
		z-index: 999;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		border-top-left-radius: 32rpx;
		border-top-right-radius: 32rpx;

		/* 适配全面屏底部安全区 */
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	.tabbar_list {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100%;
		transition: all 0.3s ease;

		.tabbar-content {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			gap: 8rpx;
		}

		&-title {
			font-size: 24rpx;
			color: #999999;
			transition: all 0.3s ease;
		}

		/* 选中状态样式 */
		&.active {
			.tabbar_list-title {
				color: #4cd964;
				font-weight: 500;
			}
		}
	}

	/* 图标容器 */
	.tabbar-icon {
		width: 56rpx;
		height: 56rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 4rpx;
	}

	/* 健康助手盾牌图标 */
	.health-shield {
		width: 56rpx;
		height: 56rpx;
		background-color: #e8f5e8;
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		transition: all 0.3s ease;

		&::before {
			content: '';
			position: absolute;
			top: -2rpx;
			left: 50%;
			transform: translateX(-50%);
			width: 20rpx;
			height: 8rpx;
			background-color: #e8f5e8;
			border-radius: 10rpx 10rpx 0 0;
		}

		&.active {
			background-color: #4cd964;

			&::before {
				background-color: #4cd964;
			}

			.shield-plus {
				color: #ffffff;
			}
		}
	}

	.shield-plus {
		font-size: 32rpx;
		font-weight: bold;
		color: #4cd964;
		transition: all 0.3s ease;
	}

	/* 列表图标 */
	.list-icon {
		width: 56rpx;
		height: 56rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.list-lines {
		font-size: 36rpx;
		color: #999999;
		transition: all 0.3s ease;
	}

	.list-icon.active .list-lines {
		color: #4cd964;
	}

	/* 用户图标 */
	.user-icon {
		width: 56rpx;
		height: 56rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.user-avatar {
		font-size: 36rpx;
		color: #999999;
		transition: all 0.3s ease;
	}

	.user-icon.active .user-avatar {
		color: #4cd964;
	}

	/* 字体大小响应式样式 */
	.font-size-small {
		.tabbar_list-title {
			font-size: var(--font-size-sm) !important;
		}

		.shield-plus,
		.list-lines,
		.user-avatar {
			font-size: var(--font-size-lg) !important;
		}
	}

	.font-size-medium {
		.tabbar_list-title {
			font-size: var(--font-size-base) !important;
		}

		.shield-plus,
		.list-lines,
		.user-avatar {
			font-size: var(--font-size-xl) !important;
		}
	}

	.font-size-large {
		.tabbar_list-title {
			font-size: var(--font-size-md) !important;
		}

		.shield-plus,
		.list-lines,
		.user-avatar {
			font-size: var(--font-size-2xl) !important;
		}
	}

	/* RTL布局支持 */
	:deep(.rtl-container) {
		.tabbar {
			flex-direction: row-reverse; /* 标签栏按钮顺序反转 */
		}
	}
</style>