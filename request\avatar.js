// 头像上传相关API
import { updateUserInfo } from '@/store/user.js'
import { BASE_URL } from '@/main.js'

/**
 * 处理头像URL，确保使用正确的域名
 * @param {string} avatarUrl - 原始头像URL
 * @returns {string} 处理后的正确头像URL
 */
export const processAvatarUrl = (avatarUrl) => {
  if (!avatarUrl) return '/static/icon/user.svg'

  // 🔧 修复错误的域名 - 将127.0.0.1:8002替换为正确的域名
  if (avatarUrl.includes('127.0.0.1:8002')) {
    avatarUrl = avatarUrl.replace('https://127.0.0.1:8002', 'https://appdava.sulmas.com.cn')
    avatarUrl = avatarUrl.replace('http://127.0.0.1:8002', 'https://appdava.sulmas.com.cn')
    console.log('🔧 修复头像域名:', avatarUrl)
    return avatarUrl
  }

  // 如果是HTTP协议，转换为HTTPS
  if (avatarUrl.startsWith('http://')) {
    return avatarUrl.replace('http://', 'https://')
  }

  // 如果已经是HTTPS或data协议，直接返回
  if (avatarUrl.startsWith('https://') || avatarUrl.startsWith('data:')) {
    return avatarUrl
  }

  // 如果是以/file/开头的相对路径，添加域名
  if (avatarUrl.startsWith('/file/')) {
    return `https://appdava.sulmas.com.cn${avatarUrl}`
  }

  // 如果只是文件名，添加完整路径
  // 根据API响应格式，头像文件存储在 /file/user_avatar/ 目录下
  // 🔧 修复双斜杠问题：确保avatarUrl不以斜杠开头
  const cleanAvatarUrl = avatarUrl.startsWith('/') ? avatarUrl.substring(1) : avatarUrl
  return `https://appdava.sulmas.com.cn/file/user_avatar/${cleanAvatarUrl}`
}

/**
 * 处理产品图片URL，确保使用正确的域名
 * @param {string} imageUrl - 原始图片URL
 * @returns {string} 处理后的正确图片URL
 */
export const processProductImageUrl = (imageUrl) => {
  if (!imageUrl) return '/static/icon/user.svg'

  // 🔧 修复错误的域名 - 将127.0.0.1:8002替换为正确的域名
  if (imageUrl.includes('127.0.0.1:8002')) {
    imageUrl = imageUrl.replace('https://127.0.0.1:8002', 'https://appdava.sulmas.com.cn')
    imageUrl = imageUrl.replace('http://127.0.0.1:8002', 'https://appdava.sulmas.com.cn')
    console.log('🔧 修复产品图片域名:', imageUrl)
    return imageUrl
  }

  // 如果是HTTP协议，转换为HTTPS
  if (imageUrl.startsWith('http://')) {
    return imageUrl.replace('http://', 'https://')
  }

  // 如果已经是HTTPS或data协议，直接返回
  if (imageUrl.startsWith('https://') || imageUrl.startsWith('data:')) {
    return imageUrl
  }

  // 如果是以/file/开头的相对路径，添加域名
  if (imageUrl.startsWith('/file/')) {
    return `https://appdava.sulmas.com.cn${imageUrl}`
  }

  // 如果只是文件名，添加完整路径
  // 根据API响应格式，产品图片存储在 /file/product_images/ 目录下
  const cleanImageUrl = imageUrl.startsWith('/') ? imageUrl.substring(1) : imageUrl
  return `https://appdava.sulmas.com.cn/file/product_images/${cleanImageUrl}`
}

/**
 * 上传头像到服务器
 * @param {string} tempFilePath - 临时文件路径
 * @param {Object} userInfo - 用户信息
 * @returns {Promise<string>} 服务器头像URL
 */
export const uploadAvatar = (tempFilePath, userInfo = {}) => {
  return new Promise((resolve, reject) => {
    // 显示上传进度
    uni.showLoading({
      title: '上传头像中...'
    })

    // 获取token
    const token = uni.getStorageSync('auth_token')
    
    uni.uploadFile({
      url: `${BASE_URL}upload/avatar`, // 使用实际的API域名
      filePath: tempFilePath,
      name: 'avatar',
      header: {
        'Authorization': token ? `Bearer ${token}` : '',
        'Content-Type': 'multipart/form-data'
      },
      formData: {
        userId: userInfo.id || uni.getStorageSync('userId') || '', // 用户ID
        type: 'avatar',
        timestamp: Date.now().toString()
      },
      success: (uploadRes) => {
        try {
          const result = JSON.parse(uploadRes.data)
          console.log('头像上传结果:', result)
          
          if (result.code === 200 || result.success) {
            const avatarUrl = result.data?.url || result.url || result.data
            
            uni.hideLoading()
            uni.showToast({
              title: '头像上传成功',
              icon: 'success'
            })
            
            // 同步更新全局用户信息
            syncAvatarToGlobalState(avatarUrl)
            
            resolve(avatarUrl)
          } else {
            throw new Error(result.message || '上传失败')
          }
        } catch (parseError) {
          console.error('解析上传结果失败:', parseError)
          uni.hideLoading()
          reject(new Error('上传结果解析失败'))
        }
      },
      fail: (error) => {
        console.error('头像上传请求失败:', error)
        uni.hideLoading()
        
        // 如果是开发环境，使用本地存储作为备用方案
        if (process.env.NODE_ENV === 'development') {
          console.log('开发环境：使用本地存储方案')
          saveAvatarToLocal(tempFilePath)
            .then(localPath => {
              // 同步更新全局用户信息
              syncAvatarToGlobalState(localPath)
              resolve(localPath)
            })
            .catch(err => reject(err))
        } else {
          reject(new Error('网络错误，请检查网络连接'))
        }
      }
    })
  })
}

/**
 * 保存头像到本地存储（开发环境备用方案）
 * @param {string} tempFilePath - 临时文件路径
 * @returns {Promise<string>} 本地文件路径
 */
export const saveAvatarToLocal = (tempFilePath) => {
  return new Promise((resolve, reject) => {
    uni.showLoading({
      title: '处理头像中...'
    })

    // 使用 saveFile 保存到本地
    uni.saveFile({
      tempFilePath: tempFilePath,
      success: (res) => {
        // 使用通用函数处理头像URL
        const processedPath = processAvatarUrl(res.savedFilePath);

        console.log('头像保存到本地成功:', processedPath)
        uni.hideLoading()
        uni.showToast({
          title: '头像更新成功',
          icon: 'success'
        })
        resolve(processedPath)
      },
      fail: (err) => {
        console.error('头像保存到本地失败:', err)
        // 备用方案：使用文件系统管理器
        try {
          const fs = uni.getFileSystemManager()
          const timestamp = Date.now()
          const localPath = `${uni.env.USER_DATA_PATH}/avatar_${timestamp}.jpg`
          
          fs.copyFile({
            srcPath: tempFilePath,
            destPath: localPath,
            success: () => {
              console.log('头像复制成功:', localPath)
              uni.hideLoading()
              uni.showToast({
                title: '头像更新成功',
                icon: 'success'
              })
              resolve(localPath)
            },
            fail: (copyErr) => {
              console.error('头像复制失败:', copyErr)
              // 最后的备用方案：直接使用临时路径
              uni.hideLoading()
              uni.showToast({
                title: '头像更新成功（临时）',
                icon: 'success'
              })
              resolve(tempFilePath)
            }
          })
        } catch (fsError) {
          console.error('文件系统操作失败:', fsError)
          uni.hideLoading()
          uni.showToast({
            title: '头像更新成功',
            icon: 'success'
          })
          resolve(tempFilePath)
        }
      }
    })
  })
}

/**
 * 同步用户信息到全局状态管理
 * @param {string} avatarUrl - 头像URL
 * @param {string} nickname - 昵称（可选）
 */
export const syncUserInfoToGlobalState = (avatarUrl, nickname = null) => {
  try {
    // 获取当前用户信息
    const currentUserInfo = uni.getStorageSync('userInfo') || {}

    // 使用通用函数处理头像URL
    const processedAvatarUrl = processAvatarUrl(avatarUrl);

    // 更新用户信息
    const newUserInfo = {
      ...currentUserInfo,
      avatar: processedAvatarUrl
    }

    // 如果提供了昵称，也更新昵称
    if (nickname && nickname.trim()) {
      newUserInfo.nickname = nickname.trim()
    }

    // 更新全局状态
    updateUserInfo(newUserInfo)

    // 更新本地存储
    uni.setStorageSync('userInfo', newUserInfo)

    console.log('用户信息已同步到全局状态:', newUserInfo)

    // 触发页面刷新事件
    uni.$emit('avatarUpdated', processedAvatarUrl)
    if (nickname) {
      uni.$emit('nicknameUpdated', nickname)
    }
    uni.$emit('userInfoUpdated', newUserInfo)

  } catch (error) {
    console.error('同步用户信息到全局状态失败:', error)
  }
}

/**
 * 同步头像到全局状态管理（向后兼容）
 * @param {string} avatarUrl - 头像URL
 */
export const syncAvatarToGlobalState = (avatarUrl) => {
  syncUserInfoToGlobalState(avatarUrl)
}

/**
 * 选择并上传头像的完整流程
 * @param {Object} options - 选项配置
 * @returns {Promise<string>} 头像URL
 */
export const chooseAndUploadAvatar = (options = {}) => {
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count: 1, // 只选择一张图片
      sizeType: ['compressed'], // 压缩图片
      sourceType: ['album', 'camera'], // 支持相册和相机
      success: async (res) => {
        const tempFilePath = res.tempFilePaths[0]
        console.log('选择的头像:', tempFilePath)
        
        try {
          // 上传头像
          const avatarUrl = await uploadAvatar(tempFilePath, options.userInfo)
          resolve(avatarUrl)
        } catch (error) {
          console.error('头像上传失败:', error)
          uni.showToast({
            title: error.message || '头像上传失败',
            icon: 'none'
          })
          reject(error)
        }
      },
      fail: (err) => {
        console.error('选择头像失败:', err)
        uni.showToast({
          title: '选择头像失败',
          icon: 'none'
        })
        reject(new Error('选择头像失败'))
      }
    })
  })
}

/**
 * 处理头像加载错误
 * @param {Event} e - 错误事件
 * @param {Function} callback - 错误处理回调
 */
export const handleAvatarError = (e, callback) => {
  console.error('头像加载失败:', e)
  
  uni.showModal({
    title: '头像显示异常',
    content: '头像可能无法正常显示，是否重新选择头像？',
    confirmText: '重新选择',
    cancelText: '稍后再试',
    success: (res) => {
      if (res.confirm && callback) {
        // 用户选择重新选择头像
        callback()
      }
    }
  })
}
