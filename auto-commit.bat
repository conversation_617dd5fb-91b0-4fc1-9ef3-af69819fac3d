@echo off
chcp 65001 >nul
echo 🚀 正在自动提交代码到Gitee...

REM 检查Git是否安装
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到Git，请先安装Git
    pause
    exit /b 1
)

REM 检查是否在Git仓库中
git rev-parse --git-dir >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  当前目录不是Git仓库，正在初始化...
    git init
    echo ✅ Git仓库初始化完成
)

REM 检查是否配置了远程仓库
git remote get-url origin >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  未配置远程仓库，请手动添加：
    echo    git remote add origin https://gitee.com/ablezz/HealthApp.git
    pause
    exit /b 1
)

REM 添加所有更改的文件
echo 📁 添加文件到暂存区...
git add .

REM 检查是否有更改
git diff --staged --quiet
if %errorlevel% equ 0 (
    echo ℹ️  没有检测到代码更改
    pause
    exit /b 0
)

REM 生成提交信息（包含时间戳）
for /f "tokens=1-4 delims=/ " %%i in ('date /t') do set mydate=%%i-%%j-%%k
for /f "tokens=1-2 delims=: " %%i in ('time /t') do set mytime=%%i:%%j
set commit_msg=自动提交: %mydate% %mytime%

REM 提交代码
echo 💾 提交代码...
git commit -m "%commit_msg%"

REM 推送到Gitee
echo 📤 推送到Gitee...
git push origin master
if %errorlevel% equ 0 (
    echo ✅ 代码已成功上传到Gitee!
) else (
    echo ❌ 推送失败，请检查网络连接和仓库权限
)

pause
