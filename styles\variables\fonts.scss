// 统一的字体变量系统
// 整合了项目中所有字体相关的定义

// ==================== 字体族定义 ====================
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
$font-family-uyghur: 'uy', 'UKIJ Tuz Tom', 'UKIJ Tuz', 'Alkatip Basma Tom', 'Alkatip Basma', 'Microsoft Uighur', sans-serif;
$font-family-monospace: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', 'Source Code Pro', monospace;

// ==================== 字体大小 ====================
// 基础字体大小（rpx）
$font-size-xs: 20rpx;      // 极小
$font-size-sm: 24rpx;      // 小
$font-size-base: 28rpx;    // 基础
$font-size-md: 32rpx;      // 中等
$font-size-lg: 36rpx;      // 大
$font-size-xl: 40rpx;      // 特大
$font-size-xxl: 48rpx;     // 超大

// 标题字体大小
$font-size-h1: 48rpx;
$font-size-h2: 40rpx;
$font-size-h3: 36rpx;
$font-size-h4: 32rpx;
$font-size-h5: 28rpx;
$font-size-h6: 24rpx;

// ==================== 字体粗细 ====================
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// ==================== 行高 ====================
$line-height-tight: 1.2;
$line-height-normal: 1.4;
$line-height-relaxed: 1.6;
$line-height-loose: 1.8;

// ==================== 字间距 ====================
$letter-spacing-tight: -0.05em;
$letter-spacing-normal: 0;
$letter-spacing-wide: 0.05em;

// ==================== CSS变量映射 ====================
:root {
  // 字体族
  --font-family-base: #{$font-family-base};
  --font-family-uyghur: #{$font-family-uyghur};
  --font-family-monospace: #{$font-family-monospace};
  
  // 字体大小
  --font-size-xs: #{$font-size-xs};
  --font-size-sm: #{$font-size-sm};
  --font-size-base: #{$font-size-base};
  --font-size-md: #{$font-size-md};
  --font-size-lg: #{$font-size-lg};
  --font-size-xl: #{$font-size-xl};
  --font-size-xxl: #{$font-size-xxl};
  
  // 标题字体大小
  --font-size-h1: #{$font-size-h1};
  --font-size-h2: #{$font-size-h2};
  --font-size-h3: #{$font-size-h3};
  --font-size-h4: #{$font-size-h4};
  --font-size-h5: #{$font-size-h5};
  --font-size-h6: #{$font-size-h6};
  
  // 字体粗细
  --font-weight-light: #{$font-weight-light};
  --font-weight-normal: #{$font-weight-normal};
  --font-weight-medium: #{$font-weight-medium};
  --font-weight-semibold: #{$font-weight-semibold};
  --font-weight-bold: #{$font-weight-bold};
  
  // 行高
  --line-height-tight: #{$line-height-tight};
  --line-height-normal: #{$line-height-normal};
  --line-height-relaxed: #{$line-height-relaxed};
  --line-height-loose: #{$line-height-loose};
  
  // 字间距
  --letter-spacing-tight: #{$letter-spacing-tight};
  --letter-spacing-normal: #{$letter-spacing-normal};
  --letter-spacing-wide: #{$letter-spacing-wide};
}

// ==================== 字体大小响应式 ====================
// 小字体模式
.font-size-small {
  --font-size-xs: 18rpx;
  --font-size-sm: 22rpx;
  --font-size-base: 26rpx;
  --font-size-md: 30rpx;
  --font-size-lg: 34rpx;
  --font-size-xl: 38rpx;
  --font-size-xxl: 44rpx;
}

// 大字体模式
.font-size-large {
  --font-size-xs: 24rpx;
  --font-size-sm: 28rpx;
  --font-size-base: 32rpx;
  --font-size-md: 36rpx;
  --font-size-lg: 40rpx;
  --font-size-xl: 44rpx;
  --font-size-xxl: 52rpx;
}

// 超大字体模式
.font-size-extra-large {
  --font-size-xs: 28rpx;
  --font-size-sm: 32rpx;
  --font-size-base: 36rpx;
  --font-size-md: 40rpx;
  --font-size-lg: 44rpx;
  --font-size-xl: 48rpx;
  --font-size-xxl: 56rpx;
}

// ==================== 维吾尔文字体支持 ====================
.ug,
.lang-ug,
.uyghur-font {
  font-family: var(--font-family-uyghur) !important;

  // 确保所有文本元素都应用维吾尔文字体，但排除图标元素
  text:not(.fui-icon):not([class*="icon"]):not([class*="Icon"]),
  view:not(.fui-icon):not([class*="icon"]):not([class*="Icon"]),
  button:not(.fui-icon):not([class*="icon"]):not([class*="Icon"]),
  input, textarea, label,
  .uni-input-input, .uni-textarea-textarea, .uni-button-text {
    font-family: var(--font-family-uyghur) !important;
  }

  // placeholder文本
  input::placeholder,
  textarea::placeholder,
  .uni-input-placeholder,
  .uni-textarea-placeholder {
    font-family: var(--font-family-uyghur) !important;
  }
}

// ==================== 图标字体保护 ====================
// 确保图标字体不被维吾尔文字体覆盖
.fui-icon,
[class*="icon"],
[class*="Icon"],
.icon,
.iconfont {
  font-family: fuiFont, "iconfont", "Material Icons", "Font Awesome", sans-serif !important;
}

// FirstUI图标特殊保护
.fui-icon {
  font-family: fuiFont !important;
  font-style: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  line-height: 1 !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

// ==================== 字体工具类 ====================
// 字体大小工具类
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-xxl { font-size: var(--font-size-xxl); }

// 标题工具类
.text-h1 { font-size: var(--font-size-h1); font-weight: var(--font-weight-bold); }
.text-h2 { font-size: var(--font-size-h2); font-weight: var(--font-weight-bold); }
.text-h3 { font-size: var(--font-size-h3); font-weight: var(--font-weight-semibold); }
.text-h4 { font-size: var(--font-size-h4); font-weight: var(--font-weight-semibold); }
.text-h5 { font-size: var(--font-size-h5); font-weight: var(--font-weight-medium); }
.text-h6 { font-size: var(--font-size-h6); font-weight: var(--font-weight-medium); }

// 字体粗细工具类
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

// 行高工具类
.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

// ==================== 兼容性映射 ====================
// 为了兼容现有代码，保留一些旧的变量名
$uni-font-size-sm: $font-size-sm;
$uni-font-size-base: $font-size-base;
$uni-font-size-lg: $font-size-lg;
