// 用户信息同步相关API
import { updateUserInfo } from '@/store/user.js'

/**
 * 同步用户信息到全局状态和本地存储
 * @param {Object} userInfoData - 用户信息数据
 * @param {string} userInfoData.nickname - 昵称
 * @param {string} userInfoData.avatar - 头像URL
 * @param {string} userInfoData.phone - 手机号
 * @param {Object} userInfoData.other - 其他用户信息
 */
export const syncUserInfoToAll = (userInfoData) => {
  try {
    // 获取当前用户信息
    const currentUserInfo = uni.getStorageSync('userInfo') || {}
    
    // 合并用户信息
    const newUserInfo = {
      ...currentUserInfo,
      ...userInfoData
    }
    
    // 确保name字段与nickname保持一致
    if (newUserInfo.nickname) {
      newUserInfo.name = newUserInfo.nickname
    }
    
    // 更新全局状态
    updateUserInfo(newUserInfo)
    
    // 更新本地存储
    uni.setStorageSync('userInfo', {
      nickname: newUserInfo.nickname || newUserInfo.name || '',
      phone: newUserInfo.phone || '未绑定手机号',
      avatar: newUserInfo.avatar || '/static/icon/user.svg',
      ...userInfoData // 保存其他字段
    })
    
    console.log('用户信息已同步到全局状态和本地存储:', newUserInfo)
    
    // 触发各种更新事件
    if (userInfoData.avatar) {
      uni.$emit('avatarUpdated', userInfoData.avatar)
    }
    if (userInfoData.nickname) {
      uni.$emit('nicknameUpdated', userInfoData.nickname)
    }
    uni.$emit('userInfoUpdated', newUserInfo)
    
    return newUserInfo
    
  } catch (error) {
    console.error('同步用户信息失败:', error)
    throw error
  }
}

/**
 * 更新用户昵称
 * @param {string} nickname - 新昵称
 */
export const updateUserNickname = (nickname) => {
  if (!nickname || !nickname.trim()) {
    throw new Error('昵称不能为空')
  }
  
  return syncUserInfoToAll({
    nickname: nickname.trim()
  })
}

/**
 * 更新用户头像
 * @param {string} avatarUrl - 新头像URL
 */
export const updateUserAvatar = (avatarUrl) => {
  if (!avatarUrl) {
    throw new Error('头像URL不能为空')
  }
  
  return syncUserInfoToAll({
    avatar: avatarUrl
  })
}

/**
 * 批量更新用户信息
 * @param {Object} updates - 要更新的字段
 */
export const batchUpdateUserInfo = (updates) => {
  return syncUserInfoToAll(updates)
}

/**
 * 获取当前用户信息
 * @returns {Object} 用户信息
 */
export const getCurrentUserInfo = () => {
  try {
    const userInfo = uni.getStorageSync('userInfo') || {}
    return {
      nickname: userInfo.nickname || userInfo.name || '',
      phone: userInfo.phone || '未绑定手机号',
      avatar: userInfo.avatar || '/static/icon/user.svg',
      ...userInfo
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return {
      nickname: '',
      phone: '未绑定手机号',
      avatar: '/static/icon/user.svg'
    }
  }
}

/**
 * 监听用户信息更新事件
 * @param {Function} callback - 回调函数
 */
export const onUserInfoUpdated = (callback) => {
  uni.$on('userInfoUpdated', callback)
}

/**
 * 移除用户信息更新事件监听
 * @param {Function} callback - 回调函数
 */
export const offUserInfoUpdated = (callback) => {
  uni.$off('userInfoUpdated', callback)
}

/**
 * 监听昵称更新事件
 * @param {Function} callback - 回调函数
 */
export const onNicknameUpdated = (callback) => {
  uni.$on('nicknameUpdated', callback)
}

/**
 * 移除昵称更新事件监听
 * @param {Function} callback - 回调函数
 */
export const offNicknameUpdated = (callback) => {
  uni.$off('nicknameUpdated', callback)
}

/**
 * 监听头像更新事件
 * @param {Function} callback - 回调函数
 */
export const onAvatarUpdated = (callback) => {
  uni.$on('avatarUpdated', callback)
}

/**
 * 移除头像更新事件监听
 * @param {Function} callback - 回调函数
 */
export const offAvatarUpdated = (callback) => {
  uni.$off('avatarUpdated', callback)
}

/**
 * 清除所有用户信息更新事件监听
 */
export const clearAllUserInfoListeners = () => {
  uni.$off('userInfoUpdated')
  uni.$off('nicknameUpdated')
  uni.$off('avatarUpdated')
}

/**
 * 验证昵称格式
 * @param {string} nickname - 昵称
 * @returns {Object} 验证结果
 */
export const validateNickname = (nickname) => {
  if (!nickname || !nickname.trim()) {
    return {
      valid: false,
      message: '昵称不能为空'
    }
  }
  
  const trimmedNickname = nickname.trim()
  
  if (trimmedNickname.length < 1) {
    return {
      valid: false,
      message: '昵称不能为空'
    }
  }
  
  if (trimmedNickname.length > 20) {
    return {
      valid: false,
      message: '昵称不能超过20个字符'
    }
  }
  
  // 检查是否包含特殊字符（可根据需要调整）
  const specialChars = /[<>'"&]/
  if (specialChars.test(trimmedNickname)) {
    return {
      valid: false,
      message: '昵称不能包含特殊字符'
    }
  }
  
  return {
    valid: true,
    message: '昵称格式正确'
  }
}
