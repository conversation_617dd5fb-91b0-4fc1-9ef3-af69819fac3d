@echo off
chcp 65001 >nul
echo 🎯 Gitee仓库初始化脚本
echo ================================

REM 检查Git是否安装
echo 🔍 检查Git安装状态...
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到Git
    echo 请先安装Git：
    echo 1. 访问 https://gitee.com/all-about-git
    echo 2. 或访问 https://mirrors.huaweicloud.com/git-for-windows/
    echo 3. 下载并安装最新版本
    pause
    exit /b 1
)
echo ✅ Git已安装

REM 配置Git用户信息
echo.
echo 📝 配置Git用户信息...
set /p username="请输入您的Gitee用户名: "
set /p email="请输入您的Gitee邮箱: "

git config --global user.name "%username%"
git config --global user.email "%email%"
echo ✅ 用户信息配置完成

REM 初始化Git仓库
echo.
echo 🚀 初始化Git仓库...
git init
echo ✅ Git仓库初始化完成

REM 添加远程仓库
echo.
echo 🔗 添加Gitee远程仓库...
set /p repo_name="请输入仓库名称 (默认: HealthApp): "
if "%repo_name%"=="" set repo_name=HealthApp

git remote add origin https://gitee.com/ablezz/%repo_name%.git
echo ✅ 远程仓库添加完成

REM 首次提交
echo.
echo 📦 准备首次提交...
git add .
git commit -m "初始提交：健康助手小程序项目"
echo ✅ 代码已提交到本地仓库

REM 推送到Gitee
echo.
echo 📤 推送到Gitee...
git push -u origin master
if %errorlevel% equ 0 (
    echo ✅ 项目已成功上传到Gitee!
    echo 🌐 访问地址: https://gitee.com/ablezz/%repo_name%
) else (
    echo ❌ 推送失败，可能的原因：
    echo 1. 网络连接问题
    echo 2. 仓库不存在或无权限
    echo 3. 需要先在Gitee创建仓库
    echo.
    echo 💡 解决方案：
    echo 1. 确保已在Gitee创建仓库: https://gitee.com/ablezz
    echo 2. 检查仓库名称是否正确
    echo 3. 确保网络连接正常
)

echo.
echo 🎉 初始化完成！
echo 💡 后续使用 auto-commit.bat 进行快速提交
pause
