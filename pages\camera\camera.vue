<template>
	<!--
		📷 相机页面模板
		这是应用的相机功能页面，用户可以拍照并发送给医生
		包含：相机预览、拍照控制、闪光灯、前后摄像头切换等功能
	-->

	<!--
		📄 相机页面容器
		- class="camera-container": 基础相机页面样式
		- :class: 动态样式类，包括字体大小和字体类型
		- :key: 强制重新渲染的键，当字体变化时重新渲染
	-->
	<view class="camera-container" :class="[fontSizeClass, fontClass]" :key="fontSizeUpdateKey">
		<!--
			📹 相机预览区域
			这是uni-app的camera组件，用于显示摄像头画面
			- class="camera-preview": 相机预览样式
			- :device-position: 摄像头位置（前置/后置）
			- :flash: 闪光灯模式（开/关/自动）
			- @error: 相机出错时的回调函数
			- @initdone: 相机初始化完成时的回调函数
		-->
		<camera
			class="camera-preview"
			:device-position="devicePosition"
			:flash="flashMode"
			@error="onCameraError"
			@initdone="onCameraInit">
		</camera>

		<!--
			🔝 顶部控制栏
			包含返回按钮、页面标题、帮助按钮
			:style: 动态样式，用于适配不同设备的状态栏高度
		-->
		<view class="top-controls" :style="topControlsStyle">
			<!--
				⬅️ 左上角返回按钮
				@tap: 点击时返回上一页
			-->
			<view class="control-button back-button" @tap="goBack">
				<view class="button-circle-small">
					<!-- 返回箭头图标 -->
					<view class="icon-back"></view>
				</view>
			</view>

			<!--
				📝 页面标题
				显示"相机"文字，支持多语言
			-->
			<text class="page-title ug">{{ $t('camera.title') }}</text>

			<!--
				❓ 右上角帮助按钮
				@tap: 点击时显示帮助信息
			-->
			<view class="control-button help-button" @tap="showHelp">
				<view class="button-circle-small">
					<!-- 帮助问号图标 -->
					<view class="icon-help">?</view>
				</view>
			</view>
		</view>

		<!--
			🔄 镜头反转按钮
			独立定位在胶囊按钮左侧，用于切换前后摄像头
			:style: 动态样式，用于精确定位
			@tap: 点击时切换前后摄像头
		-->
		<view class="flip-button-beside-capsule" :style="flipButtonStyle" @tap="flipCamera">
			<view class="button-circle-small">
				<!--
					相机切换图标
					src: 相机图标的图片路径
					mode="aspectFit": 图片适应模式，保持宽高比
				-->
				<image class="camera-icon" src="/static/icon/cameraIcon.png" mode="aspectFit"></image>
			</view>
		</view>

		<!--
			🔽 底部控制栏
			包含手电筒、拍照按钮、相册等功能
		-->
		<view class="bottom-controls">
			<!--
				🔦 左下角手电筒按钮
				@tap: 点击时切换手电筒开关
			-->
			<view class="control-button torch-button" @tap="toggleTorch">
				<!--
					手电筒按钮圆圈
					:class: 当手电筒开启时添加active样式
				-->
				<view class="button-circle" :class="{ active: torchOn }">
					<!--
						手电筒图标
						:src: 根据手电筒状态显示不同的图标
						- 开启时：显示亮起的闪电图标
						- 关闭时：显示熄灭的闪电图标
					-->
					<image
						class="torch-icon"
						:src="torchOn ? '/static/icon/TurnOnLightningIcon.png' : '/static/icon/TurnOffLightningIcon.png'"
						mode="aspectFit">
					</image>
				</view>
			</view>

			<!--
				📸 中间拍照按钮
				这是主要的拍照按钮，用户点击后会拍摄照片
				@tap: 点击时执行拍照功能
			-->
			<view class="capture-button" @tap="takePhoto">
				<view class="capture-inner">
					<!-- 拍照按钮内部图标 -->
					<view class="capture-icon"></view>
				</view>
			</view>

			<!--
				🖼️ 右下角相册按钮
				用户可以点击从相册选择照片
				@tap: 点击时打开相册选择照片
			-->
			<view class="control-button album-button" @tap="chooseFromAlbum">
				<view class="button-circle">
					<!--
						相册图标
						src: 相册图标的图片路径
					-->
					<image class="album-icon" src="/static/icon/albumIcon.png" mode="aspectFit"></image>
				</view>
			</view>
		</view>

		<!--
			❓ 帮助弹窗
			v-if="showHelpModal": 只有当显示帮助时才渲染
			@tap="hideHelp": 点击遮罩层时关闭帮助弹窗
		-->
		<view v-if="showHelpModal" class="help-modal" @tap="hideHelp">
			<!--
				帮助内容区域
				@tap.stop: 阻止事件冒泡，防止点击内容区域时关闭弹窗
			-->
			<view class="help-content" @tap.stop>
				<!-- 帮助弹窗头部 -->
				<view class="help-header">
					<!-- 帮助标题 -->
					<text class="help-title">相机功能说明</text>
					<!--
						关闭按钮
						@tap: 点击时关闭帮助弹窗
					-->
					<view class="close-btn" @tap="hideHelp">×</view>
				</view>

				<!-- 帮助项目列表 -->
				<view class="help-list">
					<!--
						🔄 循环显示帮助项目
						v-for: 遍历帮助项目数组
						:key: 每个帮助项目的唯一标识
					-->
					<view
						v-for="helpItem in helpItems"
						:key="helpItem.key"
						class="help-item"
					>
						<!-- 帮助项目图标 -->
						<text class="help-icon">{{ helpItem.icon }}</text>
						<!-- 帮助项目文字，支持多语言 -->
						<text class="help-text">{{ $t(helpItem.textKey) }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<!--
	🔧 JavaScript 逻辑部分
	使用 Vue 2 的 Options API 语法
-->
<script>
// ==================== 📦 导入依赖 ====================

// 状态管理导入
import { useAppStore } from '@/store/app.js'  // 应用全局状态
// 工具函数导入
import { useFontSizePage } from '@/utils/fontSizeMixin.js'  // 字体大小控制
import { useRTLPage } from '@/utils/rtlMixin.js'  // RTL（从右到左）布局支持
import { t } from '@/locale/index.js'  // 国际化翻译函数

export default {
	// 混入（Mixins）：将可复用的功能混入到组件中
	mixins: [useFontSizePage(), useRTLPage()],

	// ==================== 📊 组件数据 ====================
	data() {
		return {
			// 摄像头位置：'back'（后置）或 'front'（前置）
			devicePosition: 'back',
			flashMode: 'off', // 'off', 'on', 'auto', 'torch'
			fontSizeUpdateKey: 0,
			torchOn: false,
			cameraContext: null,
			showHelpModal: false,
			// 胶囊按钮位置信息
			menuButtonInfo: {
				top: 0,
				height: 32,
				left: 0,
				width: 87
			},
			// 测试功能相关
			lastCapturedImage: '', // 最后拍摄的照片
			testMode: false // 是否为测试模式
		}
	},

	computed: {
		// 字体大小类名
		fontSizeClass() {
			const appStore = useAppStore()
			return `font-size-${appStore.fontSize}`
		},

		// 字体类名
		fontClass() {
			const appStore = useAppStore()
			return {
				'ug': appStore.isUyghur,
				[`lang-${appStore.lang}`]: true
			}
		},

		// 帮助项配置数组
		helpItems() {
			return [
				{
					key: 'back',
					icon: '↩️',
					textKey: 'camera.help.back'
				},
				{
					key: 'flip',
					icon: '🔄',
					textKey: 'camera.help.flip'
				},
				{
					key: 'torch',
					icon: '🔦',
					textKey: 'camera.help.torch'
				},
				{
					key: 'capture',
					icon: '📷',
					textKey: 'camera.help.capture'
				},
				{
					key: 'album',
					icon: '🖼',
					textKey: 'camera.help.album'
				}
			]
		},

		// 计算顶部控制栏的样式
		topControlsStyle() {
			const menuButton = this.menuButtonInfo
			return {
				top: `${menuButton.top}px`,
				height: `${menuButton.height}px`,
				paddingTop: '',
				paddingBottom: ''
			}
		},

		// 计算反转按钮的位置样式（在胶囊按钮左边）
		flipButtonStyle() {
			const menuButton = this.menuButtonInfo
			const buttonWidth = 32 // 按钮宽度（px）
			const spacing = 8 // 与胶囊按钮的间距（px）

			// 计算按钮应该距离屏幕右边的距离
			const systemInfo = uni.getSystemInfoSync()
			const screenWidth = systemInfo.screenWidth || 375
			const rightDistance = screenWidth - menuButton.left + spacing

			return {
				position: 'absolute',
				top: `${menuButton.top}px`,
				right: `${rightDistance}px`,
				width: `${buttonWidth}px`,
				height: `${menuButton.height}px`,
				zIndex: 11
			}
		}
	},

	onLoad() {
		console.log('相机页面加载')

		// 获取胶囊按钮位置信息
		this.getMenuButtonInfo()

		// 监听字体大小变化
		uni.$on('fontSizeChanged', this.handleFontSizeChange)

		// 延迟初始化相机上下文，确保相机组件已渲染
		this.$nextTick(() => {
			setTimeout(() => {
				this.initCamera()
			}, 500)
		})
	},
	
	onShow() {
		// 页面显示时的处理
	},
	
	onHide() {
		// 页面隐藏时关闭手电筒
		if (this.torchOn) {
			this.flashMode = 'off'
			this.torchOn = false
		}
		// 隐藏帮助弹窗
		this.showHelpModal = false
	},

	onUnload() {
		// 移除字体大小变化监听
		uni.$off('fontSizeChanged', this.handleFontSizeChange)
	},
	
	methods: {
		// 翻译方法
		$t(key) {
			return t(key)
		},

		// 字体大小变化处理
		handleFontSizeChange(data) {
			console.log('Camera页面接收到字体大小变化:', data.fontSize)
			this.fontSizeUpdateKey++
		},

		// 显示帮助
		showHelp() {
			this.showHelpModal = true
		},

		// 隐藏帮助
		hideHelp() {
			this.showHelpModal = false
		},

		// 获取胶囊按钮位置信息
		getMenuButtonInfo() {
			// #ifdef MP-WEIXIN
			try {
				const rect = uni.getMenuButtonBoundingClientRect()
				this.menuButtonInfo = {
					top: rect.top,
					height: rect.height,
					left: rect.left,
					width: rect.width
				}
				console.log('胶囊按钮位置信息:', this.menuButtonInfo)
			} catch (error) {
				console.error('获取胶囊按钮信息失败:', error)
			}
			// #endif

			// #ifndef MP-WEIXIN
			// 非微信小程序环境，设置默认值
			const systemInfo = uni.getSystemInfoSync()
			const statusBarHeight = systemInfo.statusBarHeight || 44
			this.menuButtonInfo = {
				top: statusBarHeight + 6,
				height: 32,
				left: systemInfo.screenWidth - 87 - 10,
				width: 87
			}
			// #endif
		},

		// 初始化相机
		initCamera() {
			console.log('初始化相机上下文')
			try {
				this.cameraContext = uni.createCameraContext()
				console.log('相机上下文创建成功')
			} catch (error) {
				console.error('相机上下文创建失败', error)
				uni.showModal({
					title: '相机初始化失败',
					content: '无法初始化相机，请检查设备是否支持相机功能',
					showCancel: false,
					confirmText: '确定',
					success: () => {
						this.goBack()
					}
				})
			}
		},

		// 返回上一页
		goBack() {
			uni.navigateBack({
				delta: 1
			})
		},
		
		// 反转摄像头
		flipCamera() {
			this.devicePosition = this.devicePosition === 'back' ? 'front' : 'back'
			uni.showToast({
				title: `已切换到${this.devicePosition === 'back' ? '后置' : '前置'}摄像头`,
				icon: 'none',
				duration: 1500
			})
		},

		// 切换手电筒
		toggleTorch() {
			if (this.devicePosition === 'front') {
				uni.showToast({
					title: '前置摄像头不支持闪光灯',
					icon: 'none'
				})
				return
			}
			
			this.torchOn = !this.torchOn
			this.flashMode = this.torchOn ? 'torch' : 'off'
			
			uni.showToast({
				title: this.torchOn ? '手电筒已开启' : '手电筒已关闭',
				icon: 'none',
				duration: 1500
			})
		},
		
		// 拍照
		takePhoto() {
			console.log('开始拍照...')

			// 检查相机上下文
			if (!this.cameraContext) {
				console.log('重新创建相机上下文')
				this.cameraContext = uni.createCameraContext()
			}

			// 执行拍照
			this.performTakePhoto()
		},

		// 执行拍照
		performTakePhoto() {
			console.log('开始执行拍照，相机上下文', this.cameraContext)

			this.cameraContext.takePhoto({
				quality: 'high',
				success: (res) => {
					console.log('拍照成功:', res.tempImagePath)

					// 显示拍照成功提示
					uni.showToast({
						title: '拍照成功',
						icon: 'success',
						duration: 1500
					})

					// 处理拍照结果
					this.handlePhotoResult(res.tempImagePath)
				},
				fail: (err) => {
					console.error('拍照失败:', err)

					// 详细的错误处理
					let errorMessage = '拍照失败，请重试'
					if (err.errMsg) {
						if (err.errMsg.includes('permission')) {
							errorMessage = '相机权限不足，请检查权限设置'
						} else if (err.errMsg.includes('busy')) {
							errorMessage = '相机正忙，请稍后重试'
						} else if (err.errMsg.includes('not ready')) {
							errorMessage = '相机未就绪，请稍后重试'
						} else {
							errorMessage = `拍照失败: ${err.errMsg}`
						}
					}

					uni.showToast({
						title: errorMessage,
						icon: 'none',
						duration: 3000
					})
				}
			})
		},

		// 从相册选择照片
		chooseFromAlbum() {
			uni.chooseImage({
				count: 1,
				sizeType: ['original', 'compressed'],
				sourceType: ['album'],
				success: (res) => {
					const tempFilePath = res.tempFilePaths[0]
					console.log('选择照片:', tempFilePath)

					uni.showToast({
						title: '照片选择成功',
						icon: 'success',
						duration: 1500
					})

					// 处理选择的照片
					this.handlePhotoResult(tempFilePath)
				},
				fail: (err) => {
					console.error('选择照片失败:', err)
					if (err.errMsg !== 'chooseImage:fail cancel') {
						uni.showToast({
							title: '选择照片失败',
							icon: 'none'
						})
					}
				}
			})
		},

		// 处理照片结果（拍照或选择）
		handlePhotoResult(imagePath) {
			// 保存照片路径到本地存储和组件数据
			uni.setStorageSync('lastCapturedImage', imagePath)
			this.lastCapturedImage = imagePath

			// 检查上一页是否有处理相机结果的方法
			const pages = getCurrentPages()
			const prevPage = pages[pages.length - 2]

			// 如果是从camera-test页面来的，启用测试模式
			if (prevPage && prevPage.route && prevPage.route.includes('camera-test')) {
				this.testMode = true
				// 调用测试页面的处理方法
				if (typeof prevPage.handleCameraResult === 'function') {
					prevPage.handleCameraResult(imagePath)
				}
				uni.navigateBack()
			} else if (prevPage && typeof prevPage.handleCameraResult === 'function') {
				// 如果上一页有处理相机结果的方法，直接调用
				prevPage.handleCameraResult(imagePath)
				uni.navigateBack()
			} else {
				// 否则跳转到图片选择页面
				uni.navigateTo({
					url: `/pages/photo-select/photo-select?imagePath=${encodeURIComponent(imagePath)}`
				})
			}
		},

		// 相机初始化完成
		onCameraInit() {
			console.log('相机初始化完成')
			// 重新创建相机上下文，确保可以正常拍照
			this.cameraContext = uni.createCameraContext()
		},

		// 相机错误处理
		onCameraError(error) {
			console.error('相机错误:', error)

			let errorMessage = '相机启动失败'
			if (error.errMsg) {
				if (error.errMsg.includes('permission')) {
					errorMessage = '相机权限被拒绝，请在设置中开启相机权限'
				} else if (error.errMsg.includes('busy')) {
					errorMessage = '相机正在被其他应用使用，请稍后重试'
				} else {
					errorMessage = `相机错误: ${error.errMsg}`
				}
			}

			uni.showModal({
				title: '相机启动失败',
				content: errorMessage,
				showCancel: true,
				cancelText: '返回',
				confirmText: '重试',
				success: (res) => {
					if (res.confirm) {
						// 重试初始化相机
						setTimeout(() => {
							this.initCamera()
						}, 1000)
					} else {
						this.goBack()
					}
				}
			})
		},

		// 添加测试功能 - 保存图片到相册
		saveImageToAlbum(imagePath) {
			if (!imagePath) {
				uni.showToast({
					title: '没有可保存的照片',
					icon: 'none'
				})
				return
			}

			uni.saveImageToPhotosAlbum({
				filePath: imagePath,
				success: () => {
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					})
				},
				fail: (error) => {
					console.error('保存失败:', error)
					uni.showToast({
						title: '保存失败',
						icon: 'none'
					})
				}
			})
		},

		// 添加测试功能 - 预览图片
		previewImage(imagePath) {
			if (imagePath) {
				uni.previewImage({
					urls: [imagePath],
					current: imagePath
				})
			}
		}
	}
}
</script>

<style scoped>
/* 相机容器 */
.camera-container {
	position: relative;
	width: 100vw;
	height: 100vh;
	background: #000;
	overflow: hidden;
}

/* 相机预览 */
.camera-preview {
	width: 100%;
	height: 100%;
}

/* 顶部控制栏 */
.top-controls {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 20rpx;
	z-index: 10;
	background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), transparent);
}

/* 页面标题 */
.page-title {
	color: white;
	font-size: 32rpx;
	font-weight: 500;
	text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.5);
}

/* 控制按钮 */
.control-button {
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 12;
}

/* 小圆形按钮 */
.button-circle-small {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	background: rgba(0, 0, 0, 0.6);
	backdrop-filter: blur(10rpx);
	border: 2rpx solid rgba(255, 255, 255, 0.3);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
}

.button-circle-small:active {
	transform: scale(0.95);
	background: rgba(0, 0, 0, 0.8);
}

/* 返回图标 */
.icon-back {
	width: 0;
	height: 0;
	border-top: 8rpx solid transparent;
	border-bottom: 8rpx solid transparent;
	border-right: 12rpx solid white;
	margin-left: -2rpx;
}

/* 帮助图标 */
.icon-help {
	color: white;
	font-size: 28rpx;
	font-weight: bold;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
}

/* 相机图标 */
.camera-icon {
	width: 32rpx;
	height: 32rpx;
	filter: brightness(0) invert(1);
}

/* 镜头反转按钮 - 独立定位 */
.flip-button-beside-capsule {
	display: flex;
	align-items: center;
	justify-content: center;
}

/* 底部控制栏 */
.bottom-controls {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 200rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 60rpx 60rpx;
	background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
	z-index: 10;
}

/* 圆形按钮 */
.button-circle {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	backdrop-filter: blur(10rpx);
	border: 3rpx solid rgba(255, 255, 255, 0.4);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.button-circle:active {
	transform: scale(0.95);
}

.button-circle.active {
	background: rgba(255, 193, 7, 0.8);
	border-color: rgba(255, 193, 7, 1);
	box-shadow: 0 0 20rpx rgba(255, 193, 7, 0.6);
}

/* 手电筒图标 */
.torch-icon {
	width: 48rpx;
	height: 48rpx;
	filter: brightness(0) invert(1);
}

.button-circle.active .torch-icon {
	filter: brightness(0);
}

/* 拍照按钮 */
.capture-button {
	width: 160rpx;
	height: 160rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.9);
	border: 6rpx solid rgba(255, 255, 255, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.2s ease;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
}

.capture-button:active {
	transform: scale(0.95);
	background: rgba(255, 255, 255, 0.8);
}

.capture-inner {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: white;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.capture-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: #007AFF;
	box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

/* 相册图标 */
.album-icon {
	width: 48rpx;
	height: 48rpx;
	filter: brightness(0) invert(1);
}

/* 帮助弹窗 */
.help-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.7);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	backdrop-filter: blur(5rpx);
}

.help-content {
	background: white;
	border-radius: 24rpx;
	width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.help-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.help-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
}

.close-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: #f5f5f5;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 40rpx;
	color: #666;
	cursor: pointer;
}

.close-btn:active {
	background: #e0e0e0;
}

.help-list {
	padding: 20rpx 40rpx 40rpx;
}

.help-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f8f8f8;
}

.help-item:last-child {
	border-bottom: none;
}

.help-icon {
	font-size: 32rpx;
	margin-right: 24rpx;
	width: 60rpx;
	text-align: center;
}

.help-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}

/* 字体大小适配 */
.font-size-small .page-title {
	font-size: 28rpx;
}

.font-size-small .help-title {
	font-size: 32rpx;
}

.font-size-small .help-text {
	font-size: 24rpx;
}

.font-size-large .page-title {
	font-size: 36rpx;
}

.font-size-large .help-title {
	font-size: 40rpx;
}

.font-size-large .help-text {
	font-size: 32rpx;
}

.font-size-extra-large .page-title {
	font-size: 40rpx;
}

.font-size-extra-large .help-title {
	font-size: 44rpx;
}

.font-size-extra-large .help-text {
	font-size: 36rpx;
}
</style>


