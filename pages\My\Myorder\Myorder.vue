<template>
	<view class="page-container" :class="[fontSizeClass, fontClass]" :key="fontSizeUpdateKey">
		<!-- Tab栏：订单状态切换栏 -->
		<view class="tabs-container">
			<fui-tabs
				:tabs="tabs"                    <!-- 传入tab标签数组 -->
				:current="currentTab"           <!-- 当前选中的tab索引 -->
				:selectedColor="'#00C853'"      <!-- 选中状态的文字颜色（绿色） -->
				:sliderBackground="'#00C853'"   <!-- 底部滑块的背景色（绿色） -->
				:height="96"                    <!-- tab栏高��?-->
				:size="28"                      <!-- 未选中状态的字体大小 -->
				:selectedSize="28"              <!-- 选中状态的字体大小 -->
				:selectedFontWeight="500"       <!-- 选中状态的字体粗细 -->
				:isSlider="true"                <!-- 是否显示底部滑块 -->
				:sliderHeight="3"               <!-- 滑块高度 -->
				:short="true"                   <!-- 滑块是否为短样式 -->
				:center="true"                  <!-- 滑块是否居中显示 -->
				:padding="0"                    <!-- tab项的内边��?-->
				background="#ffffff"            <!-- tab栏背景色 -->
				@change="onTabChange"           <!-- tab切换事件 -->
			></fui-tabs>
		</view>

		<!-- 内容区域：根据选中的tab显示对应的订单列��?-->
		<view class="content-container">
			<!-- 全部订单 (索引0) -->
			<view v-if="currentTab === 0" class="tab-content">
				<!-- TODO: 这里接入API获取全部订单数据 -->
				<!-- API接口: GET /api/orders?status=all -->
				<view v-if="allOrders.length === 0" class="empty-container">
					<view class="empty-icon">
						<view class="order-icon">
							<view class="icon-bg">
								<view class="order-list-icon">
									<view class="icon-line"></view>
									<view class="icon-line"></view>
									<view class="icon-line"></view>
								</view>
							</view>
						</view>
					</view>
					<text class="empty-text">{{ $t('myOrder.empty.noOrders') }}</text>
				</view>
				<!-- 有订单数据时显示订单列表 -->
				<view v-else class="order-list">
					<!-- 这里循环显示订单��?-->
					<!-- <view v-for="order in allOrders" :key="order.id" class="order-item">
						订单内容
					</view> -->
				</view>
			</view>

			<!-- 待支付订��?(索引1) -->
			<view v-else-if="currentTab === 1" class="tab-content">
				<!-- TODO: 这里接入API获取待支付订单数��?-->
				<!-- API接口: GET /api/orders?status=pending_payment -->
				<view v-if="pendingPaymentOrders.length === 0" class="empty-container">
					<view class="empty-icon">
						<view class="order-icon">
							<view class="icon-bg">
								<view class="order-list-icon">
									<view class="icon-line"></view>
									<view class="icon-line"></view>
									<view class="icon-line"></view>
								</view>
							</view>
						</view>
					</view>
					<text class="empty-text">{{ $t('myOrder.empty.noPendingPayment') }}</text>
				</view>
			</view>

			<!-- 待发货订��?(索引2) -->
			<view v-else-if="currentTab === 2" class="tab-content">
				<!-- TODO: 这里接入API获取待发货订单数��?-->
				<!-- API接口: GET /api/orders?status=pending_shipment -->
				<view v-if="pendingShipmentOrders.length === 0" class="empty-container">
					<view class="empty-icon">
						<view class="order-icon">
							<view class="icon-bg">
								<view class="order-list-icon">
									<view class="icon-line"></view>
									<view class="icon-line"></view>
									<view class="icon-line"></view>
								</view>
							</view>
						</view>
					</view>
					<text class="empty-text">{{ $t('myOrder.empty.noPendingShipment') }}</text>
				</view>
			</view>

			<!-- 已发货订��?(索引3) -->
			<view v-else-if="currentTab === 3" class="tab-content">
				<!-- TODO: 这里接入API获取已发货订单数��?-->
				<!-- API接口: GET /api/orders?status=shipped -->
				<view v-if="shippedOrders.length === 0" class="empty-container">
					<view class="empty-icon">
						<view class="order-icon">
							<view class="icon-bg">
								<view class="order-list-icon">
									<view class="icon-line"></view>
									<view class="icon-line"></view>
									<view class="icon-line"></view>
								</view>
							</view>
						</view>
					</view>
					<text class="empty-text">{{ $t('myOrder.empty.noShipped') }}</text>
				</view>
			</view>

			<!-- 已完成订��?(索引4) -->
			<view v-else-if="currentTab === 4" class="tab-content">
				<!-- TODO: 这里接入API获取已完成订单数��?-->
				<!-- API接口: GET /api/orders?status=completed -->
				<view v-if="completedOrders.length === 0" class="empty-container">
					<view class="empty-icon">
						<view class="order-icon">
							<view class="icon-bg">
								<view class="order-list-icon">
									<view class="icon-line"></view>
									<view class="icon-line"></view>
									<view class="icon-line"></view>
								</view>
							</view>
						</view>
					</view>
					<text class="empty-text">{{ $t('myOrder.empty.noCompleted') }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
/**
 * 导入Vue的响应式API
 * ref: 创建响应式数��?
 * onMounted: 组件挂载后执行的生命周期钩子
 */
import { ref, computed, onMounted } from 'vue'
import { useFontSizePage } from '@/utils/fontSizeMixin.js'
import { useAppStore } from '@/store/app.js'
import { t } from '@/locale/index.js'

// 应用状态管理
const appStore = useAppStore()

// 计算字体类
const fontClass = computed(() => ({
	'ug': appStore.isUyghur,
	[`lang-${appStore.lang}`]: true
}))

// 翻译方法
const $t = (key) => t(key)

// ==================== 数据定义 ====================

/**
 * Tab标签数组：定义订单状态的5个选项
 * 对应索引：0-全部, 1-待支付, 2-待发货, 3-已发货, 4-已完成
 */
const tabs = computed(() => [
	$t('myOrder.tabs.all'),
	$t('myOrder.tabs.pendingPayment'),
	$t('myOrder.tabs.pendingShipment'),
	$t('myOrder.tabs.shipped'),
	$t('myOrder.tabs.completed')
])

/**
 * 当前选中的tab索引
 * 默认选中第一个tab（全部订单）
 */
const currentTab = ref(0)

// 使用字体大小功能
const { fontSizeClass, fontSizeUpdateKey } = useFontSizePage()

// ==================== 订单数据 ====================
// TODO: 这些数据需要通过API接口获取，目前为空数组用于演��?

/**
 * 全部订单数据
 * API接口: GET /api/orders?status=all
 */
const allOrders = ref([])

/**
 * 待支付订单数��?
 * API接口: GET /api/orders?status=pending_payment
 */
const pendingPaymentOrders = ref([])

/**
 * 待发货订单数��?
 * API接口: GET /api/orders?status=pending_shipment
 */
const pendingShipmentOrders = ref([])

/**
 * 已发货订单数��?
 * API接口: GET /api/orders?status=shipped
 */
const shippedOrders = ref([])

/**
 * 已完成订单数��?
 * API接口: GET /api/orders?status=completed
 */
const completedOrders = ref([])

// ==================== 方法定义 ====================

/**
 * Tab切换事件处理函数
 * @param {Object} e - 事件对象，包含index（索引）和name（标签名��?
 */
const onTabChange = (e) => {
	// 更新当前选中的tab索引
	currentTab.value = e.index

	// 打印调试信息（生产环境可删除��?
	console.log('当前选中tab:', e.index, e.name)

	// TODO: 根据选中的tab加载对应的订单数��?
	loadOrdersByTab(e.index)
}

/**
 * 根据tab索引加载对应的订单数��?
 * @param {Number} tabIndex - tab索引
 */
const loadOrdersByTab = async (tabIndex) => {
	try {
		// TODO: 根据不同的tab调用不同的API接口
		switch (tabIndex) {
			case 0: // 全部订单
				await loadAllOrders()
				break
			case 1: // 待支付订��?
				await loadPendingPaymentOrders()
				break
			case 2: // 待发货订��?
				await loadPendingShipmentOrders()
				break
			case 3: // 已发货订��?
				await loadShippedOrders()
				break
			case 4: // 已完成订��?
				await loadCompletedOrders()
				break
		}
	} catch (error) {
		console.error('加载订单数据失败:', error)
		// TODO: 显示错误提示给用��?
		uni.showToast({
			title: '加载失败，请重试',
			icon: 'none'
		})
	}
}

// ==================== API接口方法 ====================
// TODO: 以下方法需要根据实际的API接口进行实现

/**
 * 获取全部订单数据
 * API接口: GET /api/orders?status=all
 */
const loadAllOrders = async () => {
	try {
		// TODO: 替换为实际的API接口地址
		// const response = await uni.request({
		//     url: 'https://your-api-domain.com/api/orders',
		//     method: 'GET',
		//     data: {
		//         status: 'all',
		//         page: 1,
		//         limit: 20
		//     },
		//     header: {
		//         'Authorization': 'Bearer ' + getToken() // 如果需要token
		//     }
		// })
		//
		// if (response.statusCode === 200) {
		//     allOrders.value = response.data.data || []
		// }

		console.log('加载全部订单数据')
	} catch (error) {
		console.error('获取全部订单失败:', error)
		throw error
	}
}

/**
 * 获取待支付订单数��?
 * API接口: GET /api/orders?status=pending_payment
 */
const loadPendingPaymentOrders = async () => {
	try {
		// TODO: 替换为实际的API接口地址
		// const response = await uni.request({
		//     url: 'https://your-api-domain.com/api/orders',
		//     method: 'GET',
		//     data: {
		//         status: 'pending_payment',
		//         page: 1,
		//         limit: 20
		//     }
		// })
		//
		// if (response.statusCode === 200) {
		//     pendingPaymentOrders.value = response.data.data || []
		// }

		console.log('加载待支付订单数')
	} catch (error) {
		console.error('获取待支付订单失', error)
		throw error
	}
}

/**
 * 获取待发货订单数��?
 * API接口: GET /api/orders?status=pending_shipment
 */
const loadPendingShipmentOrders = async () => {
	try {
		// TODO: 替换为实际的API接口地址
		console.log('加载待发货订单数')
	} catch (error) {
		console.error('获取待发货订单失', error)
		throw error
	}
}

/**
 * 获取已发货订单数��?
 * API接口: GET /api/orders?status=shipped
 */
const loadShippedOrders = async () => {
	try {
		// TODO: 替换为实际的API接口地址
		console.log('加载已发货订单数')
	} catch (error) {
		console.error('获取已发货订单失', error)
		throw error
	}
}

/**
 * 获取已完成订单数��?
 * API接口: GET /api/orders?status=completed
 */
const loadCompletedOrders = async () => {
	try {
		// TODO: 替换为实际的API接口地址
		console.log('加载已完成订单数')
	} catch (error) {
		console.error('获取已完成订单失', error)
		throw error
	}
}

// ==================== 生命周期 ====================

/**
 * 组件挂载后执��?
 * 页面加载完成后自动加载默认tab的数��?
 */
onMounted(() => {
	// 加载默认选中tab的订单数��?
	loadOrdersByTab(currentTab.value)
})
</script>

<style lang="scss" scoped>
/* ==================== 页面容器样式 ==================== */

/**
 * 页面主容��?
 * 设置最小高度为全屏，背景色为浅灰色
 */
.page-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	position: relative;
}

/* ==================== 返回按钮样式 ==================== */

/**
 * 返回按钮容器
 * 固定定位在左上角
 */
.back-button {
	position: absolute;
	top: 20rpx;
	left: 20rpx;
	z-index: 999;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

/**
 * 返回按钮图标
 * 左箭头样��?
 */
.back-icon {
	font-size: 32rpx;
	color: #333333;
}

/* ==================== Tab栏样��?==================== */

/**
 * Tab栏容��?
 * 白色背景，底部带分割��?
 */
.tabs-container {
	background-color: #ffffff;
	border-bottom:  solid #f0f0f0;
}

/* ==================== 内容区域样式 ==================== */

/**
 * 内容容器
 * 占据剩余空间，居中对齐
 */
.content-container {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: calc(100vh - 200rpx);
}

/**
 * Tab内容容器
 * 每个tab对应的内容区��?
 */
.tab-content {
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
}

/* ==================== 空状态样��?==================== */

/**
 * 空状态容��?
 * 垂直居中布局，显示空状态图标和文字
 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 200rpx 0;
}

/**
 * 空状态图标容��?
 */
.empty-icon {
	margin-bottom: 40rpx;
}

/**
 * 订单图标容器
 */
.order-icon {
	display: flex;
	align-items: center;
	justify-content: center;
}

/**
 * 图标背景
 * 圆角矩形背景，浅灰色
 */
.icon-bg {
	width: 160rpx;
	height: 160rpx;
	background-color: #f8f8f8;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 2rpx solid #e8e8e8;
	position: relative;
}

/**
 * 订单列表图标
 * 模拟订单列表的样��?
 */
.order-list-icon {
	width: 80rpx;
	height: 80rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
}

/**
 * 图标线条
 * 模拟订单列表的横��?
 */
.icon-line {
	width: 60rpx;
	height: 8rpx;
	background-color: #cccccc;
	border-radius: 4rpx;
}

/**
 * 第一条线条样��?
 * 稍短一些，模拟标题
 */
.icon-line:first-child {
	width: 40rpx;
}

/**
 * 空状态文��?
 * 灰色文字，提示用户当前状��?
 */
.empty-text {
	font-size: 32rpx;
	color: #999999;
	font-weight: 400;
}

/* ==================== 订单列表样式 ==================== */
/* TODO: 当有实际订单数据时，可以添加以下样式 */

/**
 * 订单列表容器
 * 用于显示实际的订单列��?
 */
.order-list {
	width: 100%;
	padding: 20rpx;
}

/**
 * 单个订单项样��?
 * 白色背景，圆角，阴影效果
 */
.order-item {
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 字体大小响应式样式 */
.font-size-small {
	.empty-text {
		font-size: 26rpx !important;
	}

	.order-item-title {
		font-size: 26rpx !important;
	}

	.order-status {
		font-size: 24rpx !important;
	}

	.order-price {
		font-size: 28rpx !important;
	}

	.order-time {
		font-size: 22rpx !important;
	}

	.order-number {
		font-size: 22rpx !important;
	}

	.order-product-name {
		font-size: 24rpx !important;
	}

	.order-quantity {
		font-size: 22rpx !important;
	}

	.order-total {
		font-size: 26rpx !important;
	}
}

.font-size-medium {
	.empty-text {
		font-size: 30rpx !important;
	}

	.order-item-title {
		font-size: 30rpx !important;
	}

	.order-status {
		font-size: 28rpx !important;
	}

	.order-price {
		font-size: 32rpx !important;
	}

	.order-time {
		font-size: 26rpx !important;
	}

	.order-number {
		font-size: 26rpx !important;
	}

	.order-product-name {
		font-size: 28rpx !important;
	}

	.order-quantity {
		font-size: 26rpx !important;
	}

	.order-total {
		font-size: 30rpx !important;
	}
}

.font-size-large {
	.empty-text {
		font-size: 34rpx !important;
	}

	.order-item-title {
		font-size: 34rpx !important;
	}

	.order-status {
		font-size: 32rpx !important;
	}

	.order-price {
		font-size: 36rpx !important;
	}

	.order-time {
		font-size: 30rpx !important;
	}

	.order-number {
		font-size: 30rpx !important;
	}

	.order-product-name {
		font-size: 32rpx !important;
	}

	.order-quantity {
		font-size: 30rpx !important;
	}

	.order-total {
		font-size: 34rpx !important;
	}
}
</style>
