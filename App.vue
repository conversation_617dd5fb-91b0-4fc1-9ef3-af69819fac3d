<script setup>
	import {
		useAppStore
	} from '@/store/app.js'
	// 移除了自动登录相关的import，所有登录逻辑都在组件中处理
	import {
		onLaunch,
		onShow
	} from '@dcloudio/uni-app';
	import{wechat_Authenticator} from'@/request/index.js'
	import {FILE_URL} from '@/main.js'
	
	const appStores=useAppStore()
	
	onLaunch(() => {
		wxLogin()
	});
	onShow(() => {});


	// 静默登录
	const wxLogin = () => {
		uni.login({
			provider: 'weixin',
			success: (res) => {
				if (res.code) {
					wechat_Authenticator(res.code, 1).then((result) => {
						if (result.code === 200) {
							const {
								session_key,
								auth
							} = result.data
							const user_info=result.data;
							if (user_info.avatar){
								user_info['avatar']=`${FILE_URL}${user_info.avatar}`
							}
							 uni.setStorageSync('auth_token', session_key)
							 uni.setStorageSync('auth', auth)
							 appStores.setUserInfo(user_info)
						}
					})
				}
			},
			fail: (err) => {
				console.error('静默登录失败:', err)
	
			}
		})
	}

	// export default {
	// 	onLaunch: function() {

	// 		wxLogin();

	// 		// console.log('🚀 ===== App Launch - 应用启动 =====');

	// 		// // 初始化应用设置
	// 		// try {
	// 		// 	const appStore = useAppStore()

	// 		// 	// 初始化字体大小
	// 		// 	appStore.initFontSize()

	// 		// 	// 初始化语言设置
	// 		// 	appStore.initLanguage()

	// 		// 	// 初始化小程序字体管理器
	// 		// 	// #ifdef MP-WEIXIN
	// 		// 	try {
	// 		// 		const { initMiniProgramFontManager } = require('@/utils/miniProgramFontManager.js')
	// 		// 		initMiniProgramFontManager()
	// 		// 	} catch (error) {
	// 		// 		console.warn('初始化小程序字体管理器失败:', error)
	// 		// 	}
	// 		// 	// #endif

	// 		// 	// 初始化全局国际化管理器
	// 		// 	try {
	// 		// 		const { initGlobalI18n } = require('@/plugins/i18n.js')
	// 		// 		initGlobalI18n()
	// 		// 	} catch (error) {
	// 		// 		console.warn('初始化全局国际化管理器失败:', error)
	// 		// 	}

	// 		// 	// 初始化全局RTL支持
	// 		// 	try {
	// 		// 		const { initGlobalRTL, setupPageRTL } = require('@/utils/globalRTL.js')
	// 		// 		initGlobalRTL()
	// 		// 		setupPageRTL()
	// 		// 	} catch (error) {
	// 		// 		console.warn('初始化全局RTL支持失败:', error)
	// 		// 	}

	// 		// 	// 监听字体大小变化事件
	// 		// 	uni.$on('fontSizeChanged', (data) => {
	// 		// 		console.log('字体大小已切换:', data.fontSize)
	// 		// 	})

	// 		// 	// 不进行自动登录，用户需要手动点击登录
	// 		// 	console.log('ℹ️ 自动登录已禁用，用户需要手动登录');

	// 		// 	// 监听语言变化事件
	// 		// 	uni.$on('languageChanged', (data) => {
	// 		// 		console.log('语言已切换:', data.language, '是否为维吾尔文:', data.isUyghur)

	// 		// 		// 应用全局字体设置
	// 		// 		this.applyGlobalLanguageFont(data.language, data.isUyghur)
	// 		// 	})

	// 		// 	// 监听语言字体变化事件
	// 		// 	uni.$on('languageFontChanged', (data) => {
	// 		// 		console.log('字体已切换:', data.language, '是否为维吾尔文:', data.isUyghur)
	// 		// 	})
	// 		// } catch (error) {
	// 		// 	console.error('应用初始化失败:', error)
	// 		// }
	// 	},
	// 	onShow: function() {
	// 		console.log('👁️ ===== App Show - 应用显示 =====');
	// 		// 不在onShow时自动登录，避免过于频繁
	// 		// 如果需要自动登录，可以在onLaunch中处理
	// 	},
	// 	onHide: function() {
	// 		console.log('App Hide')
	// 	},

	// 	methods: {
	// 		// 应用全局语言字体
	// 		applyGlobalLanguageFont(language, isUyghur) {
	// 			try {
	// 				// 获取页面根元素并设置语言类
	// 				const pages = getCurrentPages()
	// 				if (pages.length > 0) {
	// 					const currentPage = pages[pages.length - 1]

	// 					// 设置页面的语言类
	// 					if (currentPage && currentPage.$vm) {
	// 						const vm = currentPage.$vm

	// 						// 通过页面实例设置class
	// 						this.setPageLanguageClass(vm, language, isUyghur)
	// 					}
	// 				}
	// 			} catch (error) {
	// 				console.warn('应用全局语言字体失败:', error)
	// 			}
	// 		},

	// 		// 设置页面语言类
	// 		setPageLanguageClass(vm, language, isUyghur) {
	// 			try {
	// 				// 在页面根元素上设置语言类
	// 				const query = uni.createSelectorQuery().in(vm)
	// 				query.select('page').exec((res) => {
	// 					if (res && res[0]) {
	// 						// 移除之前的语言类
	// 						const supportedLanguages = ['zh-CN', 'en', 'ug']
	// 						const languageClasses = supportedLanguages.map(lang => `lang-${lang}`)
	// 						languageClasses.forEach(cls => {
	// 							if (res[0].classList && res[0].classList.contains(cls)) {
	// 								res[0].classList.remove(cls)
	// 							}
	// 						})

	// 						// 添加当前语言类
	// 						const langClass = `lang-${language}`
	// 						if (res[0].classList) {
	// 							res[0].classList.add(langClass)
	// 						}

	// 						// 如果是维吾尔文，添加特殊类
	// 						if (isUyghur && res[0].classList) {
	// 							res[0].classList.add('ug-page')
	// 						}
	// 					}
	// 				})
	// 			} catch (error) {
	// 				console.warn('设置页面语言类失败:', error)
	// 			}
	// 		}
	// 	}
	// }

	// 自动登录功能已移除



	const requestSessionKey = (code) => {
		console.log('获取的code', code)
		wechat_Authenticator(code, 1).then((res) => {
			if (res.code === 200) {
				userStore.setUserInfo(res)
				const {
					token
				} = res.data
				// uni.setStorageSync('auth_token', token)
			}
		})
	}
</script>

<style lang="scss">
	/* 引入字体大小样式 */
	@import '@/styles/fontSize.scss';


	/* 引入你的 Material Icons 字体 */
	@font-face {
		font-family: 'MaterialIcons';
		/* 我们为这个字体命名为 MaterialIcons */
		src: url('~@/static/fonts/MaterialIcons-Regular.otf') format('opentype');
	}


	/* 维吾尔文字体样式 */
	.ug {
		font-family: 'uy' !important;
	}

	/* 当语言为维吾尔文时，全局应用维吾尔文字体 */
	.lang-ug,
	.ug-page {
		font-family: 'uy' !important;

		/* 确保所有文本元素都应用维吾尔文字体，但排除图标元素 */
		text:not(.fui-icon):not([class*="icon"]):not([class*="Icon"]),
		view:not(.fui-icon):not([class*="icon"]):not([class*="Icon"]),
		button:not(.fui-icon):not([class*="icon"]):not([class*="Icon"]),
		input,
		textarea,
		label,
		.uni-input-input,
		.uni-textarea-textarea,
		.uni-button-text,
		.popup-title,
		.language-text,
		.btn-text,
		.setting-value,
		.user-name,
		.user-phone,
		.setting-title,
		.setting-desc {
			font-family: 'uy' !important;
		}

		/* 弹出框中的维吾尔文字体 */
		.popup-container.ug,
		.language-popup.ug {
			font-family: 'uy' !important;

			.popup-title,
			.language-text,
			.btn-text {
				font-family: 'uy' !important;
			}
		}

		/* 设置页面中的维吾尔文字体 */
		.settings-container.ug {
			font-family: 'uy' !important;

			.setting-value,
			.setting-title,
			.setting-desc,
			.user-name,
			.user-phone {
				font-family: 'uy' !important;
			}
		}
	}

	/* 页面级别的维吾尔文字体应用 */
	page.lang-ug {
		font-family: 'uy' !important;

		/* 常见元素的维吾尔文字体，但排除图标元素 */
		text:not(.fui-icon):not([class*="icon"]):not([class*="Icon"]),
		view:not(.fui-icon):not([class*="icon"]):not([class*="Icon"]),
		button:not(.fui-icon):not([class*="icon"]):not([class*="Icon"]),
		input,
		textarea,
		label,
		image,
		.uni-input-input,
		.uni-textarea-textarea,
		.uni-button-text,
		.uni-text,
		.uni-view,
		.uni-button,
		.uni-input,
		.uni-textarea {
			font-family: 'uy' !important;
		}
	}

	/* ==================== 图标字体保护规则 ==================== */
	/* 确保图标字体不被维吾尔文字体覆盖 */
	.fui-icon,
	[class*="icon"],
	[class*="Icon"],
	.icon,
	.iconfont {
		font-family: fuiFont, "iconfont", "Material Icons", "Font Awesome", sans-serif !important;
	}

	/* FirstUI图标特殊保护 */
	.fui-icon {
		font-family: fuiFont !important;
		font-style: normal !important;
		font-variant: normal !important;
		text-transform: none !important;
		line-height: 1 !important;
		-webkit-font-smoothing: antialiased !important;
		-moz-osx-font-smoothing: grayscale !important;
	}


	/* 创建一个通用的图标样式类 */
	.material-icon {
		font-family: 'MaterialIcons';
		font-weight: normal;
		font-style: normal;
		font-size: 24px;
		/* 你可以自定义默认大小 */
		display: inline-block;
		line-height: 1;
		text-transform: none;
		letter-spacing: normal;
		word-wrap: normal;
		white-space: nowrap;
		direction: ltr;

		/* 抗锯齿，让图标更平滑 */
		-webkit-font-smoothing: antialiased;
		text-rendering: optimizeLegibility;
		-moz-osx-font-smoothing: grayscale;
		font-feature-settings: 'liga';
	}

	/* 全局主题变量 */
	page {
		/* 浅色主题（默认） */
		--fui-color-primary: #465CFF;
		--fui-color-success: #09BE4F;
		--fui-color-warning: #FFB703;
		--fui-color-danger: #FF2B2B;
		--fui-color-purple: #6831FF;

		/* 文字颜色 */
		--fui-color-title: #181818;
		--fui-color-section: #333333;
		--fui-color-subtitle: #7F7F7F;
		--fui-color-label: #B2B2B2;
		--fui-color-minor: #CCCCCC;
		--fui-color-white: #FFFFFF;
		--fui-color-link: #465CFF;

		/* 背景颜色 */
		--fui-bg-color: #ffffff;
		--fui-bg-color-grey: #F1F4FA;
		--fui-bg-color-content: #F8F8F8;
		--fui-bg-color-red: rgba(255, 43, 43, .05);
		--fui-bg-color-yellow: rgba(255, 183, 3, .1);
		--fui-bg-color-purple: rgba(104, 49, 255, .05);
		--fui-bg-color-green: rgba(9, 190, 79, .05);
		--fui-bg-color-hover: rgba(0, 0, 0, 0.2);
		--fui-bg-color-mask: rgba(0, 0, 0, 0.6);

		/* 边框颜色 */
		--fui-color-border: #EEEEEE;

		/* 阴影颜色 */
		--fui-color-shadow: rgba(2, 4, 38, 0.05);

		/* 禁用态的透明度 */
		--fui-opacity-disabled: 0.5;

		/* 全局字体大小变量（默认中等大小） */
		--font-size-xs: 24rpx;
		/* 12px - 最小字体 */
		--font-size-sm: 28rpx;
		/* 14px - 小字体 */
		--font-size-base: 30rpx;
		/* 15px - 基础字体 */
		--font-size-md: 32rpx;
		/* 16px - 中等字体 */
		--font-size-lg: 36rpx;
		/* 18px - 大字体 */
		--font-size-xl: 40rpx;
		/* 20px - 超大字体 */
		--font-size-2xl: 44rpx;
		/* 22px - 标题字体 */
		--font-size-3xl: 48rpx;
		/* 24px - 大标题 */
		--font-size-4xl: 56rpx;
		/* 28px - 超大标题 */
	}

	/* 小字体全局样式 */
	.font-size-small {
		--font-size-xs: 20rpx;
		/* 10px */
		--font-size-sm: 24rpx;
		/* 12px */
		--font-size-base: 26rpx;
		/* 13px */
		--font-size-md: 28rpx;
		/* 14px */
		--font-size-lg: 30rpx;
		/* 15px */
		--font-size-xl: 32rpx;
		/* 16px */
		--font-size-2xl: 36rpx;
		/* 18px */
		--font-size-3xl: 40rpx;
		/* 20px */
		--font-size-4xl: 48rpx;
		/* 24px */
	}

	/* 中字体全局样式（默认） */
	.font-size-medium {
		--font-size-xs: 24rpx;
		/* 12px */
		--font-size-sm: 28rpx;
		/* 14px */
		--font-size-base: 30rpx;
		/* 15px */
		--font-size-md: 32rpx;
		/* 16px */
		--font-size-lg: 36rpx;
		/* 18px */
		--font-size-xl: 40rpx;
		/* 20px */
		--font-size-2xl: 44rpx;
		/* 22px */
		--font-size-3xl: 48rpx;
		/* 24px */
		--font-size-4xl: 56rpx;
		/* 28px */
	}

	/* 大字体全局样式 */
	.font-size-large {
		--font-size-xs: 28rpx;
		/* 14px */
		--font-size-sm: 32rpx;
		/* 16px */
		--font-size-base: 34rpx;
		/* 17px */
		--font-size-md: 36rpx;
		/* 18px */
		--font-size-lg: 40rpx;
		/* 20px */
		--font-size-xl: 44rpx;
		/* 22px */
		--font-size-2xl: 48rpx;
		/* 24px */
		--font-size-3xl: 52rpx;
		/* 26px */
		--font-size-4xl: 60rpx;
		/* 30px */
	}





	/* 全局页面背景色 */
	page {
		background-color: var(--fui-bg-color-grey);
		color: var(--fui-color-section);
	}

	/* ===== RTL 支持样式 ===== */
	/* 维吾尔文 RTL 布局 */
	.ug {
		direction: rtl;
		text-align: right;
	}

	/* 中文和英文 LTR 布局 */
	.lang-zh-CN,
	.lang-en {
		direction: ltr;
		text-align: left;
	}

	/* RTL 布局下的通用样式调整 */
	.ug .flex-row {
		flex-direction: row-reverse;
	}

	.ug .flex-row-reverse {
		flex-direction: row;
	}

	/* RTL 布局下的边距调整 */
	.ug .margin-left {
		margin-left: 0;
		margin-right: var(--margin-value, 16rpx);
	}

	.ug .margin-right {
		margin-right: 0;
		margin-left: var(--margin-value, 16rpx);
	}

	.ug .padding-left {
		padding-left: 0;
		padding-right: var(--padding-value, 16rpx);
	}

	.ug .padding-right {
		padding-right: 0;
		padding-left: var(--padding-value, 16rpx);
	}

	/* RTL 布局下的浮动调整 */
	.ug .float-left {
		float: right;
	}

	.ug .float-right {
		float: left;
	}

	/* RTL 布局下的定位调整 */
	.ug .left-0 {
		left: auto;
		right: 0;
	}

	.ug .right-0 {
		right: auto;
		left: 0;
	}

	/* RTL 布局下的边框调整 */
	.ug .border-left {
		border-left: none;
		border-right: var(--border-style, 1rpx solid #eee);
	}

	.ug .border-right {
		border-right: none;
		border-left: var(--border-style, 1rpx solid #eee);
	}

	/* RTL 布局下的图标调整 */
	.ug .icon-arrow-right::before {
		content: '←';
	}

	.ug .icon-arrow-left::before {
		content: '→';
	}

	/* RTL 布局下的FirstUI图标调整 */
	.ug fui-icon[name="arrowright"] {
		transform: scaleX(-1);
	}

	.ug fui-icon[name="arrowleft"] {
		transform: scaleX(-1);
	}

	/* 更通用的FirstUI图标翻转 */
	.ug .fui-icon[class*="arrowright"] {
		transform: scaleX(-1);
	}

	.ug .fui-icon[class*="arrowleft"] {
		transform: scaleX(-1);
	}

	/* RTL 布局下的输入框调整 */
	.ug input,
	.ug textarea {
		text-align: right;
		direction: rtl;
	}

	/* RTL 布局下的列表项调整 */
	.ug .list-item {
		flex-direction: row-reverse !important;
	}

	.ug .list-item .icon {
		margin-left: 16rpx;
		margin-right: 0;
	}

	/* RTL 布局下的My页面列表项调整 */
	.ug .item-icon {
		margin-left: 30rpx;
		margin-right: 0;
	}

	/* RTL 布局下的导航栏调整 */
	.ug .navbar {
		flex-direction: row-reverse;
	}

	.ug .navbar .back-btn {
		order: 3;
	}

	.ug .navbar .title {
		order: 2;
	}

	.ug .navbar .right-btn {
		order: 1;
	}

	/* RTL 布局下的卡片调整 */
	.ug .card-header {
		flex-direction: row-reverse;
	}

	.ug .card-content {
		text-align: right;
	}

	/* RTL 布局下的表单调整 */
	.ug .form-item {
		flex-direction: row-reverse;
	}

	.ug .form-label {
		text-align: right;
		margin-left: 16rpx;
		margin-right: 0;
	}

	/* RTL 布局下的按钮组调整 */
	.ug .btn-group {
		flex-direction: row-reverse;
	}

	.ug .btn-group .btn:first-child {
		border-radius: 0 8rpx 8rpx 0;
	}

	.ug .btn-group .btn:last-child {
		border-radius: 8rpx 0 0 8rpx;
	}

	/* RTL 布局下的标签页调整 */
	.ug .tabs {
		flex-direction: row-reverse;
	}

	/* RTL 布局下的步骤条调整 */
	.ug .steps {
		flex-direction: row-reverse;
	}

	.ug .step::after {
		left: auto;
		right: 100%;
	}

	/* RTL 布局下的下拉菜单调整 */
	.ug .dropdown {
		left: auto;
		right: 0;
	}

	/* RTL 布局下的弹窗调整 */
	.ug .modal-content {
		text-align: right;
	}

	.ug .modal-footer {
		flex-direction: row-reverse;
	}

	/* RTL 布局下的消息提示调整 */
	.ug .toast {
		text-align: right;
	}

	/* RTL 布局下的进度条调整 */
	.ug .progress-bar {
		transform: scaleX(-1);
	}

	.ug .progress-text {
		transform: scaleX(-1);
	}
</style>